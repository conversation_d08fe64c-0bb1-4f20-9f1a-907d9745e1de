var CE=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var _N=CE((kN,Gf)=>{function Yf(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const o=Object.getOwnPropertyDescriptor(r,i);o&&Object.defineProperty(e,i,o.get?o:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var et=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ma(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var $v={exports:{}},Ol={},qv={exports:{}},de={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fa=Symbol.for("react.element"),OE=Symbol.for("react.portal"),NE=Symbol.for("react.fragment"),RE=Symbol.for("react.strict_mode"),DE=Symbol.for("react.profiler"),IE=Symbol.for("react.provider"),PE=Symbol.for("react.context"),AE=Symbol.for("react.forward_ref"),LE=Symbol.for("react.suspense"),ME=Symbol.for("react.memo"),FE=Symbol.for("react.lazy"),Bh=Symbol.iterator;function jE(e){return e===null||typeof e!="object"?null:(e=Bh&&e[Bh]||e["@@iterator"],typeof e=="function"?e:null)}var Hv={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Wv=Object.assign,Gv={};function lo(e,t,n){this.props=e,this.context=t,this.refs=Gv,this.updater=n||Hv}lo.prototype.isReactComponent={};lo.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};lo.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Yv(){}Yv.prototype=lo.prototype;function Xf(e,t,n){this.props=e,this.context=t,this.refs=Gv,this.updater=n||Hv}var Kf=Xf.prototype=new Yv;Kf.constructor=Xf;Wv(Kf,lo.prototype);Kf.isPureReactComponent=!0;var Qh=Array.isArray,Xv=Object.prototype.hasOwnProperty,Jf={current:null},Kv={key:!0,ref:!0,__self:!0,__source:!0};function Jv(e,t,n){var r,i={},o=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(o=""+t.key),t)Xv.call(t,r)&&!Kv.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(s===1)i.children=n;else if(1<s){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)i[r]===void 0&&(i[r]=s[r]);return{$$typeof:Fa,type:e,key:o,ref:a,props:i,_owner:Jf.current}}function zE(e,t){return{$$typeof:Fa,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Zf(e){return typeof e=="object"&&e!==null&&e.$$typeof===Fa}function UE(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var $h=/\/+/g;function yu(e,t){return typeof e=="object"&&e!==null&&e.key!=null?UE(""+e.key):t.toString(36)}function Ns(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(o){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case Fa:case OE:a=!0}}if(a)return a=e,i=i(a),e=r===""?"."+yu(a,0):r,Qh(i)?(n="",e!=null&&(n=e.replace($h,"$&/")+"/"),Ns(i,t,n,"",function(u){return u})):i!=null&&(Zf(i)&&(i=zE(i,n+(!i.key||a&&a.key===i.key?"":(""+i.key).replace($h,"$&/")+"/")+e)),t.push(i)),1;if(a=0,r=r===""?".":r+":",Qh(e))for(var s=0;s<e.length;s++){o=e[s];var l=r+yu(o,s);a+=Ns(o,t,n,l,i)}else if(l=jE(e),typeof l=="function")for(e=l.call(e),s=0;!(o=e.next()).done;)o=o.value,l=r+yu(o,s++),a+=Ns(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Za(e,t,n){if(e==null)return e;var r=[],i=0;return Ns(e,r,"","",function(o){return t.call(n,o,i++)}),r}function VE(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xt={current:null},Rs={transition:null},BE={ReactCurrentDispatcher:xt,ReactCurrentBatchConfig:Rs,ReactCurrentOwner:Jf};function Zv(){throw Error("act(...) is not supported in production builds of React.")}de.Children={map:Za,forEach:function(e,t,n){Za(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Za(e,function(){t++}),t},toArray:function(e){return Za(e,function(t){return t})||[]},only:function(e){if(!Zf(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};de.Component=lo;de.Fragment=NE;de.Profiler=DE;de.PureComponent=Xf;de.StrictMode=RE;de.Suspense=LE;de.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=BE;de.act=Zv;de.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Wv({},e.props),i=e.key,o=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,a=Jf.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(l in t)Xv.call(t,l)&&!Kv.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&s!==void 0?s[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){s=Array(l);for(var u=0;u<l;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:Fa,type:e.type,key:i,ref:o,props:r,_owner:a}};de.createContext=function(e){return e={$$typeof:PE,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:IE,_context:e},e.Consumer=e};de.createElement=Jv;de.createFactory=function(e){var t=Jv.bind(null,e);return t.type=e,t};de.createRef=function(){return{current:null}};de.forwardRef=function(e){return{$$typeof:AE,render:e}};de.isValidElement=Zf;de.lazy=function(e){return{$$typeof:FE,_payload:{_status:-1,_result:e},_init:VE}};de.memo=function(e,t){return{$$typeof:ME,type:e,compare:t===void 0?null:t}};de.startTransition=function(e){var t=Rs.transition;Rs.transition={};try{e()}finally{Rs.transition=t}};de.unstable_act=Zv;de.useCallback=function(e,t){return xt.current.useCallback(e,t)};de.useContext=function(e){return xt.current.useContext(e)};de.useDebugValue=function(){};de.useDeferredValue=function(e){return xt.current.useDeferredValue(e)};de.useEffect=function(e,t){return xt.current.useEffect(e,t)};de.useId=function(){return xt.current.useId()};de.useImperativeHandle=function(e,t,n){return xt.current.useImperativeHandle(e,t,n)};de.useInsertionEffect=function(e,t){return xt.current.useInsertionEffect(e,t)};de.useLayoutEffect=function(e,t){return xt.current.useLayoutEffect(e,t)};de.useMemo=function(e,t){return xt.current.useMemo(e,t)};de.useReducer=function(e,t,n){return xt.current.useReducer(e,t,n)};de.useRef=function(e){return xt.current.useRef(e)};de.useState=function(e){return xt.current.useState(e)};de.useSyncExternalStore=function(e,t,n){return xt.current.useSyncExternalStore(e,t,n)};de.useTransition=function(){return xt.current.useTransition()};de.version="18.3.1";qv.exports=de;var N=qv.exports;const xe=Ma(N),QE=Yf({__proto__:null,default:xe},[N]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $E=N,qE=Symbol.for("react.element"),HE=Symbol.for("react.fragment"),WE=Object.prototype.hasOwnProperty,GE=$E.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,YE={key:!0,ref:!0,__self:!0,__source:!0};function ey(e,t,n){var r,i={},o=null,a=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)WE.call(t,r)&&!YE.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:qE,type:e,key:o,ref:a,props:i,_owner:GE.current}}Ol.Fragment=HE;Ol.jsx=ey;Ol.jsxs=ey;$v.exports=Ol;var R=$v.exports,mc={},ty={exports:{}},Qt={},ny={exports:{}},ry={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(M,V){var U=M.length;M.push(V);e:for(;0<U;){var ae=U-1>>>1,q=M[ae];if(0<i(q,V))M[ae]=V,M[U]=q,U=ae;else break e}}function n(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var V=M[0],U=M.pop();if(U!==V){M[0]=U;e:for(var ae=0,q=M.length,Ee=q>>>1;ae<Ee;){var te=2*(ae+1)-1,Se=M[te],Ce=te+1,qe=M[Ce];if(0>i(Se,U))Ce<q&&0>i(qe,Se)?(M[ae]=qe,M[Ce]=U,ae=Ce):(M[ae]=Se,M[te]=U,ae=te);else if(Ce<q&&0>i(qe,U))M[ae]=qe,M[Ce]=U,ae=Ce;else break e}}return V}function i(M,V){var U=M.sortIndex-V.sortIndex;return U!==0?U:M.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var a=Date,s=a.now();e.unstable_now=function(){return a.now()-s}}var l=[],u=[],c=1,f=null,d=3,y=!1,g=!1,v=!1,E=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(M){for(var V=n(u);V!==null;){if(V.callback===null)r(u);else if(V.startTime<=M)r(u),V.sortIndex=V.expirationTime,t(l,V);else break;V=n(u)}}function S(M){if(v=!1,p(M),!g)if(n(l)!==null)g=!0,rt(T);else{var V=n(u);V!==null&&Fe(S,V.startTime-M)}}function T(M,V){g=!1,v&&(v=!1,h(k),k=-1),y=!0;var U=d;try{for(p(V),f=n(l);f!==null&&(!(f.expirationTime>V)||M&&!K());){var ae=f.callback;if(typeof ae=="function"){f.callback=null,d=f.priorityLevel;var q=ae(f.expirationTime<=V);V=e.unstable_now(),typeof q=="function"?f.callback=q:f===n(l)&&r(l),p(V)}else r(l);f=n(l)}if(f!==null)var Ee=!0;else{var te=n(u);te!==null&&Fe(S,te.startTime-V),Ee=!1}return Ee}finally{f=null,d=U,y=!1}}var O=!1,w=null,k=-1,I=5,P=-1;function K(){return!(e.unstable_now()-P<I)}function se(){if(w!==null){var M=e.unstable_now();P=M;var V=!0;try{V=w(!0,M)}finally{V?ee():(O=!1,w=null)}}else O=!1}var ee;if(typeof m=="function")ee=function(){m(se)};else if(typeof MessageChannel<"u"){var le=new MessageChannel,Ae=le.port2;le.port1.onmessage=se,ee=function(){Ae.postMessage(null)}}else ee=function(){E(se,0)};function rt(M){w=M,O||(O=!0,ee())}function Fe(M,V){k=E(function(){M(e.unstable_now())},V)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(M){M.callback=null},e.unstable_continueExecution=function(){g||y||(g=!0,rt(T))},e.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<M?Math.floor(1e3/M):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(M){switch(d){case 1:case 2:case 3:var V=3;break;default:V=d}var U=d;d=V;try{return M()}finally{d=U}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(M,V){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var U=d;d=M;try{return V()}finally{d=U}},e.unstable_scheduleCallback=function(M,V,U){var ae=e.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?ae+U:ae):U=ae,M){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=U+q,M={id:c++,callback:V,priorityLevel:M,startTime:U,expirationTime:q,sortIndex:-1},U>ae?(M.sortIndex=U,t(u,M),n(l)===null&&M===n(u)&&(v?(h(k),k=-1):v=!0,Fe(S,U-ae))):(M.sortIndex=q,t(l,M),g||y||(g=!0,rt(T))),M},e.unstable_shouldYield=K,e.unstable_wrapCallback=function(M){var V=d;return function(){var U=d;d=V;try{return M.apply(this,arguments)}finally{d=U}}}})(ry);ny.exports=ry;var XE=ny.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var KE=N,Vt=XE;function F(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var iy=new Set,la={};function fi(e,t){Xi(e,t),Xi(e+"Capture",t)}function Xi(e,t){for(la[e]=t,e=0;e<t.length;e++)iy.add(t[e])}var Qn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vc=Object.prototype.hasOwnProperty,JE=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,qh={},Hh={};function ZE(e){return vc.call(Hh,e)?!0:vc.call(qh,e)?!1:JE.test(e)?Hh[e]=!0:(qh[e]=!0,!1)}function ew(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function tw(e,t,n,r){if(t===null||typeof t>"u"||ew(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function bt(e,t,n,r,i,o,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var ft={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ft[e]=new bt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ft[t]=new bt(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ft[e]=new bt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ft[e]=new bt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ft[e]=new bt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ft[e]=new bt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ft[e]=new bt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ft[e]=new bt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ft[e]=new bt(e,5,!1,e.toLowerCase(),null,!1,!1)});var ed=/[\-:]([a-z])/g;function td(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ed,td);ft[t]=new bt(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ed,td);ft[t]=new bt(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ed,td);ft[t]=new bt(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ft[e]=new bt(e,1,!1,e.toLowerCase(),null,!1,!1)});ft.xlinkHref=new bt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ft[e]=new bt(e,1,!1,e.toLowerCase(),null,!0,!0)});function nd(e,t,n,r){var i=ft.hasOwnProperty(t)?ft[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(tw(t,n,i,r)&&(n=null),r||i===null?ZE(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Gn=KE.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,es=Symbol.for("react.element"),xi=Symbol.for("react.portal"),bi=Symbol.for("react.fragment"),rd=Symbol.for("react.strict_mode"),yc=Symbol.for("react.profiler"),oy=Symbol.for("react.provider"),ay=Symbol.for("react.context"),id=Symbol.for("react.forward_ref"),gc=Symbol.for("react.suspense"),Ec=Symbol.for("react.suspense_list"),od=Symbol.for("react.memo"),sr=Symbol.for("react.lazy"),sy=Symbol.for("react.offscreen"),Wh=Symbol.iterator;function wo(e){return e===null||typeof e!="object"?null:(e=Wh&&e[Wh]||e["@@iterator"],typeof e=="function"?e:null)}var Ve=Object.assign,gu;function zo(e){if(gu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);gu=t&&t[1]||""}return`
`+gu+e}var Eu=!1;function wu(e,t){if(!e||Eu)return"";Eu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),a=i.length-1,s=o.length-1;1<=a&&0<=s&&i[a]!==o[s];)s--;for(;1<=a&&0<=s;a--,s--)if(i[a]!==o[s]){if(a!==1||s!==1)do if(a--,s--,0>s||i[a]!==o[s]){var l=`
`+i[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=a&&0<=s);break}}}finally{Eu=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?zo(e):""}function nw(e){switch(e.tag){case 5:return zo(e.type);case 16:return zo("Lazy");case 13:return zo("Suspense");case 19:return zo("SuspenseList");case 0:case 2:case 15:return e=wu(e.type,!1),e;case 11:return e=wu(e.type.render,!1),e;case 1:return e=wu(e.type,!0),e;default:return""}}function wc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case bi:return"Fragment";case xi:return"Portal";case yc:return"Profiler";case rd:return"StrictMode";case gc:return"Suspense";case Ec:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ay:return(e.displayName||"Context")+".Consumer";case oy:return(e._context.displayName||"Context")+".Provider";case id:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case od:return t=e.displayName||null,t!==null?t:wc(e.type)||"Memo";case sr:t=e._payload,e=e._init;try{return wc(e(t))}catch{}}return null}function rw(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return wc(t);case 8:return t===rd?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function kr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ly(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function iw(e){var t=ly(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(a){r=""+a,o.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ts(e){e._valueTracker||(e._valueTracker=iw(e))}function uy(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ly(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Xs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Sc(e,t){var n=t.checked;return Ve({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Gh(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=kr(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function cy(e,t){t=t.checked,t!=null&&nd(e,"checked",t,!1)}function _c(e,t){cy(e,t);var n=kr(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Tc(e,t.type,n):t.hasOwnProperty("defaultValue")&&Tc(e,t.type,kr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Yh(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Tc(e,t,n){(t!=="number"||Xs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Uo=Array.isArray;function Vi(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+kr(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function xc(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(F(91));return Ve({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xh(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(F(92));if(Uo(n)){if(1<n.length)throw Error(F(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:kr(n)}}function fy(e,t){var n=kr(t.value),r=kr(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Kh(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function dy(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function bc(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?dy(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ns,hy=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ns=ns||document.createElement("div"),ns.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ns.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ua(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var qo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ow=["Webkit","ms","Moz","O"];Object.keys(qo).forEach(function(e){ow.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),qo[t]=qo[e]})});function py(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||qo.hasOwnProperty(e)&&qo[e]?(""+t).trim():t+"px"}function my(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=py(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var aw=Ve({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function kc(e,t){if(t){if(aw[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(F(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(F(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(F(61))}if(t.style!=null&&typeof t.style!="object")throw Error(F(62))}}function Cc(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Oc=null;function ad(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Nc=null,Bi=null,Qi=null;function Jh(e){if(e=Ua(e)){if(typeof Nc!="function")throw Error(F(280));var t=e.stateNode;t&&(t=Pl(t),Nc(e.stateNode,e.type,t))}}function vy(e){Bi?Qi?Qi.push(e):Qi=[e]:Bi=e}function yy(){if(Bi){var e=Bi,t=Qi;if(Qi=Bi=null,Jh(e),t)for(e=0;e<t.length;e++)Jh(t[e])}}function gy(e,t){return e(t)}function Ey(){}var Su=!1;function wy(e,t,n){if(Su)return e(t,n);Su=!0;try{return gy(e,t,n)}finally{Su=!1,(Bi!==null||Qi!==null)&&(Ey(),yy())}}function ca(e,t){var n=e.stateNode;if(n===null)return null;var r=Pl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(F(231,t,typeof n));return n}var Rc=!1;if(Qn)try{var So={};Object.defineProperty(So,"passive",{get:function(){Rc=!0}}),window.addEventListener("test",So,So),window.removeEventListener("test",So,So)}catch{Rc=!1}function sw(e,t,n,r,i,o,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Ho=!1,Ks=null,Js=!1,Dc=null,lw={onError:function(e){Ho=!0,Ks=e}};function uw(e,t,n,r,i,o,a,s,l){Ho=!1,Ks=null,sw.apply(lw,arguments)}function cw(e,t,n,r,i,o,a,s,l){if(uw.apply(this,arguments),Ho){if(Ho){var u=Ks;Ho=!1,Ks=null}else throw Error(F(198));Js||(Js=!0,Dc=u)}}function di(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Sy(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Zh(e){if(di(e)!==e)throw Error(F(188))}function fw(e){var t=e.alternate;if(!t){if(t=di(e),t===null)throw Error(F(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Zh(i),e;if(o===r)return Zh(i),t;o=o.sibling}throw Error(F(188))}if(n.return!==r.return)n=i,r=o;else{for(var a=!1,s=i.child;s;){if(s===n){a=!0,n=i,r=o;break}if(s===r){a=!0,r=i,n=o;break}s=s.sibling}if(!a){for(s=o.child;s;){if(s===n){a=!0,n=o,r=i;break}if(s===r){a=!0,r=o,n=i;break}s=s.sibling}if(!a)throw Error(F(189))}}if(n.alternate!==r)throw Error(F(190))}if(n.tag!==3)throw Error(F(188));return n.stateNode.current===n?e:t}function _y(e){return e=fw(e),e!==null?Ty(e):null}function Ty(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ty(e);if(t!==null)return t;e=e.sibling}return null}var xy=Vt.unstable_scheduleCallback,ep=Vt.unstable_cancelCallback,dw=Vt.unstable_shouldYield,hw=Vt.unstable_requestPaint,We=Vt.unstable_now,pw=Vt.unstable_getCurrentPriorityLevel,sd=Vt.unstable_ImmediatePriority,by=Vt.unstable_UserBlockingPriority,Zs=Vt.unstable_NormalPriority,mw=Vt.unstable_LowPriority,ky=Vt.unstable_IdlePriority,Nl=null,Tn=null;function vw(e){if(Tn&&typeof Tn.onCommitFiberRoot=="function")try{Tn.onCommitFiberRoot(Nl,e,void 0,(e.current.flags&128)===128)}catch{}}var pn=Math.clz32?Math.clz32:Ew,yw=Math.log,gw=Math.LN2;function Ew(e){return e>>>=0,e===0?32:31-(yw(e)/gw|0)|0}var rs=64,is=4194304;function Vo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function el(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,a=n&268435455;if(a!==0){var s=a&~i;s!==0?r=Vo(s):(o&=a,o!==0&&(r=Vo(o)))}else a=n&~i,a!==0?r=Vo(a):o!==0&&(r=Vo(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-pn(t),i=1<<n,r|=e[n],t&=~i;return r}function ww(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Sw(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-pn(o),s=1<<a,l=i[a];l===-1?(!(s&n)||s&r)&&(i[a]=ww(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}function Ic(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Cy(){var e=rs;return rs<<=1,!(rs&4194240)&&(rs=64),e}function _u(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ja(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-pn(t),e[t]=n}function _w(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-pn(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function ld(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pn(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var be=0;function Oy(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ny,ud,Ry,Dy,Iy,Pc=!1,os=[],yr=null,gr=null,Er=null,fa=new Map,da=new Map,dr=[],Tw="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function tp(e,t){switch(e){case"focusin":case"focusout":yr=null;break;case"dragenter":case"dragleave":gr=null;break;case"mouseover":case"mouseout":Er=null;break;case"pointerover":case"pointerout":fa.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":da.delete(t.pointerId)}}function _o(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Ua(t),t!==null&&ud(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function xw(e,t,n,r,i){switch(t){case"focusin":return yr=_o(yr,e,t,n,r,i),!0;case"dragenter":return gr=_o(gr,e,t,n,r,i),!0;case"mouseover":return Er=_o(Er,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return fa.set(o,_o(fa.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,da.set(o,_o(da.get(o)||null,e,t,n,r,i)),!0}return!1}function Py(e){var t=Wr(e.target);if(t!==null){var n=di(t);if(n!==null){if(t=n.tag,t===13){if(t=Sy(n),t!==null){e.blockedOn=t,Iy(e.priority,function(){Ry(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ds(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ac(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Oc=r,n.target.dispatchEvent(r),Oc=null}else return t=Ua(n),t!==null&&ud(t),e.blockedOn=n,!1;t.shift()}return!0}function np(e,t,n){Ds(e)&&n.delete(t)}function bw(){Pc=!1,yr!==null&&Ds(yr)&&(yr=null),gr!==null&&Ds(gr)&&(gr=null),Er!==null&&Ds(Er)&&(Er=null),fa.forEach(np),da.forEach(np)}function To(e,t){e.blockedOn===t&&(e.blockedOn=null,Pc||(Pc=!0,Vt.unstable_scheduleCallback(Vt.unstable_NormalPriority,bw)))}function ha(e){function t(i){return To(i,e)}if(0<os.length){To(os[0],e);for(var n=1;n<os.length;n++){var r=os[n];r.blockedOn===e&&(r.blockedOn=null)}}for(yr!==null&&To(yr,e),gr!==null&&To(gr,e),Er!==null&&To(Er,e),fa.forEach(t),da.forEach(t),n=0;n<dr.length;n++)r=dr[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<dr.length&&(n=dr[0],n.blockedOn===null);)Py(n),n.blockedOn===null&&dr.shift()}var $i=Gn.ReactCurrentBatchConfig,tl=!0;function kw(e,t,n,r){var i=be,o=$i.transition;$i.transition=null;try{be=1,cd(e,t,n,r)}finally{be=i,$i.transition=o}}function Cw(e,t,n,r){var i=be,o=$i.transition;$i.transition=null;try{be=4,cd(e,t,n,r)}finally{be=i,$i.transition=o}}function cd(e,t,n,r){if(tl){var i=Ac(e,t,n,r);if(i===null)Iu(e,t,r,nl,n),tp(e,r);else if(xw(i,e,t,n,r))r.stopPropagation();else if(tp(e,r),t&4&&-1<Tw.indexOf(e)){for(;i!==null;){var o=Ua(i);if(o!==null&&Ny(o),o=Ac(e,t,n,r),o===null&&Iu(e,t,r,nl,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else Iu(e,t,r,null,n)}}var nl=null;function Ac(e,t,n,r){if(nl=null,e=ad(r),e=Wr(e),e!==null)if(t=di(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Sy(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return nl=e,null}function Ay(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(pw()){case sd:return 1;case by:return 4;case Zs:case mw:return 16;case ky:return 536870912;default:return 16}default:return 16}}var pr=null,fd=null,Is=null;function Ly(){if(Is)return Is;var e,t=fd,n=t.length,r,i="value"in pr?pr.value:pr.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===i[o-r];r++);return Is=i.slice(e,1<r?1-r:void 0)}function Ps(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function as(){return!0}function rp(){return!1}function $t(e){function t(n,r,i,o,a){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=a,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(o):o[s]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?as:rp,this.isPropagationStopped=rp,this}return Ve(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=as)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=as)},persist:function(){},isPersistent:as}),t}var uo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},dd=$t(uo),za=Ve({},uo,{view:0,detail:0}),Ow=$t(za),Tu,xu,xo,Rl=Ve({},za,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==xo&&(xo&&e.type==="mousemove"?(Tu=e.screenX-xo.screenX,xu=e.screenY-xo.screenY):xu=Tu=0,xo=e),Tu)},movementY:function(e){return"movementY"in e?e.movementY:xu}}),ip=$t(Rl),Nw=Ve({},Rl,{dataTransfer:0}),Rw=$t(Nw),Dw=Ve({},za,{relatedTarget:0}),bu=$t(Dw),Iw=Ve({},uo,{animationName:0,elapsedTime:0,pseudoElement:0}),Pw=$t(Iw),Aw=Ve({},uo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Lw=$t(Aw),Mw=Ve({},uo,{data:0}),op=$t(Mw),Fw={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jw={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},zw={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Uw(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=zw[e])?!!t[e]:!1}function hd(){return Uw}var Vw=Ve({},za,{key:function(e){if(e.key){var t=Fw[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ps(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?jw[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hd,charCode:function(e){return e.type==="keypress"?Ps(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ps(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Bw=$t(Vw),Qw=Ve({},Rl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ap=$t(Qw),$w=Ve({},za,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hd}),qw=$t($w),Hw=Ve({},uo,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ww=$t(Hw),Gw=Ve({},Rl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Yw=$t(Gw),Xw=[9,13,27,32],pd=Qn&&"CompositionEvent"in window,Wo=null;Qn&&"documentMode"in document&&(Wo=document.documentMode);var Kw=Qn&&"TextEvent"in window&&!Wo,My=Qn&&(!pd||Wo&&8<Wo&&11>=Wo),sp=" ",lp=!1;function Fy(e,t){switch(e){case"keyup":return Xw.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function jy(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ki=!1;function Jw(e,t){switch(e){case"compositionend":return jy(t);case"keypress":return t.which!==32?null:(lp=!0,sp);case"textInput":return e=t.data,e===sp&&lp?null:e;default:return null}}function Zw(e,t){if(ki)return e==="compositionend"||!pd&&Fy(e,t)?(e=Ly(),Is=fd=pr=null,ki=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return My&&t.locale!=="ko"?null:t.data;default:return null}}var eS={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function up(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!eS[e.type]:t==="textarea"}function zy(e,t,n,r){vy(r),t=rl(t,"onChange"),0<t.length&&(n=new dd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Go=null,pa=null;function tS(e){Xy(e,0)}function Dl(e){var t=Ni(e);if(uy(t))return e}function nS(e,t){if(e==="change")return t}var Uy=!1;if(Qn){var ku;if(Qn){var Cu="oninput"in document;if(!Cu){var cp=document.createElement("div");cp.setAttribute("oninput","return;"),Cu=typeof cp.oninput=="function"}ku=Cu}else ku=!1;Uy=ku&&(!document.documentMode||9<document.documentMode)}function fp(){Go&&(Go.detachEvent("onpropertychange",Vy),pa=Go=null)}function Vy(e){if(e.propertyName==="value"&&Dl(pa)){var t=[];zy(t,pa,e,ad(e)),wy(tS,t)}}function rS(e,t,n){e==="focusin"?(fp(),Go=t,pa=n,Go.attachEvent("onpropertychange",Vy)):e==="focusout"&&fp()}function iS(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Dl(pa)}function oS(e,t){if(e==="click")return Dl(t)}function aS(e,t){if(e==="input"||e==="change")return Dl(t)}function sS(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yn=typeof Object.is=="function"?Object.is:sS;function ma(e,t){if(yn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!vc.call(t,i)||!yn(e[i],t[i]))return!1}return!0}function dp(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function hp(e,t){var n=dp(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=dp(n)}}function By(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?By(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Qy(){for(var e=window,t=Xs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Xs(e.document)}return t}function md(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function lS(e){var t=Qy(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&By(n.ownerDocument.documentElement,n)){if(r!==null&&md(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=hp(n,o);var a=hp(n,r);i&&a&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var uS=Qn&&"documentMode"in document&&11>=document.documentMode,Ci=null,Lc=null,Yo=null,Mc=!1;function pp(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Mc||Ci==null||Ci!==Xs(r)||(r=Ci,"selectionStart"in r&&md(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Yo&&ma(Yo,r)||(Yo=r,r=rl(Lc,"onSelect"),0<r.length&&(t=new dd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Ci)))}function ss(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Oi={animationend:ss("Animation","AnimationEnd"),animationiteration:ss("Animation","AnimationIteration"),animationstart:ss("Animation","AnimationStart"),transitionend:ss("Transition","TransitionEnd")},Ou={},$y={};Qn&&($y=document.createElement("div").style,"AnimationEvent"in window||(delete Oi.animationend.animation,delete Oi.animationiteration.animation,delete Oi.animationstart.animation),"TransitionEvent"in window||delete Oi.transitionend.transition);function Il(e){if(Ou[e])return Ou[e];if(!Oi[e])return e;var t=Oi[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in $y)return Ou[e]=t[n];return e}var qy=Il("animationend"),Hy=Il("animationiteration"),Wy=Il("animationstart"),Gy=Il("transitionend"),Yy=new Map,mp="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Pr(e,t){Yy.set(e,t),fi(t,[e])}for(var Nu=0;Nu<mp.length;Nu++){var Ru=mp[Nu],cS=Ru.toLowerCase(),fS=Ru[0].toUpperCase()+Ru.slice(1);Pr(cS,"on"+fS)}Pr(qy,"onAnimationEnd");Pr(Hy,"onAnimationIteration");Pr(Wy,"onAnimationStart");Pr("dblclick","onDoubleClick");Pr("focusin","onFocus");Pr("focusout","onBlur");Pr(Gy,"onTransitionEnd");Xi("onMouseEnter",["mouseout","mouseover"]);Xi("onMouseLeave",["mouseout","mouseover"]);Xi("onPointerEnter",["pointerout","pointerover"]);Xi("onPointerLeave",["pointerout","pointerover"]);fi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));fi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));fi("onBeforeInput",["compositionend","keypress","textInput","paste"]);fi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));fi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));fi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Bo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),dS=new Set("cancel close invalid load scroll toggle".split(" ").concat(Bo));function vp(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,cw(r,t,void 0,e),e.currentTarget=null}function Xy(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var a=r.length-1;0<=a;a--){var s=r[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&i.isPropagationStopped())break e;vp(i,s,u),o=l}else for(a=0;a<r.length;a++){if(s=r[a],l=s.instance,u=s.currentTarget,s=s.listener,l!==o&&i.isPropagationStopped())break e;vp(i,s,u),o=l}}}if(Js)throw e=Dc,Js=!1,Dc=null,e}function Ie(e,t){var n=t[Vc];n===void 0&&(n=t[Vc]=new Set);var r=e+"__bubble";n.has(r)||(Ky(t,e,2,!1),n.add(r))}function Du(e,t,n){var r=0;t&&(r|=4),Ky(n,e,r,t)}var ls="_reactListening"+Math.random().toString(36).slice(2);function va(e){if(!e[ls]){e[ls]=!0,iy.forEach(function(n){n!=="selectionchange"&&(dS.has(n)||Du(n,!1,e),Du(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ls]||(t[ls]=!0,Du("selectionchange",!1,t))}}function Ky(e,t,n,r){switch(Ay(t)){case 1:var i=kw;break;case 4:i=Cw;break;default:i=cd}n=i.bind(null,t,n,e),i=void 0,!Rc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Iu(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var s=r.stateNode.containerInfo;if(s===i||s.nodeType===8&&s.parentNode===i)break;if(a===4)for(a=r.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;a=a.return}for(;s!==null;){if(a=Wr(s),a===null)return;if(l=a.tag,l===5||l===6){r=o=a;continue e}s=s.parentNode}}r=r.return}wy(function(){var u=o,c=ad(n),f=[];e:{var d=Yy.get(e);if(d!==void 0){var y=dd,g=e;switch(e){case"keypress":if(Ps(n)===0)break e;case"keydown":case"keyup":y=Bw;break;case"focusin":g="focus",y=bu;break;case"focusout":g="blur",y=bu;break;case"beforeblur":case"afterblur":y=bu;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=ip;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Rw;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=qw;break;case qy:case Hy:case Wy:y=Pw;break;case Gy:y=Ww;break;case"scroll":y=Ow;break;case"wheel":y=Yw;break;case"copy":case"cut":case"paste":y=Lw;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=ap}var v=(t&4)!==0,E=!v&&e==="scroll",h=v?d!==null?d+"Capture":null:d;v=[];for(var m=u,p;m!==null;){p=m;var S=p.stateNode;if(p.tag===5&&S!==null&&(p=S,h!==null&&(S=ca(m,h),S!=null&&v.push(ya(m,S,p)))),E)break;m=m.return}0<v.length&&(d=new y(d,g,null,n,c),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",d&&n!==Oc&&(g=n.relatedTarget||n.fromElement)&&(Wr(g)||g[$n]))break e;if((y||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,y?(g=n.relatedTarget||n.toElement,y=u,g=g?Wr(g):null,g!==null&&(E=di(g),g!==E||g.tag!==5&&g.tag!==6)&&(g=null)):(y=null,g=u),y!==g)){if(v=ip,S="onMouseLeave",h="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(v=ap,S="onPointerLeave",h="onPointerEnter",m="pointer"),E=y==null?d:Ni(y),p=g==null?d:Ni(g),d=new v(S,m+"leave",y,n,c),d.target=E,d.relatedTarget=p,S=null,Wr(c)===u&&(v=new v(h,m+"enter",g,n,c),v.target=p,v.relatedTarget=E,S=v),E=S,y&&g)t:{for(v=y,h=g,m=0,p=v;p;p=wi(p))m++;for(p=0,S=h;S;S=wi(S))p++;for(;0<m-p;)v=wi(v),m--;for(;0<p-m;)h=wi(h),p--;for(;m--;){if(v===h||h!==null&&v===h.alternate)break t;v=wi(v),h=wi(h)}v=null}else v=null;y!==null&&yp(f,d,y,v,!1),g!==null&&E!==null&&yp(f,E,g,v,!0)}}e:{if(d=u?Ni(u):window,y=d.nodeName&&d.nodeName.toLowerCase(),y==="select"||y==="input"&&d.type==="file")var T=nS;else if(up(d))if(Uy)T=aS;else{T=iS;var O=rS}else(y=d.nodeName)&&y.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(T=oS);if(T&&(T=T(e,u))){zy(f,T,n,c);break e}O&&O(e,d,u),e==="focusout"&&(O=d._wrapperState)&&O.controlled&&d.type==="number"&&Tc(d,"number",d.value)}switch(O=u?Ni(u):window,e){case"focusin":(up(O)||O.contentEditable==="true")&&(Ci=O,Lc=u,Yo=null);break;case"focusout":Yo=Lc=Ci=null;break;case"mousedown":Mc=!0;break;case"contextmenu":case"mouseup":case"dragend":Mc=!1,pp(f,n,c);break;case"selectionchange":if(uS)break;case"keydown":case"keyup":pp(f,n,c)}var w;if(pd)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else ki?Fy(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(My&&n.locale!=="ko"&&(ki||k!=="onCompositionStart"?k==="onCompositionEnd"&&ki&&(w=Ly()):(pr=c,fd="value"in pr?pr.value:pr.textContent,ki=!0)),O=rl(u,k),0<O.length&&(k=new op(k,e,null,n,c),f.push({event:k,listeners:O}),w?k.data=w:(w=jy(n),w!==null&&(k.data=w)))),(w=Kw?Jw(e,n):Zw(e,n))&&(u=rl(u,"onBeforeInput"),0<u.length&&(c=new op("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=w))}Xy(f,t)})}function ya(e,t,n){return{instance:e,listener:t,currentTarget:n}}function rl(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=ca(e,n),o!=null&&r.unshift(ya(e,o,i)),o=ca(e,t),o!=null&&r.push(ya(e,o,i))),e=e.return}return r}function wi(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function yp(e,t,n,r,i){for(var o=t._reactName,a=[];n!==null&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(l!==null&&l===r)break;s.tag===5&&u!==null&&(s=u,i?(l=ca(n,o),l!=null&&a.unshift(ya(n,l,s))):i||(l=ca(n,o),l!=null&&a.push(ya(n,l,s)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var hS=/\r\n?/g,pS=/\u0000|\uFFFD/g;function gp(e){return(typeof e=="string"?e:""+e).replace(hS,`
`).replace(pS,"")}function us(e,t,n){if(t=gp(t),gp(e)!==t&&n)throw Error(F(425))}function il(){}var Fc=null,jc=null;function zc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Uc=typeof setTimeout=="function"?setTimeout:void 0,mS=typeof clearTimeout=="function"?clearTimeout:void 0,Ep=typeof Promise=="function"?Promise:void 0,vS=typeof queueMicrotask=="function"?queueMicrotask:typeof Ep<"u"?function(e){return Ep.resolve(null).then(e).catch(yS)}:Uc;function yS(e){setTimeout(function(){throw e})}function Pu(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),ha(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);ha(t)}function wr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function wp(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var co=Math.random().toString(36).slice(2),_n="__reactFiber$"+co,ga="__reactProps$"+co,$n="__reactContainer$"+co,Vc="__reactEvents$"+co,gS="__reactListeners$"+co,ES="__reactHandles$"+co;function Wr(e){var t=e[_n];if(t)return t;for(var n=e.parentNode;n;){if(t=n[$n]||n[_n]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=wp(e);e!==null;){if(n=e[_n])return n;e=wp(e)}return t}e=n,n=e.parentNode}return null}function Ua(e){return e=e[_n]||e[$n],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Ni(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(F(33))}function Pl(e){return e[ga]||null}var Bc=[],Ri=-1;function Ar(e){return{current:e}}function Pe(e){0>Ri||(e.current=Bc[Ri],Bc[Ri]=null,Ri--)}function De(e,t){Ri++,Bc[Ri]=e.current,e.current=t}var Cr={},gt=Ar(Cr),Dt=Ar(!1),ri=Cr;function Ki(e,t){var n=e.type.contextTypes;if(!n)return Cr;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function It(e){return e=e.childContextTypes,e!=null}function ol(){Pe(Dt),Pe(gt)}function Sp(e,t,n){if(gt.current!==Cr)throw Error(F(168));De(gt,t),De(Dt,n)}function Jy(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(F(108,rw(e)||"Unknown",i));return Ve({},n,r)}function al(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Cr,ri=gt.current,De(gt,e),De(Dt,Dt.current),!0}function _p(e,t,n){var r=e.stateNode;if(!r)throw Error(F(169));n?(e=Jy(e,t,ri),r.__reactInternalMemoizedMergedChildContext=e,Pe(Dt),Pe(gt),De(gt,e)):Pe(Dt),De(Dt,n)}var Fn=null,Al=!1,Au=!1;function Zy(e){Fn===null?Fn=[e]:Fn.push(e)}function wS(e){Al=!0,Zy(e)}function Lr(){if(!Au&&Fn!==null){Au=!0;var e=0,t=be;try{var n=Fn;for(be=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Fn=null,Al=!1}catch(i){throw Fn!==null&&(Fn=Fn.slice(e+1)),xy(sd,Lr),i}finally{be=t,Au=!1}}return null}var Di=[],Ii=0,sl=null,ll=0,Wt=[],Gt=0,ii=null,zn=1,Un="";function Vr(e,t){Di[Ii++]=ll,Di[Ii++]=sl,sl=e,ll=t}function eg(e,t,n){Wt[Gt++]=zn,Wt[Gt++]=Un,Wt[Gt++]=ii,ii=e;var r=zn;e=Un;var i=32-pn(r)-1;r&=~(1<<i),n+=1;var o=32-pn(t)+i;if(30<o){var a=i-i%5;o=(r&(1<<a)-1).toString(32),r>>=a,i-=a,zn=1<<32-pn(t)+i|n<<i|r,Un=o+e}else zn=1<<o|n<<i|r,Un=e}function vd(e){e.return!==null&&(Vr(e,1),eg(e,1,0))}function yd(e){for(;e===sl;)sl=Di[--Ii],Di[Ii]=null,ll=Di[--Ii],Di[Ii]=null;for(;e===ii;)ii=Wt[--Gt],Wt[Gt]=null,Un=Wt[--Gt],Wt[Gt]=null,zn=Wt[--Gt],Wt[Gt]=null}var Ut=null,jt=null,Me=!1,dn=null;function tg(e,t){var n=Xt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Tp(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ut=e,jt=wr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ut=e,jt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=ii!==null?{id:zn,overflow:Un}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Xt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ut=e,jt=null,!0):!1;default:return!1}}function Qc(e){return(e.mode&1)!==0&&(e.flags&128)===0}function $c(e){if(Me){var t=jt;if(t){var n=t;if(!Tp(e,t)){if(Qc(e))throw Error(F(418));t=wr(n.nextSibling);var r=Ut;t&&Tp(e,t)?tg(r,n):(e.flags=e.flags&-4097|2,Me=!1,Ut=e)}}else{if(Qc(e))throw Error(F(418));e.flags=e.flags&-4097|2,Me=!1,Ut=e}}}function xp(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ut=e}function cs(e){if(e!==Ut)return!1;if(!Me)return xp(e),Me=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!zc(e.type,e.memoizedProps)),t&&(t=jt)){if(Qc(e))throw ng(),Error(F(418));for(;t;)tg(e,t),t=wr(t.nextSibling)}if(xp(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(F(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){jt=wr(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}jt=null}}else jt=Ut?wr(e.stateNode.nextSibling):null;return!0}function ng(){for(var e=jt;e;)e=wr(e.nextSibling)}function Ji(){jt=Ut=null,Me=!1}function gd(e){dn===null?dn=[e]:dn.push(e)}var SS=Gn.ReactCurrentBatchConfig;function bo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(F(309));var r=n.stateNode}if(!r)throw Error(F(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(a){var s=i.refs;a===null?delete s[o]:s[o]=a},t._stringRef=o,t)}if(typeof e!="string")throw Error(F(284));if(!n._owner)throw Error(F(290,e))}return e}function fs(e,t){throw e=Object.prototype.toString.call(t),Error(F(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bp(e){var t=e._init;return t(e._payload)}function rg(e){function t(h,m){if(e){var p=h.deletions;p===null?(h.deletions=[m],h.flags|=16):p.push(m)}}function n(h,m){if(!e)return null;for(;m!==null;)t(h,m),m=m.sibling;return null}function r(h,m){for(h=new Map;m!==null;)m.key!==null?h.set(m.key,m):h.set(m.index,m),m=m.sibling;return h}function i(h,m){return h=xr(h,m),h.index=0,h.sibling=null,h}function o(h,m,p){return h.index=p,e?(p=h.alternate,p!==null?(p=p.index,p<m?(h.flags|=2,m):p):(h.flags|=2,m)):(h.flags|=1048576,m)}function a(h){return e&&h.alternate===null&&(h.flags|=2),h}function s(h,m,p,S){return m===null||m.tag!==6?(m=Vu(p,h.mode,S),m.return=h,m):(m=i(m,p),m.return=h,m)}function l(h,m,p,S){var T=p.type;return T===bi?c(h,m,p.props.children,S,p.key):m!==null&&(m.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===sr&&bp(T)===m.type)?(S=i(m,p.props),S.ref=bo(h,m,p),S.return=h,S):(S=Us(p.type,p.key,p.props,null,h.mode,S),S.ref=bo(h,m,p),S.return=h,S)}function u(h,m,p,S){return m===null||m.tag!==4||m.stateNode.containerInfo!==p.containerInfo||m.stateNode.implementation!==p.implementation?(m=Bu(p,h.mode,S),m.return=h,m):(m=i(m,p.children||[]),m.return=h,m)}function c(h,m,p,S,T){return m===null||m.tag!==7?(m=ei(p,h.mode,S,T),m.return=h,m):(m=i(m,p),m.return=h,m)}function f(h,m,p){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Vu(""+m,h.mode,p),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case es:return p=Us(m.type,m.key,m.props,null,h.mode,p),p.ref=bo(h,null,m),p.return=h,p;case xi:return m=Bu(m,h.mode,p),m.return=h,m;case sr:var S=m._init;return f(h,S(m._payload),p)}if(Uo(m)||wo(m))return m=ei(m,h.mode,p,null),m.return=h,m;fs(h,m)}return null}function d(h,m,p,S){var T=m!==null?m.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return T!==null?null:s(h,m,""+p,S);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case es:return p.key===T?l(h,m,p,S):null;case xi:return p.key===T?u(h,m,p,S):null;case sr:return T=p._init,d(h,m,T(p._payload),S)}if(Uo(p)||wo(p))return T!==null?null:c(h,m,p,S,null);fs(h,p)}return null}function y(h,m,p,S,T){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(p)||null,s(m,h,""+S,T);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case es:return h=h.get(S.key===null?p:S.key)||null,l(m,h,S,T);case xi:return h=h.get(S.key===null?p:S.key)||null,u(m,h,S,T);case sr:var O=S._init;return y(h,m,p,O(S._payload),T)}if(Uo(S)||wo(S))return h=h.get(p)||null,c(m,h,S,T,null);fs(m,S)}return null}function g(h,m,p,S){for(var T=null,O=null,w=m,k=m=0,I=null;w!==null&&k<p.length;k++){w.index>k?(I=w,w=null):I=w.sibling;var P=d(h,w,p[k],S);if(P===null){w===null&&(w=I);break}e&&w&&P.alternate===null&&t(h,w),m=o(P,m,k),O===null?T=P:O.sibling=P,O=P,w=I}if(k===p.length)return n(h,w),Me&&Vr(h,k),T;if(w===null){for(;k<p.length;k++)w=f(h,p[k],S),w!==null&&(m=o(w,m,k),O===null?T=w:O.sibling=w,O=w);return Me&&Vr(h,k),T}for(w=r(h,w);k<p.length;k++)I=y(w,h,k,p[k],S),I!==null&&(e&&I.alternate!==null&&w.delete(I.key===null?k:I.key),m=o(I,m,k),O===null?T=I:O.sibling=I,O=I);return e&&w.forEach(function(K){return t(h,K)}),Me&&Vr(h,k),T}function v(h,m,p,S){var T=wo(p);if(typeof T!="function")throw Error(F(150));if(p=T.call(p),p==null)throw Error(F(151));for(var O=T=null,w=m,k=m=0,I=null,P=p.next();w!==null&&!P.done;k++,P=p.next()){w.index>k?(I=w,w=null):I=w.sibling;var K=d(h,w,P.value,S);if(K===null){w===null&&(w=I);break}e&&w&&K.alternate===null&&t(h,w),m=o(K,m,k),O===null?T=K:O.sibling=K,O=K,w=I}if(P.done)return n(h,w),Me&&Vr(h,k),T;if(w===null){for(;!P.done;k++,P=p.next())P=f(h,P.value,S),P!==null&&(m=o(P,m,k),O===null?T=P:O.sibling=P,O=P);return Me&&Vr(h,k),T}for(w=r(h,w);!P.done;k++,P=p.next())P=y(w,h,k,P.value,S),P!==null&&(e&&P.alternate!==null&&w.delete(P.key===null?k:P.key),m=o(P,m,k),O===null?T=P:O.sibling=P,O=P);return e&&w.forEach(function(se){return t(h,se)}),Me&&Vr(h,k),T}function E(h,m,p,S){if(typeof p=="object"&&p!==null&&p.type===bi&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case es:e:{for(var T=p.key,O=m;O!==null;){if(O.key===T){if(T=p.type,T===bi){if(O.tag===7){n(h,O.sibling),m=i(O,p.props.children),m.return=h,h=m;break e}}else if(O.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===sr&&bp(T)===O.type){n(h,O.sibling),m=i(O,p.props),m.ref=bo(h,O,p),m.return=h,h=m;break e}n(h,O);break}else t(h,O);O=O.sibling}p.type===bi?(m=ei(p.props.children,h.mode,S,p.key),m.return=h,h=m):(S=Us(p.type,p.key,p.props,null,h.mode,S),S.ref=bo(h,m,p),S.return=h,h=S)}return a(h);case xi:e:{for(O=p.key;m!==null;){if(m.key===O)if(m.tag===4&&m.stateNode.containerInfo===p.containerInfo&&m.stateNode.implementation===p.implementation){n(h,m.sibling),m=i(m,p.children||[]),m.return=h,h=m;break e}else{n(h,m);break}else t(h,m);m=m.sibling}m=Bu(p,h.mode,S),m.return=h,h=m}return a(h);case sr:return O=p._init,E(h,m,O(p._payload),S)}if(Uo(p))return g(h,m,p,S);if(wo(p))return v(h,m,p,S);fs(h,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,m!==null&&m.tag===6?(n(h,m.sibling),m=i(m,p),m.return=h,h=m):(n(h,m),m=Vu(p,h.mode,S),m.return=h,h=m),a(h)):n(h,m)}return E}var Zi=rg(!0),ig=rg(!1),ul=Ar(null),cl=null,Pi=null,Ed=null;function wd(){Ed=Pi=cl=null}function Sd(e){var t=ul.current;Pe(ul),e._currentValue=t}function qc(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function qi(e,t){cl=e,Ed=Pi=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Rt=!0),e.firstContext=null)}function Jt(e){var t=e._currentValue;if(Ed!==e)if(e={context:e,memoizedValue:t,next:null},Pi===null){if(cl===null)throw Error(F(308));Pi=e,cl.dependencies={lanes:0,firstContext:e}}else Pi=Pi.next=e;return t}var Gr=null;function _d(e){Gr===null?Gr=[e]:Gr.push(e)}function og(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,_d(t)):(n.next=i.next,i.next=n),t.interleaved=n,qn(e,r)}function qn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var lr=!1;function Td(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ag(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Vn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Sr(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ve&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,qn(e,n)}return i=r.interleaved,i===null?(t.next=t,_d(r)):(t.next=i.next,i.next=t),r.interleaved=t,qn(e,n)}function As(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ld(e,n)}}function kp(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=a:o=o.next=a,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function fl(e,t,n,r){var i=e.updateQueue;lr=!1;var o=i.firstBaseUpdate,a=i.lastBaseUpdate,s=i.shared.pending;if(s!==null){i.shared.pending=null;var l=s,u=l.next;l.next=null,a===null?o=u:a.next=u,a=l;var c=e.alternate;c!==null&&(c=c.updateQueue,s=c.lastBaseUpdate,s!==a&&(s===null?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(o!==null){var f=i.baseState;a=0,c=u=l=null,s=o;do{var d=s.lane,y=s.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:y,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var g=e,v=s;switch(d=t,y=n,v.tag){case 1:if(g=v.payload,typeof g=="function"){f=g.call(y,f,d);break e}f=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=v.payload,d=typeof g=="function"?g.call(y,f,d):g,d==null)break e;f=Ve({},f,d);break e;case 2:lr=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[s]:d.push(s))}else y={eventTime:y,lane:d,tag:s.tag,payload:s.payload,callback:s.callback,next:null},c===null?(u=c=y,l=f):c=c.next=y,a|=d;if(s=s.next,s===null){if(s=i.shared.pending,s===null)break;d=s,s=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(c===null&&(l=f),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do a|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);ai|=a,e.lanes=a,e.memoizedState=f}}function Cp(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(F(191,i));i.call(r)}}}var Va={},xn=Ar(Va),Ea=Ar(Va),wa=Ar(Va);function Yr(e){if(e===Va)throw Error(F(174));return e}function xd(e,t){switch(De(wa,t),De(Ea,e),De(xn,Va),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:bc(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=bc(t,e)}Pe(xn),De(xn,t)}function eo(){Pe(xn),Pe(Ea),Pe(wa)}function sg(e){Yr(wa.current);var t=Yr(xn.current),n=bc(t,e.type);t!==n&&(De(Ea,e),De(xn,n))}function bd(e){Ea.current===e&&(Pe(xn),Pe(Ea))}var ze=Ar(0);function dl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Lu=[];function kd(){for(var e=0;e<Lu.length;e++)Lu[e]._workInProgressVersionPrimary=null;Lu.length=0}var Ls=Gn.ReactCurrentDispatcher,Mu=Gn.ReactCurrentBatchConfig,oi=0,Ue=null,tt=null,ot=null,hl=!1,Xo=!1,Sa=0,_S=0;function mt(){throw Error(F(321))}function Cd(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yn(e[n],t[n]))return!1;return!0}function Od(e,t,n,r,i,o){if(oi=o,Ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ls.current=e===null||e.memoizedState===null?kS:CS,e=n(r,i),Xo){o=0;do{if(Xo=!1,Sa=0,25<=o)throw Error(F(301));o+=1,ot=tt=null,t.updateQueue=null,Ls.current=OS,e=n(r,i)}while(Xo)}if(Ls.current=pl,t=tt!==null&&tt.next!==null,oi=0,ot=tt=Ue=null,hl=!1,t)throw Error(F(300));return e}function Nd(){var e=Sa!==0;return Sa=0,e}function Sn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ot===null?Ue.memoizedState=ot=e:ot=ot.next=e,ot}function Zt(){if(tt===null){var e=Ue.alternate;e=e!==null?e.memoizedState:null}else e=tt.next;var t=ot===null?Ue.memoizedState:ot.next;if(t!==null)ot=t,tt=e;else{if(e===null)throw Error(F(310));tt=e,e={memoizedState:tt.memoizedState,baseState:tt.baseState,baseQueue:tt.baseQueue,queue:tt.queue,next:null},ot===null?Ue.memoizedState=ot=e:ot=ot.next=e}return ot}function _a(e,t){return typeof t=="function"?t(e):t}function Fu(e){var t=Zt(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=tt,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var a=i.next;i.next=o.next,o.next=a}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var s=a=null,l=null,u=o;do{var c=u.lane;if((oi&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(s=l=f,a=r):l=l.next=f,Ue.lanes|=c,ai|=c}u=u.next}while(u!==null&&u!==o);l===null?a=r:l.next=s,yn(r,t.memoizedState)||(Rt=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Ue.lanes|=o,ai|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ju(e){var t=Zt(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do o=e(o,a.action),a=a.next;while(a!==i);yn(o,t.memoizedState)||(Rt=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function lg(){}function ug(e,t){var n=Ue,r=Zt(),i=t(),o=!yn(r.memoizedState,i);if(o&&(r.memoizedState=i,Rt=!0),r=r.queue,Rd(dg.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ot!==null&&ot.memoizedState.tag&1){if(n.flags|=2048,Ta(9,fg.bind(null,n,r,i,t),void 0,null),st===null)throw Error(F(349));oi&30||cg(n,t,i)}return i}function cg(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Ue.updateQueue,t===null?(t={lastEffect:null,stores:null},Ue.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function fg(e,t,n,r){t.value=n,t.getSnapshot=r,hg(t)&&pg(e)}function dg(e,t,n){return n(function(){hg(t)&&pg(e)})}function hg(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yn(e,n)}catch{return!0}}function pg(e){var t=qn(e,1);t!==null&&mn(t,e,1,-1)}function Op(e){var t=Sn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:_a,lastRenderedState:e},t.queue=e,e=e.dispatch=bS.bind(null,Ue,e),[t.memoizedState,e]}function Ta(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Ue.updateQueue,t===null?(t={lastEffect:null,stores:null},Ue.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function mg(){return Zt().memoizedState}function Ms(e,t,n,r){var i=Sn();Ue.flags|=e,i.memoizedState=Ta(1|t,n,void 0,r===void 0?null:r)}function Ll(e,t,n,r){var i=Zt();r=r===void 0?null:r;var o=void 0;if(tt!==null){var a=tt.memoizedState;if(o=a.destroy,r!==null&&Cd(r,a.deps)){i.memoizedState=Ta(t,n,o,r);return}}Ue.flags|=e,i.memoizedState=Ta(1|t,n,o,r)}function Np(e,t){return Ms(8390656,8,e,t)}function Rd(e,t){return Ll(2048,8,e,t)}function vg(e,t){return Ll(4,2,e,t)}function yg(e,t){return Ll(4,4,e,t)}function gg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Eg(e,t,n){return n=n!=null?n.concat([e]):null,Ll(4,4,gg.bind(null,t,e),n)}function Dd(){}function wg(e,t){var n=Zt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Cd(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Sg(e,t){var n=Zt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Cd(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function _g(e,t,n){return oi&21?(yn(n,t)||(n=Cy(),Ue.lanes|=n,ai|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Rt=!0),e.memoizedState=n)}function TS(e,t){var n=be;be=n!==0&&4>n?n:4,e(!0);var r=Mu.transition;Mu.transition={};try{e(!1),t()}finally{be=n,Mu.transition=r}}function Tg(){return Zt().memoizedState}function xS(e,t,n){var r=Tr(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},xg(e))bg(t,n);else if(n=og(e,t,n,r),n!==null){var i=St();mn(n,e,r,i),kg(n,t,r)}}function bS(e,t,n){var r=Tr(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(xg(e))bg(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var a=t.lastRenderedState,s=o(a,n);if(i.hasEagerState=!0,i.eagerState=s,yn(s,a)){var l=t.interleaved;l===null?(i.next=i,_d(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=og(e,t,i,r),n!==null&&(i=St(),mn(n,e,r,i),kg(n,t,r))}}function xg(e){var t=e.alternate;return e===Ue||t!==null&&t===Ue}function bg(e,t){Xo=hl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function kg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ld(e,n)}}var pl={readContext:Jt,useCallback:mt,useContext:mt,useEffect:mt,useImperativeHandle:mt,useInsertionEffect:mt,useLayoutEffect:mt,useMemo:mt,useReducer:mt,useRef:mt,useState:mt,useDebugValue:mt,useDeferredValue:mt,useTransition:mt,useMutableSource:mt,useSyncExternalStore:mt,useId:mt,unstable_isNewReconciler:!1},kS={readContext:Jt,useCallback:function(e,t){return Sn().memoizedState=[e,t===void 0?null:t],e},useContext:Jt,useEffect:Np,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ms(4194308,4,gg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ms(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ms(4,2,e,t)},useMemo:function(e,t){var n=Sn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Sn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=xS.bind(null,Ue,e),[r.memoizedState,e]},useRef:function(e){var t=Sn();return e={current:e},t.memoizedState=e},useState:Op,useDebugValue:Dd,useDeferredValue:function(e){return Sn().memoizedState=e},useTransition:function(){var e=Op(!1),t=e[0];return e=TS.bind(null,e[1]),Sn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Ue,i=Sn();if(Me){if(n===void 0)throw Error(F(407));n=n()}else{if(n=t(),st===null)throw Error(F(349));oi&30||cg(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Np(dg.bind(null,r,o,e),[e]),r.flags|=2048,Ta(9,fg.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Sn(),t=st.identifierPrefix;if(Me){var n=Un,r=zn;n=(r&~(1<<32-pn(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Sa++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=_S++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},CS={readContext:Jt,useCallback:wg,useContext:Jt,useEffect:Rd,useImperativeHandle:Eg,useInsertionEffect:vg,useLayoutEffect:yg,useMemo:Sg,useReducer:Fu,useRef:mg,useState:function(){return Fu(_a)},useDebugValue:Dd,useDeferredValue:function(e){var t=Zt();return _g(t,tt.memoizedState,e)},useTransition:function(){var e=Fu(_a)[0],t=Zt().memoizedState;return[e,t]},useMutableSource:lg,useSyncExternalStore:ug,useId:Tg,unstable_isNewReconciler:!1},OS={readContext:Jt,useCallback:wg,useContext:Jt,useEffect:Rd,useImperativeHandle:Eg,useInsertionEffect:vg,useLayoutEffect:yg,useMemo:Sg,useReducer:ju,useRef:mg,useState:function(){return ju(_a)},useDebugValue:Dd,useDeferredValue:function(e){var t=Zt();return tt===null?t.memoizedState=e:_g(t,tt.memoizedState,e)},useTransition:function(){var e=ju(_a)[0],t=Zt().memoizedState;return[e,t]},useMutableSource:lg,useSyncExternalStore:ug,useId:Tg,unstable_isNewReconciler:!1};function un(e,t){if(e&&e.defaultProps){t=Ve({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Hc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Ve({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ml={isMounted:function(e){return(e=e._reactInternals)?di(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=St(),i=Tr(e),o=Vn(r,i);o.payload=t,n!=null&&(o.callback=n),t=Sr(e,o,i),t!==null&&(mn(t,e,i,r),As(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=St(),i=Tr(e),o=Vn(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Sr(e,o,i),t!==null&&(mn(t,e,i,r),As(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=St(),r=Tr(e),i=Vn(n,r);i.tag=2,t!=null&&(i.callback=t),t=Sr(e,i,r),t!==null&&(mn(t,e,r,n),As(t,e,r))}};function Rp(e,t,n,r,i,o,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,a):t.prototype&&t.prototype.isPureReactComponent?!ma(n,r)||!ma(i,o):!0}function Cg(e,t,n){var r=!1,i=Cr,o=t.contextType;return typeof o=="object"&&o!==null?o=Jt(o):(i=It(t)?ri:gt.current,r=t.contextTypes,o=(r=r!=null)?Ki(e,i):Cr),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ml,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Dp(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ml.enqueueReplaceState(t,t.state,null)}function Wc(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Td(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Jt(o):(o=It(t)?ri:gt.current,i.context=Ki(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Hc(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Ml.enqueueReplaceState(i,i.state,null),fl(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function to(e,t){try{var n="",r=t;do n+=nw(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function zu(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Gc(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var NS=typeof WeakMap=="function"?WeakMap:Map;function Og(e,t,n){n=Vn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){vl||(vl=!0,of=r),Gc(e,t)},n}function Ng(e,t,n){n=Vn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Gc(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Gc(e,t),typeof r!="function"&&(_r===null?_r=new Set([this]):_r.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Ip(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new NS;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=QS.bind(null,e,t,n),t.then(e,e))}function Pp(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ap(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Vn(-1,1),t.tag=2,Sr(n,t,1))),n.lanes|=1),e)}var RS=Gn.ReactCurrentOwner,Rt=!1;function Et(e,t,n,r){t.child=e===null?ig(t,null,n,r):Zi(t,e.child,n,r)}function Lp(e,t,n,r,i){n=n.render;var o=t.ref;return qi(t,i),r=Od(e,t,n,r,o,i),n=Nd(),e!==null&&!Rt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Hn(e,t,i)):(Me&&n&&vd(t),t.flags|=1,Et(e,t,r,i),t.child)}function Mp(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!zd(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Rg(e,t,o,r,i)):(e=Us(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var a=o.memoizedProps;if(n=n.compare,n=n!==null?n:ma,n(a,r)&&e.ref===t.ref)return Hn(e,t,i)}return t.flags|=1,e=xr(o,r),e.ref=t.ref,e.return=t,t.child=e}function Rg(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(ma(o,r)&&e.ref===t.ref)if(Rt=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Rt=!0);else return t.lanes=e.lanes,Hn(e,t,i)}return Yc(e,t,n,r,i)}function Dg(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},De(Li,Ft),Ft|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,De(Li,Ft),Ft|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,De(Li,Ft),Ft|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,De(Li,Ft),Ft|=r;return Et(e,t,i,n),t.child}function Ig(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Yc(e,t,n,r,i){var o=It(n)?ri:gt.current;return o=Ki(t,o),qi(t,i),n=Od(e,t,n,r,o,i),r=Nd(),e!==null&&!Rt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Hn(e,t,i)):(Me&&r&&vd(t),t.flags|=1,Et(e,t,n,i),t.child)}function Fp(e,t,n,r,i){if(It(n)){var o=!0;al(t)}else o=!1;if(qi(t,i),t.stateNode===null)Fs(e,t),Cg(t,n,r),Wc(t,n,r,i),r=!0;else if(e===null){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=Jt(u):(u=It(n)?ri:gt.current,u=Ki(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof a.getSnapshotBeforeUpdate=="function";f||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(s!==r||l!==u)&&Dp(t,a,r,u),lr=!1;var d=t.memoizedState;a.state=d,fl(t,r,a,i),l=t.memoizedState,s!==r||d!==l||Dt.current||lr?(typeof c=="function"&&(Hc(t,n,c,r),l=t.memoizedState),(s=lr||Rp(t,n,s,r,d,l,u))?(f||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=u,r=s):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,ag(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:un(t.type,s),a.props=u,f=t.pendingProps,d=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=Jt(l):(l=It(n)?ri:gt.current,l=Ki(t,l));var y=n.getDerivedStateFromProps;(c=typeof y=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(s!==f||d!==l)&&Dp(t,a,r,l),lr=!1,d=t.memoizedState,a.state=d,fl(t,r,a,i);var g=t.memoizedState;s!==f||d!==g||Dt.current||lr?(typeof y=="function"&&(Hc(t,n,y,r),g=t.memoizedState),(u=lr||Rp(t,n,u,r,d,g,l)||!1)?(c||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,g,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,g,l)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),a.props=r,a.state=g,a.context=l,r=u):(typeof a.componentDidUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Xc(e,t,n,r,o,i)}function Xc(e,t,n,r,i,o){Ig(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return i&&_p(t,n,!1),Hn(e,t,o);r=t.stateNode,RS.current=t;var s=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=Zi(t,e.child,null,o),t.child=Zi(t,null,s,o)):Et(e,t,s,o),t.memoizedState=r.state,i&&_p(t,n,!0),t.child}function Pg(e){var t=e.stateNode;t.pendingContext?Sp(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Sp(e,t.context,!1),xd(e,t.containerInfo)}function jp(e,t,n,r,i){return Ji(),gd(i),t.flags|=256,Et(e,t,n,r),t.child}var Kc={dehydrated:null,treeContext:null,retryLane:0};function Jc(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ag(e,t,n){var r=t.pendingProps,i=ze.current,o=!1,a=(t.flags&128)!==0,s;if((s=a)||(s=e!==null&&e.memoizedState===null?!1:(i&2)!==0),s?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),De(ze,i&1),e===null)return $c(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,o?(r=t.mode,o=t.child,a={mode:"hidden",children:a},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=a):o=zl(a,r,0,null),e=ei(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Jc(n),t.memoizedState=Kc,e):Id(t,a));if(i=e.memoizedState,i!==null&&(s=i.dehydrated,s!==null))return DS(e,t,a,r,s,i,n);if(o){o=r.fallback,a=t.mode,i=e.child,s=i.sibling;var l={mode:"hidden",children:r.children};return!(a&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=xr(i,l),r.subtreeFlags=i.subtreeFlags&14680064),s!==null?o=xr(s,o):(o=ei(o,a,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,a=e.child.memoizedState,a=a===null?Jc(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},o.memoizedState=a,o.childLanes=e.childLanes&~n,t.memoizedState=Kc,r}return o=e.child,e=o.sibling,r=xr(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Id(e,t){return t=zl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ds(e,t,n,r){return r!==null&&gd(r),Zi(t,e.child,null,n),e=Id(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function DS(e,t,n,r,i,o,a){if(n)return t.flags&256?(t.flags&=-257,r=zu(Error(F(422))),ds(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=zl({mode:"visible",children:r.children},i,0,null),o=ei(o,i,a,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Zi(t,e.child,null,a),t.child.memoizedState=Jc(a),t.memoizedState=Kc,o);if(!(t.mode&1))return ds(e,t,a,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var s=r.dgst;return r=s,o=Error(F(419)),r=zu(o,r,void 0),ds(e,t,a,r)}if(s=(a&e.childLanes)!==0,Rt||s){if(r=st,r!==null){switch(a&-a){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|a)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,qn(e,i),mn(r,e,i,-1))}return jd(),r=zu(Error(F(421))),ds(e,t,a,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=$S.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,jt=wr(i.nextSibling),Ut=t,Me=!0,dn=null,e!==null&&(Wt[Gt++]=zn,Wt[Gt++]=Un,Wt[Gt++]=ii,zn=e.id,Un=e.overflow,ii=t),t=Id(t,r.children),t.flags|=4096,t)}function zp(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),qc(e.return,t,n)}function Uu(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Lg(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Et(e,t,r.children,n),r=ze.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&zp(e,n,t);else if(e.tag===19)zp(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(De(ze,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&dl(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Uu(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&dl(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Uu(t,!0,n,null,o);break;case"together":Uu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Fs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ai|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(F(153));if(t.child!==null){for(e=t.child,n=xr(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=xr(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function IS(e,t,n){switch(t.tag){case 3:Pg(t),Ji();break;case 5:sg(t);break;case 1:It(t.type)&&al(t);break;case 4:xd(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;De(ul,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(De(ze,ze.current&1),t.flags|=128,null):n&t.child.childLanes?Ag(e,t,n):(De(ze,ze.current&1),e=Hn(e,t,n),e!==null?e.sibling:null);De(ze,ze.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Lg(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),De(ze,ze.current),r)break;return null;case 22:case 23:return t.lanes=0,Dg(e,t,n)}return Hn(e,t,n)}var Mg,Zc,Fg,jg;Mg=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Zc=function(){};Fg=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Yr(xn.current);var o=null;switch(n){case"input":i=Sc(e,i),r=Sc(e,r),o=[];break;case"select":i=Ve({},i,{value:void 0}),r=Ve({},r,{value:void 0}),o=[];break;case"textarea":i=xc(e,i),r=xc(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=il)}kc(n,r);var a;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var s=i[u];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(la.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(s=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==s&&(l!=null||s!=null))if(u==="style")if(s){for(a in s)!s.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&s[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,s=s?s.__html:void 0,l!=null&&s!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(la.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Ie("scroll",e),o||s===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};jg=function(e,t,n,r){n!==r&&(t.flags|=4)};function ko(e,t){if(!Me)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function vt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function PS(e,t,n){var r=t.pendingProps;switch(yd(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return vt(t),null;case 1:return It(t.type)&&ol(),vt(t),null;case 3:return r=t.stateNode,eo(),Pe(Dt),Pe(gt),kd(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(cs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,dn!==null&&(lf(dn),dn=null))),Zc(e,t),vt(t),null;case 5:bd(t);var i=Yr(wa.current);if(n=t.type,e!==null&&t.stateNode!=null)Fg(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(F(166));return vt(t),null}if(e=Yr(xn.current),cs(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[_n]=t,r[ga]=o,e=(t.mode&1)!==0,n){case"dialog":Ie("cancel",r),Ie("close",r);break;case"iframe":case"object":case"embed":Ie("load",r);break;case"video":case"audio":for(i=0;i<Bo.length;i++)Ie(Bo[i],r);break;case"source":Ie("error",r);break;case"img":case"image":case"link":Ie("error",r),Ie("load",r);break;case"details":Ie("toggle",r);break;case"input":Gh(r,o),Ie("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Ie("invalid",r);break;case"textarea":Xh(r,o),Ie("invalid",r)}kc(n,o),i=null;for(var a in o)if(o.hasOwnProperty(a)){var s=o[a];a==="children"?typeof s=="string"?r.textContent!==s&&(o.suppressHydrationWarning!==!0&&us(r.textContent,s,e),i=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(o.suppressHydrationWarning!==!0&&us(r.textContent,s,e),i=["children",""+s]):la.hasOwnProperty(a)&&s!=null&&a==="onScroll"&&Ie("scroll",r)}switch(n){case"input":ts(r),Yh(r,o,!0);break;case"textarea":ts(r),Kh(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=il)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=dy(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[_n]=t,e[ga]=r,Mg(e,t,!1,!1),t.stateNode=e;e:{switch(a=Cc(n,r),n){case"dialog":Ie("cancel",e),Ie("close",e),i=r;break;case"iframe":case"object":case"embed":Ie("load",e),i=r;break;case"video":case"audio":for(i=0;i<Bo.length;i++)Ie(Bo[i],e);i=r;break;case"source":Ie("error",e),i=r;break;case"img":case"image":case"link":Ie("error",e),Ie("load",e),i=r;break;case"details":Ie("toggle",e),i=r;break;case"input":Gh(e,r),i=Sc(e,r),Ie("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Ve({},r,{value:void 0}),Ie("invalid",e);break;case"textarea":Xh(e,r),i=xc(e,r),Ie("invalid",e);break;default:i=r}kc(n,i),s=i;for(o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="style"?my(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&hy(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ua(e,l):typeof l=="number"&&ua(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(la.hasOwnProperty(o)?l!=null&&o==="onScroll"&&Ie("scroll",e):l!=null&&nd(e,o,l,a))}switch(n){case"input":ts(e),Yh(e,r,!1);break;case"textarea":ts(e),Kh(e);break;case"option":r.value!=null&&e.setAttribute("value",""+kr(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Vi(e,!!r.multiple,o,!1):r.defaultValue!=null&&Vi(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=il)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return vt(t),null;case 6:if(e&&t.stateNode!=null)jg(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(F(166));if(n=Yr(wa.current),Yr(xn.current),cs(t)){if(r=t.stateNode,n=t.memoizedProps,r[_n]=t,(o=r.nodeValue!==n)&&(e=Ut,e!==null))switch(e.tag){case 3:us(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&us(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[_n]=t,t.stateNode=r}return vt(t),null;case 13:if(Pe(ze),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Me&&jt!==null&&t.mode&1&&!(t.flags&128))ng(),Ji(),t.flags|=98560,o=!1;else if(o=cs(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(F(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(F(317));o[_n]=t}else Ji(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;vt(t),o=!1}else dn!==null&&(lf(dn),dn=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ze.current&1?nt===0&&(nt=3):jd())),t.updateQueue!==null&&(t.flags|=4),vt(t),null);case 4:return eo(),Zc(e,t),e===null&&va(t.stateNode.containerInfo),vt(t),null;case 10:return Sd(t.type._context),vt(t),null;case 17:return It(t.type)&&ol(),vt(t),null;case 19:if(Pe(ze),o=t.memoizedState,o===null)return vt(t),null;if(r=(t.flags&128)!==0,a=o.rendering,a===null)if(r)ko(o,!1);else{if(nt!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=dl(e),a!==null){for(t.flags|=128,ko(o,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,a=o.alternate,a===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=a.childLanes,o.lanes=a.lanes,o.child=a.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=a.memoizedProps,o.memoizedState=a.memoizedState,o.updateQueue=a.updateQueue,o.type=a.type,e=a.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return De(ze,ze.current&1|2),t.child}e=e.sibling}o.tail!==null&&We()>no&&(t.flags|=128,r=!0,ko(o,!1),t.lanes=4194304)}else{if(!r)if(e=dl(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ko(o,!0),o.tail===null&&o.tailMode==="hidden"&&!a.alternate&&!Me)return vt(t),null}else 2*We()-o.renderingStartTime>no&&n!==1073741824&&(t.flags|=128,r=!0,ko(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(n=o.last,n!==null?n.sibling=a:t.child=a,o.last=a)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=We(),t.sibling=null,n=ze.current,De(ze,r?n&1|2:n&1),t):(vt(t),null);case 22:case 23:return Fd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ft&1073741824&&(vt(t),t.subtreeFlags&6&&(t.flags|=8192)):vt(t),null;case 24:return null;case 25:return null}throw Error(F(156,t.tag))}function AS(e,t){switch(yd(t),t.tag){case 1:return It(t.type)&&ol(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return eo(),Pe(Dt),Pe(gt),kd(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return bd(t),null;case 13:if(Pe(ze),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(F(340));Ji()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Pe(ze),null;case 4:return eo(),null;case 10:return Sd(t.type._context),null;case 22:case 23:return Fd(),null;case 24:return null;default:return null}}var hs=!1,yt=!1,LS=typeof WeakSet=="function"?WeakSet:Set,$=null;function Ai(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Be(e,t,r)}else n.current=null}function ef(e,t,n){try{n()}catch(r){Be(e,t,r)}}var Up=!1;function MS(e,t){if(Fc=tl,e=Qy(),md(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var a=0,s=-1,l=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var y;f!==n||i!==0&&f.nodeType!==3||(s=a+i),f!==o||r!==0&&f.nodeType!==3||(l=a+r),f.nodeType===3&&(a+=f.nodeValue.length),(y=f.firstChild)!==null;)d=f,f=y;for(;;){if(f===e)break t;if(d===n&&++u===i&&(s=a),d===o&&++c===r&&(l=a),(y=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=y}n=s===-1||l===-1?null:{start:s,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(jc={focusedElem:e,selectionRange:n},tl=!1,$=t;$!==null;)if(t=$,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,$=e;else for(;$!==null;){t=$;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var v=g.memoizedProps,E=g.memoizedState,h=t.stateNode,m=h.getSnapshotBeforeUpdate(t.elementType===t.type?v:un(t.type,v),E);h.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(F(163))}}catch(S){Be(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,$=e;break}$=t.return}return g=Up,Up=!1,g}function Ko(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&ef(t,n,o)}i=i.next}while(i!==r)}}function Fl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function tf(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function zg(e){var t=e.alternate;t!==null&&(e.alternate=null,zg(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[_n],delete t[ga],delete t[Vc],delete t[gS],delete t[ES])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ug(e){return e.tag===5||e.tag===3||e.tag===4}function Vp(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ug(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function nf(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=il));else if(r!==4&&(e=e.child,e!==null))for(nf(e,t,n),e=e.sibling;e!==null;)nf(e,t,n),e=e.sibling}function rf(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(rf(e,t,n),e=e.sibling;e!==null;)rf(e,t,n),e=e.sibling}var ut=null,cn=!1;function ir(e,t,n){for(n=n.child;n!==null;)Vg(e,t,n),n=n.sibling}function Vg(e,t,n){if(Tn&&typeof Tn.onCommitFiberUnmount=="function")try{Tn.onCommitFiberUnmount(Nl,n)}catch{}switch(n.tag){case 5:yt||Ai(n,t);case 6:var r=ut,i=cn;ut=null,ir(e,t,n),ut=r,cn=i,ut!==null&&(cn?(e=ut,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ut.removeChild(n.stateNode));break;case 18:ut!==null&&(cn?(e=ut,n=n.stateNode,e.nodeType===8?Pu(e.parentNode,n):e.nodeType===1&&Pu(e,n),ha(e)):Pu(ut,n.stateNode));break;case 4:r=ut,i=cn,ut=n.stateNode.containerInfo,cn=!0,ir(e,t,n),ut=r,cn=i;break;case 0:case 11:case 14:case 15:if(!yt&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,a=o.destroy;o=o.tag,a!==void 0&&(o&2||o&4)&&ef(n,t,a),i=i.next}while(i!==r)}ir(e,t,n);break;case 1:if(!yt&&(Ai(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Be(n,t,s)}ir(e,t,n);break;case 21:ir(e,t,n);break;case 22:n.mode&1?(yt=(r=yt)||n.memoizedState!==null,ir(e,t,n),yt=r):ir(e,t,n);break;default:ir(e,t,n)}}function Bp(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new LS),t.forEach(function(r){var i=qS.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function an(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,a=t,s=a;e:for(;s!==null;){switch(s.tag){case 5:ut=s.stateNode,cn=!1;break e;case 3:ut=s.stateNode.containerInfo,cn=!0;break e;case 4:ut=s.stateNode.containerInfo,cn=!0;break e}s=s.return}if(ut===null)throw Error(F(160));Vg(o,a,i),ut=null,cn=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){Be(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Bg(t,e),t=t.sibling}function Bg(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(an(t,e),wn(e),r&4){try{Ko(3,e,e.return),Fl(3,e)}catch(v){Be(e,e.return,v)}try{Ko(5,e,e.return)}catch(v){Be(e,e.return,v)}}break;case 1:an(t,e),wn(e),r&512&&n!==null&&Ai(n,n.return);break;case 5:if(an(t,e),wn(e),r&512&&n!==null&&Ai(n,n.return),e.flags&32){var i=e.stateNode;try{ua(i,"")}catch(v){Be(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,a=n!==null?n.memoizedProps:o,s=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{s==="input"&&o.type==="radio"&&o.name!=null&&cy(i,o),Cc(s,a);var u=Cc(s,o);for(a=0;a<l.length;a+=2){var c=l[a],f=l[a+1];c==="style"?my(i,f):c==="dangerouslySetInnerHTML"?hy(i,f):c==="children"?ua(i,f):nd(i,c,f,u)}switch(s){case"input":_c(i,o);break;case"textarea":fy(i,o);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var y=o.value;y!=null?Vi(i,!!o.multiple,y,!1):d!==!!o.multiple&&(o.defaultValue!=null?Vi(i,!!o.multiple,o.defaultValue,!0):Vi(i,!!o.multiple,o.multiple?[]:"",!1))}i[ga]=o}catch(v){Be(e,e.return,v)}}break;case 6:if(an(t,e),wn(e),r&4){if(e.stateNode===null)throw Error(F(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(v){Be(e,e.return,v)}}break;case 3:if(an(t,e),wn(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ha(t.containerInfo)}catch(v){Be(e,e.return,v)}break;case 4:an(t,e),wn(e);break;case 13:an(t,e),wn(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Ld=We())),r&4&&Bp(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(yt=(u=yt)||c,an(t,e),yt=u):an(t,e),wn(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for($=e,c=e.child;c!==null;){for(f=$=c;$!==null;){switch(d=$,y=d.child,d.tag){case 0:case 11:case 14:case 15:Ko(4,d,d.return);break;case 1:Ai(d,d.return);var g=d.stateNode;if(typeof g.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(v){Be(r,n,v)}}break;case 5:Ai(d,d.return);break;case 22:if(d.memoizedState!==null){$p(f);continue}}y!==null?(y.return=d,$=y):$p(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(s=f.stateNode,l=f.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,s.style.display=py("display",a))}catch(v){Be(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){Be(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:an(t,e),wn(e),r&4&&Bp(e);break;case 21:break;default:an(t,e),wn(e)}}function wn(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ug(n)){var r=n;break e}n=n.return}throw Error(F(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(ua(i,""),r.flags&=-33);var o=Vp(e);rf(e,o,i);break;case 3:case 4:var a=r.stateNode.containerInfo,s=Vp(e);nf(e,s,a);break;default:throw Error(F(161))}}catch(l){Be(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function FS(e,t,n){$=e,Qg(e)}function Qg(e,t,n){for(var r=(e.mode&1)!==0;$!==null;){var i=$,o=i.child;if(i.tag===22&&r){var a=i.memoizedState!==null||hs;if(!a){var s=i.alternate,l=s!==null&&s.memoizedState!==null||yt;s=hs;var u=yt;if(hs=a,(yt=l)&&!u)for($=i;$!==null;)a=$,l=a.child,a.tag===22&&a.memoizedState!==null?qp(i):l!==null?(l.return=a,$=l):qp(i);for(;o!==null;)$=o,Qg(o),o=o.sibling;$=i,hs=s,yt=u}Qp(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,$=o):Qp(e)}}function Qp(e){for(;$!==null;){var t=$;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:yt||Fl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!yt)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:un(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Cp(t,o,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Cp(t,a,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&ha(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(F(163))}yt||t.flags&512&&tf(t)}catch(d){Be(t,t.return,d)}}if(t===e){$=null;break}if(n=t.sibling,n!==null){n.return=t.return,$=n;break}$=t.return}}function $p(e){for(;$!==null;){var t=$;if(t===e){$=null;break}var n=t.sibling;if(n!==null){n.return=t.return,$=n;break}$=t.return}}function qp(e){for(;$!==null;){var t=$;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Fl(4,t)}catch(l){Be(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){Be(t,i,l)}}var o=t.return;try{tf(t)}catch(l){Be(t,o,l)}break;case 5:var a=t.return;try{tf(t)}catch(l){Be(t,a,l)}}}catch(l){Be(t,t.return,l)}if(t===e){$=null;break}var s=t.sibling;if(s!==null){s.return=t.return,$=s;break}$=t.return}}var jS=Math.ceil,ml=Gn.ReactCurrentDispatcher,Pd=Gn.ReactCurrentOwner,Kt=Gn.ReactCurrentBatchConfig,ve=0,st=null,Ze=null,ct=0,Ft=0,Li=Ar(0),nt=0,xa=null,ai=0,jl=0,Ad=0,Jo=null,Nt=null,Ld=0,no=1/0,Mn=null,vl=!1,of=null,_r=null,ps=!1,mr=null,yl=0,Zo=0,af=null,js=-1,zs=0;function St(){return ve&6?We():js!==-1?js:js=We()}function Tr(e){return e.mode&1?ve&2&&ct!==0?ct&-ct:SS.transition!==null?(zs===0&&(zs=Cy()),zs):(e=be,e!==0||(e=window.event,e=e===void 0?16:Ay(e.type)),e):1}function mn(e,t,n,r){if(50<Zo)throw Zo=0,af=null,Error(F(185));ja(e,n,r),(!(ve&2)||e!==st)&&(e===st&&(!(ve&2)&&(jl|=n),nt===4&&hr(e,ct)),Pt(e,r),n===1&&ve===0&&!(t.mode&1)&&(no=We()+500,Al&&Lr()))}function Pt(e,t){var n=e.callbackNode;Sw(e,t);var r=el(e,e===st?ct:0);if(r===0)n!==null&&ep(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ep(n),t===1)e.tag===0?wS(Hp.bind(null,e)):Zy(Hp.bind(null,e)),vS(function(){!(ve&6)&&Lr()}),n=null;else{switch(Oy(r)){case 1:n=sd;break;case 4:n=by;break;case 16:n=Zs;break;case 536870912:n=ky;break;default:n=Zs}n=Kg(n,$g.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function $g(e,t){if(js=-1,zs=0,ve&6)throw Error(F(327));var n=e.callbackNode;if(Hi()&&e.callbackNode!==n)return null;var r=el(e,e===st?ct:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=gl(e,r);else{t=r;var i=ve;ve|=2;var o=Hg();(st!==e||ct!==t)&&(Mn=null,no=We()+500,Zr(e,t));do try{VS();break}catch(s){qg(e,s)}while(!0);wd(),ml.current=o,ve=i,Ze!==null?t=0:(st=null,ct=0,t=nt)}if(t!==0){if(t===2&&(i=Ic(e),i!==0&&(r=i,t=sf(e,i))),t===1)throw n=xa,Zr(e,0),hr(e,r),Pt(e,We()),n;if(t===6)hr(e,r);else{if(i=e.current.alternate,!(r&30)&&!zS(i)&&(t=gl(e,r),t===2&&(o=Ic(e),o!==0&&(r=o,t=sf(e,o))),t===1))throw n=xa,Zr(e,0),hr(e,r),Pt(e,We()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(F(345));case 2:Br(e,Nt,Mn);break;case 3:if(hr(e,r),(r&130023424)===r&&(t=Ld+500-We(),10<t)){if(el(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){St(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Uc(Br.bind(null,e,Nt,Mn),t);break}Br(e,Nt,Mn);break;case 4:if(hr(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var a=31-pn(r);o=1<<a,a=t[a],a>i&&(i=a),r&=~o}if(r=i,r=We()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*jS(r/1960))-r,10<r){e.timeoutHandle=Uc(Br.bind(null,e,Nt,Mn),r);break}Br(e,Nt,Mn);break;case 5:Br(e,Nt,Mn);break;default:throw Error(F(329))}}}return Pt(e,We()),e.callbackNode===n?$g.bind(null,e):null}function sf(e,t){var n=Jo;return e.current.memoizedState.isDehydrated&&(Zr(e,t).flags|=256),e=gl(e,t),e!==2&&(t=Nt,Nt=n,t!==null&&lf(t)),e}function lf(e){Nt===null?Nt=e:Nt.push.apply(Nt,e)}function zS(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!yn(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function hr(e,t){for(t&=~Ad,t&=~jl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-pn(t),r=1<<n;e[n]=-1,t&=~r}}function Hp(e){if(ve&6)throw Error(F(327));Hi();var t=el(e,0);if(!(t&1))return Pt(e,We()),null;var n=gl(e,t);if(e.tag!==0&&n===2){var r=Ic(e);r!==0&&(t=r,n=sf(e,r))}if(n===1)throw n=xa,Zr(e,0),hr(e,t),Pt(e,We()),n;if(n===6)throw Error(F(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Br(e,Nt,Mn),Pt(e,We()),null}function Md(e,t){var n=ve;ve|=1;try{return e(t)}finally{ve=n,ve===0&&(no=We()+500,Al&&Lr())}}function si(e){mr!==null&&mr.tag===0&&!(ve&6)&&Hi();var t=ve;ve|=1;var n=Kt.transition,r=be;try{if(Kt.transition=null,be=1,e)return e()}finally{be=r,Kt.transition=n,ve=t,!(ve&6)&&Lr()}}function Fd(){Ft=Li.current,Pe(Li)}function Zr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,mS(n)),Ze!==null)for(n=Ze.return;n!==null;){var r=n;switch(yd(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ol();break;case 3:eo(),Pe(Dt),Pe(gt),kd();break;case 5:bd(r);break;case 4:eo();break;case 13:Pe(ze);break;case 19:Pe(ze);break;case 10:Sd(r.type._context);break;case 22:case 23:Fd()}n=n.return}if(st=e,Ze=e=xr(e.current,null),ct=Ft=t,nt=0,xa=null,Ad=jl=ai=0,Nt=Jo=null,Gr!==null){for(t=0;t<Gr.length;t++)if(n=Gr[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var a=o.next;o.next=i,r.next=a}n.pending=r}Gr=null}return e}function qg(e,t){do{var n=Ze;try{if(wd(),Ls.current=pl,hl){for(var r=Ue.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}hl=!1}if(oi=0,ot=tt=Ue=null,Xo=!1,Sa=0,Pd.current=null,n===null||n.return===null){nt=1,xa=t,Ze=null;break}e:{var o=e,a=n.return,s=n,l=t;if(t=ct,s.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=s,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var y=Pp(a);if(y!==null){y.flags&=-257,Ap(y,a,s,o,t),y.mode&1&&Ip(o,u,t),t=y,l=u;var g=t.updateQueue;if(g===null){var v=new Set;v.add(l),t.updateQueue=v}else g.add(l);break e}else{if(!(t&1)){Ip(o,u,t),jd();break e}l=Error(F(426))}}else if(Me&&s.mode&1){var E=Pp(a);if(E!==null){!(E.flags&65536)&&(E.flags|=256),Ap(E,a,s,o,t),gd(to(l,s));break e}}o=l=to(l,s),nt!==4&&(nt=2),Jo===null?Jo=[o]:Jo.push(o),o=a;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var h=Og(o,l,t);kp(o,h);break e;case 1:s=l;var m=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof m.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(_r===null||!_r.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var S=Ng(o,s,t);kp(o,S);break e}}o=o.return}while(o!==null)}Gg(n)}catch(T){t=T,Ze===n&&n!==null&&(Ze=n=n.return);continue}break}while(!0)}function Hg(){var e=ml.current;return ml.current=pl,e===null?pl:e}function jd(){(nt===0||nt===3||nt===2)&&(nt=4),st===null||!(ai&268435455)&&!(jl&268435455)||hr(st,ct)}function gl(e,t){var n=ve;ve|=2;var r=Hg();(st!==e||ct!==t)&&(Mn=null,Zr(e,t));do try{US();break}catch(i){qg(e,i)}while(!0);if(wd(),ve=n,ml.current=r,Ze!==null)throw Error(F(261));return st=null,ct=0,nt}function US(){for(;Ze!==null;)Wg(Ze)}function VS(){for(;Ze!==null&&!dw();)Wg(Ze)}function Wg(e){var t=Xg(e.alternate,e,Ft);e.memoizedProps=e.pendingProps,t===null?Gg(e):Ze=t,Pd.current=null}function Gg(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=AS(n,t),n!==null){n.flags&=32767,Ze=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{nt=6,Ze=null;return}}else if(n=PS(n,t,Ft),n!==null){Ze=n;return}if(t=t.sibling,t!==null){Ze=t;return}Ze=t=e}while(t!==null);nt===0&&(nt=5)}function Br(e,t,n){var r=be,i=Kt.transition;try{Kt.transition=null,be=1,BS(e,t,n,r)}finally{Kt.transition=i,be=r}return null}function BS(e,t,n,r){do Hi();while(mr!==null);if(ve&6)throw Error(F(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(F(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(_w(e,o),e===st&&(Ze=st=null,ct=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ps||(ps=!0,Kg(Zs,function(){return Hi(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Kt.transition,Kt.transition=null;var a=be;be=1;var s=ve;ve|=4,Pd.current=null,MS(e,n),Bg(n,e),lS(jc),tl=!!Fc,jc=Fc=null,e.current=n,FS(n),hw(),ve=s,be=a,Kt.transition=o}else e.current=n;if(ps&&(ps=!1,mr=e,yl=i),o=e.pendingLanes,o===0&&(_r=null),vw(n.stateNode),Pt(e,We()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(vl)throw vl=!1,e=of,of=null,e;return yl&1&&e.tag!==0&&Hi(),o=e.pendingLanes,o&1?e===af?Zo++:(Zo=0,af=e):Zo=0,Lr(),null}function Hi(){if(mr!==null){var e=Oy(yl),t=Kt.transition,n=be;try{if(Kt.transition=null,be=16>e?16:e,mr===null)var r=!1;else{if(e=mr,mr=null,yl=0,ve&6)throw Error(F(331));var i=ve;for(ve|=4,$=e.current;$!==null;){var o=$,a=o.child;if($.flags&16){var s=o.deletions;if(s!==null){for(var l=0;l<s.length;l++){var u=s[l];for($=u;$!==null;){var c=$;switch(c.tag){case 0:case 11:case 15:Ko(8,c,o)}var f=c.child;if(f!==null)f.return=c,$=f;else for(;$!==null;){c=$;var d=c.sibling,y=c.return;if(zg(c),c===u){$=null;break}if(d!==null){d.return=y,$=d;break}$=y}}}var g=o.alternate;if(g!==null){var v=g.child;if(v!==null){g.child=null;do{var E=v.sibling;v.sibling=null,v=E}while(v!==null)}}$=o}}if(o.subtreeFlags&2064&&a!==null)a.return=o,$=a;else e:for(;$!==null;){if(o=$,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Ko(9,o,o.return)}var h=o.sibling;if(h!==null){h.return=o.return,$=h;break e}$=o.return}}var m=e.current;for($=m;$!==null;){a=$;var p=a.child;if(a.subtreeFlags&2064&&p!==null)p.return=a,$=p;else e:for(a=m;$!==null;){if(s=$,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:Fl(9,s)}}catch(T){Be(s,s.return,T)}if(s===a){$=null;break e}var S=s.sibling;if(S!==null){S.return=s.return,$=S;break e}$=s.return}}if(ve=i,Lr(),Tn&&typeof Tn.onPostCommitFiberRoot=="function")try{Tn.onPostCommitFiberRoot(Nl,e)}catch{}r=!0}return r}finally{be=n,Kt.transition=t}}return!1}function Wp(e,t,n){t=to(n,t),t=Og(e,t,1),e=Sr(e,t,1),t=St(),e!==null&&(ja(e,1,t),Pt(e,t))}function Be(e,t,n){if(e.tag===3)Wp(e,e,n);else for(;t!==null;){if(t.tag===3){Wp(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(_r===null||!_r.has(r))){e=to(n,e),e=Ng(t,e,1),t=Sr(t,e,1),e=St(),t!==null&&(ja(t,1,e),Pt(t,e));break}}t=t.return}}function QS(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=St(),e.pingedLanes|=e.suspendedLanes&n,st===e&&(ct&n)===n&&(nt===4||nt===3&&(ct&130023424)===ct&&500>We()-Ld?Zr(e,0):Ad|=n),Pt(e,t)}function Yg(e,t){t===0&&(e.mode&1?(t=is,is<<=1,!(is&130023424)&&(is=4194304)):t=1);var n=St();e=qn(e,t),e!==null&&(ja(e,t,n),Pt(e,n))}function $S(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Yg(e,n)}function qS(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(F(314))}r!==null&&r.delete(t),Yg(e,n)}var Xg;Xg=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Dt.current)Rt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Rt=!1,IS(e,t,n);Rt=!!(e.flags&131072)}else Rt=!1,Me&&t.flags&1048576&&eg(t,ll,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Fs(e,t),e=t.pendingProps;var i=Ki(t,gt.current);qi(t,n),i=Od(null,t,r,e,i,n);var o=Nd();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,It(r)?(o=!0,al(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Td(t),i.updater=Ml,t.stateNode=i,i._reactInternals=t,Wc(t,r,e,n),t=Xc(null,t,r,!0,o,n)):(t.tag=0,Me&&o&&vd(t),Et(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Fs(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=WS(r),e=un(r,e),i){case 0:t=Yc(null,t,r,e,n);break e;case 1:t=Fp(null,t,r,e,n);break e;case 11:t=Lp(null,t,r,e,n);break e;case 14:t=Mp(null,t,r,un(r.type,e),n);break e}throw Error(F(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:un(r,i),Yc(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:un(r,i),Fp(e,t,r,i,n);case 3:e:{if(Pg(t),e===null)throw Error(F(387));r=t.pendingProps,o=t.memoizedState,i=o.element,ag(e,t),fl(t,r,null,n);var a=t.memoizedState;if(r=a.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=to(Error(F(423)),t),t=jp(e,t,r,n,i);break e}else if(r!==i){i=to(Error(F(424)),t),t=jp(e,t,r,n,i);break e}else for(jt=wr(t.stateNode.containerInfo.firstChild),Ut=t,Me=!0,dn=null,n=ig(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Ji(),r===i){t=Hn(e,t,n);break e}Et(e,t,r,n)}t=t.child}return t;case 5:return sg(t),e===null&&$c(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,a=i.children,zc(r,i)?a=null:o!==null&&zc(r,o)&&(t.flags|=32),Ig(e,t),Et(e,t,a,n),t.child;case 6:return e===null&&$c(t),null;case 13:return Ag(e,t,n);case 4:return xd(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Zi(t,null,r,n):Et(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:un(r,i),Lp(e,t,r,i,n);case 7:return Et(e,t,t.pendingProps,n),t.child;case 8:return Et(e,t,t.pendingProps.children,n),t.child;case 12:return Et(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,a=i.value,De(ul,r._currentValue),r._currentValue=a,o!==null)if(yn(o.value,a)){if(o.children===i.children&&!Dt.current){t=Hn(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var s=o.dependencies;if(s!==null){a=o.child;for(var l=s.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=Vn(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),qc(o.return,n,t),s.lanes|=n;break}l=l.next}}else if(o.tag===10)a=o.type===t.type?null:o.child;else if(o.tag===18){if(a=o.return,a===null)throw Error(F(341));a.lanes|=n,s=a.alternate,s!==null&&(s.lanes|=n),qc(a,n,t),a=o.sibling}else a=o.child;if(a!==null)a.return=o;else for(a=o;a!==null;){if(a===t){a=null;break}if(o=a.sibling,o!==null){o.return=a.return,a=o;break}a=a.return}o=a}Et(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,qi(t,n),i=Jt(i),r=r(i),t.flags|=1,Et(e,t,r,n),t.child;case 14:return r=t.type,i=un(r,t.pendingProps),i=un(r.type,i),Mp(e,t,r,i,n);case 15:return Rg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:un(r,i),Fs(e,t),t.tag=1,It(r)?(e=!0,al(t)):e=!1,qi(t,n),Cg(t,r,i),Wc(t,r,i,n),Xc(null,t,r,!0,e,n);case 19:return Lg(e,t,n);case 22:return Dg(e,t,n)}throw Error(F(156,t.tag))};function Kg(e,t){return xy(e,t)}function HS(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Xt(e,t,n,r){return new HS(e,t,n,r)}function zd(e){return e=e.prototype,!(!e||!e.isReactComponent)}function WS(e){if(typeof e=="function")return zd(e)?1:0;if(e!=null){if(e=e.$$typeof,e===id)return 11;if(e===od)return 14}return 2}function xr(e,t){var n=e.alternate;return n===null?(n=Xt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Us(e,t,n,r,i,o){var a=2;if(r=e,typeof e=="function")zd(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case bi:return ei(n.children,i,o,t);case rd:a=8,i|=8;break;case yc:return e=Xt(12,n,t,i|2),e.elementType=yc,e.lanes=o,e;case gc:return e=Xt(13,n,t,i),e.elementType=gc,e.lanes=o,e;case Ec:return e=Xt(19,n,t,i),e.elementType=Ec,e.lanes=o,e;case sy:return zl(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case oy:a=10;break e;case ay:a=9;break e;case id:a=11;break e;case od:a=14;break e;case sr:a=16,r=null;break e}throw Error(F(130,e==null?e:typeof e,""))}return t=Xt(a,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function ei(e,t,n,r){return e=Xt(7,e,r,t),e.lanes=n,e}function zl(e,t,n,r){return e=Xt(22,e,r,t),e.elementType=sy,e.lanes=n,e.stateNode={isHidden:!1},e}function Vu(e,t,n){return e=Xt(6,e,null,t),e.lanes=n,e}function Bu(e,t,n){return t=Xt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function GS(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=_u(0),this.expirationTimes=_u(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=_u(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Ud(e,t,n,r,i,o,a,s,l){return e=new GS(e,t,n,s,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Xt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Td(o),e}function YS(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xi,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Jg(e){if(!e)return Cr;e=e._reactInternals;e:{if(di(e)!==e||e.tag!==1)throw Error(F(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(It(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(F(171))}if(e.tag===1){var n=e.type;if(It(n))return Jy(e,n,t)}return t}function Zg(e,t,n,r,i,o,a,s,l){return e=Ud(n,r,!0,e,i,o,a,s,l),e.context=Jg(null),n=e.current,r=St(),i=Tr(n),o=Vn(r,i),o.callback=t??null,Sr(n,o,i),e.current.lanes=i,ja(e,i,r),Pt(e,r),e}function Ul(e,t,n,r){var i=t.current,o=St(),a=Tr(i);return n=Jg(n),t.context===null?t.context=n:t.pendingContext=n,t=Vn(o,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Sr(i,t,a),e!==null&&(mn(e,i,a,o),As(e,i,a)),a}function El(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Gp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Vd(e,t){Gp(e,t),(e=e.alternate)&&Gp(e,t)}function XS(){return null}var e0=typeof reportError=="function"?reportError:function(e){console.error(e)};function Bd(e){this._internalRoot=e}Vl.prototype.render=Bd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(F(409));Ul(e,t,null,null)};Vl.prototype.unmount=Bd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;si(function(){Ul(null,e,null,null)}),t[$n]=null}};function Vl(e){this._internalRoot=e}Vl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Dy();e={blockedOn:null,target:e,priority:t};for(var n=0;n<dr.length&&t!==0&&t<dr[n].priority;n++);dr.splice(n,0,e),n===0&&Py(e)}};function Qd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Bl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Yp(){}function KS(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=El(a);o.call(u)}}var a=Zg(t,r,e,0,null,!1,!1,"",Yp);return e._reactRootContainer=a,e[$n]=a.current,va(e.nodeType===8?e.parentNode:e),si(),a}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var s=r;r=function(){var u=El(l);s.call(u)}}var l=Ud(e,0,!1,null,null,!1,!1,"",Yp);return e._reactRootContainer=l,e[$n]=l.current,va(e.nodeType===8?e.parentNode:e),si(function(){Ul(t,l,n,r)}),l}function Ql(e,t,n,r,i){var o=n._reactRootContainer;if(o){var a=o;if(typeof i=="function"){var s=i;i=function(){var l=El(a);s.call(l)}}Ul(t,a,e,i)}else a=KS(n,t,e,i,r);return El(a)}Ny=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Vo(t.pendingLanes);n!==0&&(ld(t,n|1),Pt(t,We()),!(ve&6)&&(no=We()+500,Lr()))}break;case 13:si(function(){var r=qn(e,1);if(r!==null){var i=St();mn(r,e,1,i)}}),Vd(e,1)}};ud=function(e){if(e.tag===13){var t=qn(e,134217728);if(t!==null){var n=St();mn(t,e,134217728,n)}Vd(e,134217728)}};Ry=function(e){if(e.tag===13){var t=Tr(e),n=qn(e,t);if(n!==null){var r=St();mn(n,e,t,r)}Vd(e,t)}};Dy=function(){return be};Iy=function(e,t){var n=be;try{return be=e,t()}finally{be=n}};Nc=function(e,t,n){switch(t){case"input":if(_c(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Pl(r);if(!i)throw Error(F(90));uy(r),_c(r,i)}}}break;case"textarea":fy(e,n);break;case"select":t=n.value,t!=null&&Vi(e,!!n.multiple,t,!1)}};gy=Md;Ey=si;var JS={usingClientEntryPoint:!1,Events:[Ua,Ni,Pl,vy,yy,Md]},Co={findFiberByHostInstance:Wr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ZS={bundleType:Co.bundleType,version:Co.version,rendererPackageName:Co.rendererPackageName,rendererConfig:Co.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Gn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=_y(e),e===null?null:e.stateNode},findFiberByHostInstance:Co.findFiberByHostInstance||XS,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ms=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ms.isDisabled&&ms.supportsFiber)try{Nl=ms.inject(ZS),Tn=ms}catch{}}Qt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=JS;Qt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Qd(t))throw Error(F(200));return YS(e,t,null,n)};Qt.createRoot=function(e,t){if(!Qd(e))throw Error(F(299));var n=!1,r="",i=e0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Ud(e,1,!1,null,null,n,!1,r,i),e[$n]=t.current,va(e.nodeType===8?e.parentNode:e),new Bd(t)};Qt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(F(188)):(e=Object.keys(e).join(","),Error(F(268,e)));return e=_y(t),e=e===null?null:e.stateNode,e};Qt.flushSync=function(e){return si(e)};Qt.hydrate=function(e,t,n){if(!Bl(t))throw Error(F(200));return Ql(null,e,t,!0,n)};Qt.hydrateRoot=function(e,t,n){if(!Qd(e))throw Error(F(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",a=e0;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Zg(t,null,e,1,n??null,i,!1,o,a),e[$n]=t.current,va(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Vl(t)};Qt.render=function(e,t,n){if(!Bl(t))throw Error(F(200));return Ql(null,e,t,!1,n)};Qt.unmountComponentAtNode=function(e){if(!Bl(e))throw Error(F(40));return e._reactRootContainer?(si(function(){Ql(null,null,e,!1,function(){e._reactRootContainer=null,e[$n]=null})}),!0):!1};Qt.unstable_batchedUpdates=Md;Qt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Bl(n))throw Error(F(200));if(e==null||e._reactInternals===void 0)throw Error(F(38));return Ql(e,t,n,!1,r)};Qt.version="18.3.1-next-f1338f8080-20240426";function t0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t0)}catch(e){console.error(e)}}t0(),ty.exports=Qt;var $d=ty.exports;const e_=Ma($d),t_=Yf({__proto__:null,default:e_},[$d]);var Xp=$d;mc.createRoot=Xp.createRoot,mc.hydrateRoot=Xp.hydrateRoot;/**
 * @remix-run/router v1.16.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function je(){return je=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},je.apply(this,arguments)}var Je;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Je||(Je={}));const Kp="popstate";function n_(e){e===void 0&&(e={});function t(r,i){let{pathname:o,search:a,hash:s}=r.location;return ba("",{pathname:o,search:a,hash:s},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:li(i)}return i_(t,n,null,e)}function fe(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function ro(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function r_(){return Math.random().toString(36).substr(2,8)}function Jp(e,t){return{usr:e.state,key:e.key,idx:t}}function ba(e,t,n,r){return n===void 0&&(n=null),je({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Mr(t):t,{state:n,key:t&&t.key||r||r_()})}function li(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Mr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function i_(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:o=!1}=r,a=i.history,s=Je.Pop,l=null,u=c();u==null&&(u=0,a.replaceState(je({},a.state,{idx:u}),""));function c(){return(a.state||{idx:null}).idx}function f(){s=Je.Pop;let E=c(),h=E==null?null:E-u;u=E,l&&l({action:s,location:v.location,delta:h})}function d(E,h){s=Je.Push;let m=ba(v.location,E,h);u=c()+1;let p=Jp(m,u),S=v.createHref(m);try{a.pushState(p,"",S)}catch(T){if(T instanceof DOMException&&T.name==="DataCloneError")throw T;i.location.assign(S)}o&&l&&l({action:s,location:v.location,delta:1})}function y(E,h){s=Je.Replace;let m=ba(v.location,E,h);u=c();let p=Jp(m,u),S=v.createHref(m);a.replaceState(p,"",S),o&&l&&l({action:s,location:v.location,delta:0})}function g(E){let h=i.location.origin!=="null"?i.location.origin:i.location.href,m=typeof E=="string"?E:li(E);return m=m.replace(/ $/,"%20"),fe(h,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,h)}let v={get action(){return s},get location(){return e(i,a)},listen(E){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(Kp,f),l=E,()=>{i.removeEventListener(Kp,f),l=null}},createHref(E){return t(i,E)},createURL:g,encodeLocation(E){let h=g(E);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:d,replace:y,go(E){return a.go(E)}};return v}var Le;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Le||(Le={}));const o_=new Set(["lazy","caseSensitive","path","id","index","children"]);function a_(e){return e.index===!0}function uf(e,t,n,r){return n===void 0&&(n=[]),r===void 0&&(r={}),e.map((i,o)=>{let a=[...n,o],s=typeof i.id=="string"?i.id:a.join("-");if(fe(i.index!==!0||!i.children,"Cannot specify children on an index route"),fe(!r[s],'Found a route id collision on id "'+s+`".  Route id's must be globally unique within Data Router usages`),a_(i)){let l=je({},i,t(i),{id:s});return r[s]=l,l}else{let l=je({},i,t(i),{id:s,children:void 0});return r[s]=l,i.children&&(l.children=uf(i.children,t,a,r)),l}})}function Mi(e,t,n){n===void 0&&(n="/");let r=typeof t=="string"?Mr(t):t,i=fo(r.pathname||"/",n);if(i==null)return null;let o=n0(e);l_(o);let a=null;for(let s=0;a==null&&s<o.length;++s){let l=w_(i);a=y_(o[s],l)}return a}function s_(e,t){let{route:n,pathname:r,params:i}=e;return{id:n.id,pathname:r,params:i,data:t[n.id],handle:n.handle}}function n0(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(o,a,s)=>{let l={relativePath:s===void 0?o.path||"":s,caseSensitive:o.caseSensitive===!0,childrenIndex:a,route:o};l.relativePath.startsWith("/")&&(fe(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=Bn([r,l.relativePath]),c=n.concat(l);o.children&&o.children.length>0&&(fe(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),n0(o.children,t,c,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:m_(u,o.index),routesMeta:c})};return e.forEach((o,a)=>{var s;if(o.path===""||!((s=o.path)!=null&&s.includes("?")))i(o,a);else for(let l of r0(o.path))i(o,a,l)}),t}function r0(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return i?[o,""]:[o];let a=r0(r.join("/")),s=[];return s.push(...a.map(l=>l===""?o:[o,l].join("/"))),i&&s.push(...a),s.map(l=>e.startsWith("/")&&l===""?"/":l)}function l_(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:v_(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const u_=/^:[\w-]+$/,c_=3,f_=2,d_=1,h_=10,p_=-2,Zp=e=>e==="*";function m_(e,t){let n=e.split("/"),r=n.length;return n.some(Zp)&&(r+=p_),t&&(r+=f_),n.filter(i=>!Zp(i)).reduce((i,o)=>i+(u_.test(o)?c_:o===""?d_:h_),r)}function v_(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function y_(e,t){let{routesMeta:n}=e,r={},i="/",o=[];for(let a=0;a<n.length;++a){let s=n[a],l=a===n.length-1,u=i==="/"?t:t.slice(i.length)||"/",c=g_({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},u);if(!c)return null;Object.assign(r,c.params);let f=s.route;o.push({params:r,pathname:Bn([i,c.pathname]),pathnameBase:T_(Bn([i,c.pathnameBase])),route:f}),c.pathnameBase!=="/"&&(i=Bn([i,c.pathnameBase]))}return o}function g_(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=E_(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let o=i[0],a=o.replace(/(.)\/+$/,"$1"),s=i.slice(1);return{params:r.reduce((u,c,f)=>{let{paramName:d,isOptional:y}=c;if(d==="*"){let v=s[f]||"";a=o.slice(0,o.length-v.length).replace(/(.)\/+$/,"$1")}const g=s[f];return y&&!g?u[d]=void 0:u[d]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:a,pattern:e}}function E_(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),ro(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,s,l)=>(r.push({paramName:s,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function w_(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ro(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function fo(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function S_(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?Mr(e):e;return{pathname:n?n.startsWith("/")?n:__(n,t):t,search:x_(r),hash:b_(i)}}function __(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Qu(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function i0(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function qd(e,t){let n=i0(e);return t?n.map((r,i)=>i===e.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Hd(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=Mr(e):(i=je({},e),fe(!i.pathname||!i.pathname.includes("?"),Qu("?","pathname","search",i)),fe(!i.pathname||!i.pathname.includes("#"),Qu("#","pathname","hash",i)),fe(!i.search||!i.search.includes("#"),Qu("#","search","hash",i)));let o=e===""||i.pathname==="",a=o?"/":i.pathname,s;if(a==null)s=n;else{let f=t.length-1;if(!r&&a.startsWith("..")){let d=a.split("/");for(;d[0]==="..";)d.shift(),f-=1;i.pathname=d.join("/")}s=f>=0?t[f]:"/"}let l=S_(i,s),u=a&&a!=="/"&&a.endsWith("/"),c=(o||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const Bn=e=>e.join("/").replace(/\/\/+/g,"/"),T_=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),x_=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,b_=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class Wd{constructor(t,n,r,i){i===void 0&&(i=!1),this.status=t,this.statusText=n||"",this.internal=i,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}}function Gd(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const o0=["post","put","patch","delete"],k_=new Set(o0),C_=["get",...o0],O_=new Set(C_),N_=new Set([301,302,303,307,308]),R_=new Set([307,308]),$u={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},D_={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Oo={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Yd=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,I_=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),a0="remix-router-transitions";function P_(e){const t=e.window?e.window:typeof window<"u"?window:void 0,n=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",r=!n;fe(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let i;if(e.mapRouteProperties)i=e.mapRouteProperties;else if(e.detectErrorBoundary){let x=e.detectErrorBoundary;i=C=>({hasErrorBoundary:x(C)})}else i=I_;let o={},a=uf(e.routes,i,void 0,o),s,l=e.basename||"/",u=e.unstable_dataStrategy||F_,c=je({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,unstable_skipActionErrorRevalidation:!1},e.future),f=null,d=new Set,y=null,g=null,v=null,E=e.hydrationData!=null,h=Mi(a,e.history.location,l),m=null;if(h==null){let x=Ht(404,{pathname:e.history.location.pathname}),{matches:C,route:D}=um(a);h=C,m={[D.id]:x}}let p,S=h.some(x=>x.route.lazy),T=h.some(x=>x.route.loader);if(S)p=!1;else if(!T)p=!0;else if(c.v7_partialHydration){let x=e.hydrationData?e.hydrationData.loaderData:null,C=e.hydrationData?e.hydrationData.errors:null,D=j=>j.route.loader?typeof j.route.loader=="function"&&j.route.loader.hydrate===!0?!1:x&&x[j.route.id]!==void 0||C&&C[j.route.id]!==void 0:!0;if(C){let j=h.findIndex(B=>C[B.route.id]!==void 0);p=h.slice(0,j+1).every(D)}else p=h.every(D)}else p=e.hydrationData!=null;let O,w={historyAction:e.history.action,location:e.history.location,matches:h,initialized:p,navigation:$u,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||m,fetchers:new Map,blockers:new Map},k=Je.Pop,I=!1,P,K=!1,se=new Map,ee=null,le=!1,Ae=!1,rt=[],Fe=[],M=new Map,V=0,U=-1,ae=new Map,q=new Set,Ee=new Map,te=new Map,Se=new Set,Ce=new Map,qe=new Map,Rn=!1;function Ga(){if(f=e.history.listen(x=>{let{action:C,location:D,delta:j}=x;if(Rn){Rn=!1;return}ro(qe.size===0||j!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let B=Zn({currentLocation:w.location,nextLocation:D,historyAction:C});if(B&&j!=null){Rn=!0,e.history.go(j*-1),Ei(B,{state:"blocked",location:D,proceed(){Ei(B,{state:"proceeding",proceed:void 0,reset:void 0,location:D}),e.history.go(j)},reset(){let oe=new Map(w.blockers);oe.set(B,Oo),ht({blockers:oe})}});return}return Ct(C,D)}),n){Y_(t,se);let x=()=>X_(t,se);t.addEventListener("pagehide",x),ee=()=>t.removeEventListener("pagehide",x)}return w.initialized||Ct(Je.Pop,w.location,{initialHydration:!0}),O}function Ya(){f&&f(),ee&&ee(),d.clear(),P&&P.abort(),w.fetchers.forEach((x,C)=>Jn(C)),w.blockers.forEach((x,C)=>gi(C))}function Xn(x){return d.add(x),()=>d.delete(x)}function ht(x,C){C===void 0&&(C={}),w=je({},w,x);let D=[],j=[];c.v7_fetcherPersist&&w.fetchers.forEach((B,oe)=>{B.state==="idle"&&(Se.has(oe)?j.push(oe):D.push(oe))}),[...d].forEach(B=>B(w,{deletedFetchers:j,unstable_viewTransitionOpts:C.viewTransitionOpts,unstable_flushSync:C.flushSync===!0})),c.v7_fetcherPersist&&(D.forEach(B=>w.fetchers.delete(B)),j.forEach(B=>Jn(B)))}function Lt(x,C,D){var j,B;let{flushSync:oe}=D===void 0?{}:D,J=w.actionData!=null&&w.navigation.formMethod!=null&&fn(w.navigation.formMethod)&&w.navigation.state==="loading"&&((j=x.state)==null?void 0:j._isRedirect)!==!0,H;C.actionData?Object.keys(C.actionData).length>0?H=C.actionData:H=null:J?H=w.actionData:H=null;let Z=C.loaderData?sm(w.loaderData,C.loaderData,C.matches||[],C.errors):w.loaderData,re=w.blockers;re.size>0&&(re=new Map(re),re.forEach((ie,Oe)=>re.set(Oe,Oo)));let Ge=I===!0||w.navigation.formMethod!=null&&fn(w.navigation.formMethod)&&((B=x.state)==null?void 0:B._isRedirect)!==!0;s&&(a=s,s=void 0),le||k===Je.Pop||(k===Je.Push?e.history.push(x,x.state):k===Je.Replace&&e.history.replace(x,x.state));let Ye;if(k===Je.Pop){let ie=se.get(w.location.pathname);ie&&ie.has(x.pathname)?Ye={currentLocation:w.location,nextLocation:x}:se.has(x.pathname)&&(Ye={currentLocation:x,nextLocation:w.location})}else if(K){let ie=se.get(w.location.pathname);ie?ie.add(x.pathname):(ie=new Set([x.pathname]),se.set(w.location.pathname,ie)),Ye={currentLocation:w.location,nextLocation:x}}ht(je({},C,{actionData:H,loaderData:Z,historyAction:k,location:x,initialized:!0,navigation:$u,revalidation:"idle",restoreScrollPosition:Eo(x,C.matches||w.matches),preventScrollReset:Ge,blockers:re}),{viewTransitionOpts:Ye,flushSync:oe===!0}),k=Je.Pop,I=!1,K=!1,le=!1,Ae=!1,rt=[],Fe=[]}async function mi(x,C){if(typeof x=="number"){e.history.go(x);return}let D=cf(w.location,w.matches,l,c.v7_prependBasename,x,c.v7_relativeSplatPath,C==null?void 0:C.fromRouteId,C==null?void 0:C.relative),{path:j,submission:B,error:oe}=em(c.v7_normalizeFormMethod,!1,D,C),J=w.location,H=ba(w.location,j,C&&C.state);H=je({},H,e.history.encodeLocation(H));let Z=C&&C.replace!=null?C.replace:void 0,re=Je.Push;Z===!0?re=Je.Replace:Z===!1||B!=null&&fn(B.formMethod)&&B.formAction===w.location.pathname+w.location.search&&(re=Je.Replace);let Ge=C&&"preventScrollReset"in C?C.preventScrollReset===!0:void 0,Ye=(C&&C.unstable_flushSync)===!0,ie=Zn({currentLocation:J,nextLocation:H,historyAction:re});if(ie){Ei(ie,{state:"blocked",location:H,proceed(){Ei(ie,{state:"proceeding",proceed:void 0,reset:void 0,location:H}),mi(x,C)},reset(){let Oe=new Map(w.blockers);Oe.set(ie,Oo),ht({blockers:Oe})}});return}return await Ct(re,H,{submission:B,pendingError:oe,preventScrollReset:Ge,replace:C&&C.replace,enableViewTransition:C&&C.unstable_viewTransition,flushSync:Ye})}function mo(){if(En(),ht({revalidation:"loading"}),w.navigation.state!=="submitting"){if(w.navigation.state==="idle"){Ct(w.historyAction,w.location,{startUninterruptedRevalidation:!0});return}Ct(k||w.historyAction,w.navigation.location,{overrideNavigation:w.navigation})}}async function Ct(x,C,D){P&&P.abort(),P=null,k=x,le=(D&&D.startUninterruptedRevalidation)===!0,er(w.location,w.matches),I=(D&&D.preventScrollReset)===!0,K=(D&&D.enableViewTransition)===!0;let j=s||a,B=D&&D.overrideNavigation,oe=Mi(j,C,l),J=(D&&D.flushSync)===!0;if(!oe){let ie=Ht(404,{pathname:C.pathname}),{matches:Oe,route:ke}=um(j);go(),Lt(C,{matches:Oe,loaderData:{},errors:{[ke.id]:ie}},{flushSync:J});return}if(w.initialized&&!Ae&&Q_(w.location,C)&&!(D&&D.submission&&fn(D.submission.formMethod))){Lt(C,{matches:oe},{flushSync:J});return}P=new AbortController;let H=Si(e.history,C,P.signal,D&&D.submission),Z;if(D&&D.pendingError)Z=[ea(oe).route.id,{type:Le.error,error:D.pendingError}];else if(D&&D.submission&&fn(D.submission.formMethod)){let ie=await vi(H,C,D.submission,oe,{replace:D.replace,flushSync:J});if(ie.shortCircuited)return;Z=ie.pendingActionResult,B=qu(C,D.submission),J=!1,H=Si(e.history,H.url,H.signal)}let{shortCircuited:re,loaderData:Ge,errors:Ye}=await yi(H,C,oe,B,D&&D.submission,D&&D.fetcherSubmission,D&&D.replace,D&&D.initialHydration===!0,J,Z);re||(P=null,Lt(C,je({matches:oe},lm(Z),{loaderData:Ge,errors:Ye})))}async function vi(x,C,D,j,B){B===void 0&&(B={}),En();let oe=W_(C,D);ht({navigation:oe},{flushSync:B.flushSync===!0});let J,H=df(j,C);if(!H.route.action&&!H.route.lazy)J={type:Le.error,error:Ht(405,{method:x.method,pathname:C.pathname,routeId:H.route.id})};else if(J=(await nn("action",x,[H],j))[0],x.signal.aborted)return{shortCircuited:!0};if(Kr(J)){let Z;return B&&B.replace!=null?Z=B.replace:Z=im(J.response.headers.get("Location"),new URL(x.url),l)===w.location.pathname+w.location.search,await Dn(x,J,{submission:D,replace:Z}),{shortCircuited:!0}}if(Xr(J))throw Ht(400,{type:"defer-action"});if(Yt(J)){let Z=ea(j,H.route.id);return(B&&B.replace)!==!0&&(k=Je.Push),{pendingActionResult:[Z.route.id,J]}}return{pendingActionResult:[H.route.id,J]}}async function yi(x,C,D,j,B,oe,J,H,Z,re){let Ge=j||qu(C,B),Ye=B||oe||dm(Ge),ie=s||a,[Oe,ke]=tm(e.history,w,D,Ye,C,c.v7_partialHydration&&H===!0,c.unstable_skipActionErrorRevalidation,Ae,rt,Fe,Se,Ee,q,ie,l,re);if(go(z=>!(D&&D.some(ye=>ye.route.id===z))||Oe&&Oe.some(ye=>ye.route.id===z)),U=++V,Oe.length===0&&ke.length===0){let z=qt();return Lt(C,je({matches:D,loaderData:{},errors:re&&Yt(re[1])?{[re[0]]:re[1].error}:null},lm(re),z?{fetchers:new Map(w.fetchers)}:{}),{flushSync:Z}),{shortCircuited:!0}}if(!le&&(!c.v7_partialHydration||!H)){ke.forEach(ye=>{let Te=w.fetchers.get(ye.key),ge=No(void 0,Te?Te.data:void 0);w.fetchers.set(ye.key,ge)});let z;re&&!Yt(re[1])?z={[re[0]]:re[1].data}:w.actionData&&(Object.keys(w.actionData).length===0?z=null:z=w.actionData),ht(je({navigation:Ge},z!==void 0?{actionData:z}:{},ke.length>0?{fetchers:new Map(w.fetchers)}:{}),{flushSync:Z})}ke.forEach(z=>{M.has(z.key)&&Mt(z.key),z.controller&&M.set(z.key,z.controller)});let tr=()=>ke.forEach(z=>Mt(z.key));P&&P.signal.addEventListener("abort",tr);let{loaderResults:on,fetcherResults:An}=await In(w.matches,D,Oe,ke,x);if(x.signal.aborted)return{shortCircuited:!0};P&&P.signal.removeEventListener("abort",tr),ke.forEach(z=>M.delete(z.key));let Ln=cm([...on,...An]);if(Ln){if(Ln.idx>=Oe.length){let z=ke[Ln.idx-Oe.length].key;q.add(z)}return await Dn(x,Ln.result,{replace:J}),{shortCircuited:!0}}let{loaderData:nr,errors:Q}=am(w,D,Oe,on,re,ke,An,Ce);Ce.forEach((z,ye)=>{z.subscribe(Te=>{(Te||z.done)&&Ce.delete(ye)})}),c.v7_partialHydration&&H&&w.errors&&Object.entries(w.errors).filter(z=>{let[ye]=z;return!Oe.some(Te=>Te.route.id===ye)}).forEach(z=>{let[ye,Te]=z;Q=Object.assign(Q||{},{[ye]:Te})});let _=qt(),L=Pn(U),W=_||L||ke.length>0;return je({loaderData:nr,errors:Q},W?{fetchers:new Map(w.fetchers)}:{})}function Xa(x,C,D,j){if(r)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");M.has(x)&&Mt(x);let B=(j&&j.unstable_flushSync)===!0,oe=s||a,J=cf(w.location,w.matches,l,c.v7_prependBasename,D,c.v7_relativeSplatPath,C,j==null?void 0:j.relative),H=Mi(oe,J,l);if(!H){Kn(x,C,Ht(404,{pathname:J}),{flushSync:B});return}let{path:Z,submission:re,error:Ge}=em(c.v7_normalizeFormMethod,!0,J,j);if(Ge){Kn(x,C,Ge,{flushSync:B});return}let Ye=df(H,Z);if(I=(j&&j.preventScrollReset)===!0,re&&fn(re.formMethod)){Ka(x,C,Z,Ye,H,B,re);return}Ee.set(x,{routeId:C,path:Z}),pu(x,C,Z,Ye,H,B,re)}async function Ka(x,C,D,j,B,oe,J){if(En(),Ee.delete(x),!j.route.action&&!j.route.lazy){let ge=Ht(405,{method:J.formMethod,pathname:D,routeId:C});Kn(x,C,ge,{flushSync:oe});return}let H=w.fetchers.get(x);rn(x,G_(J,H),{flushSync:oe});let Z=new AbortController,re=Si(e.history,D,Z.signal,J);M.set(x,Z);let Ge=V,ie=(await nn("action",re,[j],B))[0];if(re.signal.aborted){M.get(x)===Z&&M.delete(x);return}if(c.v7_fetcherPersist&&Se.has(x)){if(Kr(ie)||Yt(ie)){rn(x,or(void 0));return}}else{if(Kr(ie))if(M.delete(x),U>Ge){rn(x,or(void 0));return}else return q.add(x),rn(x,No(J)),Dn(re,ie,{fetcherSubmission:J});if(Yt(ie)){Kn(x,C,ie.error);return}}if(Xr(ie))throw Ht(400,{type:"defer-action"});let Oe=w.navigation.location||w.location,ke=Si(e.history,Oe,Z.signal),tr=s||a,on=w.navigation.state!=="idle"?Mi(tr,w.navigation.location,l):w.matches;fe(on,"Didn't find any matches after fetcher action");let An=++V;ae.set(x,An);let Ln=No(J,ie.data);w.fetchers.set(x,Ln);let[nr,Q]=tm(e.history,w,on,J,Oe,!1,c.unstable_skipActionErrorRevalidation,Ae,rt,Fe,Se,Ee,q,tr,l,[j.route.id,ie]);Q.filter(ge=>ge.key!==x).forEach(ge=>{let Xe=ge.key,rr=w.fetchers.get(Xe),pt=No(void 0,rr?rr.data:void 0);w.fetchers.set(Xe,pt),M.has(Xe)&&Mt(Xe),ge.controller&&M.set(Xe,ge.controller)}),ht({fetchers:new Map(w.fetchers)});let _=()=>Q.forEach(ge=>Mt(ge.key));Z.signal.addEventListener("abort",_);let{loaderResults:L,fetcherResults:W}=await In(w.matches,on,nr,Q,ke);if(Z.signal.aborted)return;Z.signal.removeEventListener("abort",_),ae.delete(x),M.delete(x),Q.forEach(ge=>M.delete(ge.key));let z=cm([...L,...W]);if(z){if(z.idx>=nr.length){let ge=Q[z.idx-nr.length].key;q.add(ge)}return Dn(ke,z.result)}let{loaderData:ye,errors:Te}=am(w,w.matches,nr,L,void 0,Q,W,Ce);if(w.fetchers.has(x)){let ge=or(ie.data);w.fetchers.set(x,ge)}Pn(An),w.navigation.state==="loading"&&An>U?(fe(k,"Expected pending action"),P&&P.abort(),Lt(w.navigation.location,{matches:on,loaderData:ye,errors:Te,fetchers:new Map(w.fetchers)})):(ht({errors:Te,loaderData:sm(w.loaderData,ye,on,Te),fetchers:new Map(w.fetchers)}),Ae=!1)}async function pu(x,C,D,j,B,oe,J){let H=w.fetchers.get(x);rn(x,No(J,H?H.data:void 0),{flushSync:oe});let Z=new AbortController,re=Si(e.history,D,Z.signal);M.set(x,Z);let Ge=V,ie=(await nn("loader",re,[j],B))[0];if(Xr(ie)&&(ie=await c0(ie,re.signal,!0)||ie),M.get(x)===Z&&M.delete(x),!re.signal.aborted){if(Se.has(x)){rn(x,or(void 0));return}if(Kr(ie))if(U>Ge){rn(x,or(void 0));return}else{q.add(x),await Dn(re,ie);return}if(Yt(ie)){Kn(x,C,ie.error);return}fe(!Xr(ie),"Unhandled fetcher deferred data"),rn(x,or(ie.data))}}async function Dn(x,C,D){let{submission:j,fetcherSubmission:B,replace:oe}=D===void 0?{}:D;C.response.headers.has("X-Remix-Revalidate")&&(Ae=!0);let J=C.response.headers.get("Location");fe(J,"Expected a Location header on the redirect Response"),J=im(J,new URL(x.url),l);let H=ba(w.location,J,{_isRedirect:!0});if(n){let Oe=!1;if(C.response.headers.has("X-Remix-Reload-Document"))Oe=!0;else if(Yd.test(J)){const ke=e.history.createURL(J);Oe=ke.origin!==t.location.origin||fo(ke.pathname,l)==null}if(Oe){oe?t.location.replace(J):t.location.assign(J);return}}P=null;let Z=oe===!0?Je.Replace:Je.Push,{formMethod:re,formAction:Ge,formEncType:Ye}=w.navigation;!j&&!B&&re&&Ge&&Ye&&(j=dm(w.navigation));let ie=j||B;if(R_.has(C.response.status)&&ie&&fn(ie.formMethod))await Ct(Z,H,{submission:je({},ie,{formAction:J}),preventScrollReset:I});else{let Oe=qu(H,j);await Ct(Z,H,{overrideNavigation:Oe,fetcherSubmission:B,preventScrollReset:I})}}async function nn(x,C,D,j){try{let B=await j_(u,x,C,D,j,o,i);return await Promise.all(B.map((oe,J)=>{if($_(oe)){let H=oe.result;return{type:Le.redirect,response:V_(H,C,D[J].route.id,j,l,c.v7_relativeSplatPath)}}return U_(oe)}))}catch(B){return D.map(()=>({type:Le.error,error:B}))}}async function In(x,C,D,j,B){let[oe,...J]=await Promise.all([D.length?nn("loader",B,D,C):[],...j.map(H=>{if(H.matches&&H.match&&H.controller){let Z=Si(e.history,H.path,H.controller.signal);return nn("loader",Z,[H.match],H.matches).then(re=>re[0])}else return Promise.resolve({type:Le.error,error:Ht(404,{pathname:H.path})})})]);return await Promise.all([fm(x,D,oe,oe.map(()=>B.signal),!1,w.loaderData),fm(x,j.map(H=>H.match),J,j.map(H=>H.controller?H.controller.signal:null),!0)]),{loaderResults:oe,fetcherResults:J}}function En(){Ae=!0,rt.push(...go()),Ee.forEach((x,C)=>{M.has(C)&&(Fe.push(C),Mt(C))})}function rn(x,C,D){D===void 0&&(D={}),w.fetchers.set(x,C),ht({fetchers:new Map(w.fetchers)},{flushSync:(D&&D.flushSync)===!0})}function Kn(x,C,D,j){j===void 0&&(j={});let B=ea(w.matches,C);Jn(x),ht({errors:{[B.route.id]:D},fetchers:new Map(w.fetchers)},{flushSync:(j&&j.flushSync)===!0})}function vo(x){return c.v7_fetcherPersist&&(te.set(x,(te.get(x)||0)+1),Se.has(x)&&Se.delete(x)),w.fetchers.get(x)||D_}function Jn(x){let C=w.fetchers.get(x);M.has(x)&&!(C&&C.state==="loading"&&ae.has(x))&&Mt(x),Ee.delete(x),ae.delete(x),q.delete(x),Se.delete(x),w.fetchers.delete(x)}function Ja(x){if(c.v7_fetcherPersist){let C=(te.get(x)||0)-1;C<=0?(te.delete(x),Se.add(x)):te.set(x,C)}else Jn(x);ht({fetchers:new Map(w.fetchers)})}function Mt(x){let C=M.get(x);fe(C,"Expected fetch controller: "+x),C.abort(),M.delete(x)}function Ur(x){for(let C of x){let D=vo(C),j=or(D.data);w.fetchers.set(C,j)}}function qt(){let x=[],C=!1;for(let D of q){let j=w.fetchers.get(D);fe(j,"Expected fetcher: "+D),j.state==="loading"&&(q.delete(D),x.push(D),C=!0)}return Ur(x),C}function Pn(x){let C=[];for(let[D,j]of ae)if(j<x){let B=w.fetchers.get(D);fe(B,"Expected fetcher: "+D),B.state==="loading"&&(Mt(D),ae.delete(D),C.push(D))}return Ur(C),C.length>0}function yo(x,C){let D=w.blockers.get(x)||Oo;return qe.get(x)!==C&&qe.set(x,C),D}function gi(x){w.blockers.delete(x),qe.delete(x)}function Ei(x,C){let D=w.blockers.get(x)||Oo;fe(D.state==="unblocked"&&C.state==="blocked"||D.state==="blocked"&&C.state==="blocked"||D.state==="blocked"&&C.state==="proceeding"||D.state==="blocked"&&C.state==="unblocked"||D.state==="proceeding"&&C.state==="unblocked","Invalid blocker state transition: "+D.state+" -> "+C.state);let j=new Map(w.blockers);j.set(x,C),ht({blockers:j})}function Zn(x){let{currentLocation:C,nextLocation:D,historyAction:j}=x;if(qe.size===0)return;qe.size>1&&ro(!1,"A router only supports one blocker at a time");let B=Array.from(qe.entries()),[oe,J]=B[B.length-1],H=w.blockers.get(oe);if(!(H&&H.state==="proceeding")&&J({currentLocation:C,nextLocation:D,historyAction:j}))return oe}function go(x){let C=[];return Ce.forEach((D,j)=>{(!x||x(j))&&(D.cancel(),C.push(j),Ce.delete(j))}),C}function mu(x,C,D){if(y=x,v=C,g=D||null,!E&&w.navigation===$u){E=!0;let j=Eo(w.location,w.matches);j!=null&&ht({restoreScrollPosition:j})}return()=>{y=null,v=null,g=null}}function He(x,C){return g&&g(x,C.map(j=>s_(j,w.loaderData)))||x.key}function er(x,C){if(y&&v){let D=He(x,C);y[D]=v()}}function Eo(x,C){if(y){let D=He(x,C),j=y[D];if(typeof j=="number")return j}return null}function vu(x){o={},s=uf(x,i,void 0,o)}return O={get basename(){return l},get future(){return c},get state(){return w},get routes(){return a},get window(){return t},initialize:Ga,subscribe:Xn,enableScrollRestoration:mu,navigate:mi,fetch:Xa,revalidate:mo,createHref:x=>e.history.createHref(x),encodeLocation:x=>e.history.encodeLocation(x),getFetcher:vo,deleteFetcher:Ja,dispose:Ya,getBlocker:yo,deleteBlocker:gi,_internalFetchControllers:M,_internalActiveDeferreds:Ce,_internalSetRoutes:vu},O}function A_(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function cf(e,t,n,r,i,o,a,s){let l,u;if(a){l=[];for(let f of t)if(l.push(f),f.route.id===a){u=f;break}}else l=t,u=t[t.length-1];let c=Hd(i||".",qd(l,o),fo(e.pathname,n)||e.pathname,s==="path");return i==null&&(c.search=e.search,c.hash=e.hash),(i==null||i===""||i===".")&&u&&u.route.index&&!Xd(c.search)&&(c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index"),r&&n!=="/"&&(c.pathname=c.pathname==="/"?n:Bn([n,c.pathname])),li(c)}function em(e,t,n,r){if(!r||!A_(r))return{path:n};if(r.formMethod&&!H_(r.formMethod))return{path:n,error:Ht(405,{method:r.formMethod})};let i=()=>({path:n,error:Ht(400,{type:"invalid-body"})}),o=r.formMethod||"get",a=e?o.toUpperCase():o.toLowerCase(),s=l0(n);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!fn(a))return i();let d=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((y,g)=>{let[v,E]=g;return""+y+v+"="+E+`
`},""):String(r.body);return{path:n,submission:{formMethod:a,formAction:s,formEncType:r.formEncType,formData:void 0,json:void 0,text:d}}}else if(r.formEncType==="application/json"){if(!fn(a))return i();try{let d=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:n,submission:{formMethod:a,formAction:s,formEncType:r.formEncType,formData:void 0,json:d,text:void 0}}}catch{return i()}}}fe(typeof FormData=="function","FormData is not available in this environment");let l,u;if(r.formData)l=ff(r.formData),u=r.formData;else if(r.body instanceof FormData)l=ff(r.body),u=r.body;else if(r.body instanceof URLSearchParams)l=r.body,u=om(l);else if(r.body==null)l=new URLSearchParams,u=new FormData;else try{l=new URLSearchParams(r.body),u=om(l)}catch{return i()}let c={formMethod:a,formAction:s,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:u,json:void 0,text:void 0};if(fn(c.formMethod))return{path:n,submission:c};let f=Mr(n);return t&&f.search&&Xd(f.search)&&l.append("index",""),f.search="?"+l,{path:li(f),submission:c}}function L_(e,t){let n=e;if(t){let r=e.findIndex(i=>i.route.id===t);r>=0&&(n=e.slice(0,r))}return n}function tm(e,t,n,r,i,o,a,s,l,u,c,f,d,y,g,v){let E=v?Yt(v[1])?v[1].error:v[1].data:void 0,h=e.createURL(t.location),m=e.createURL(i),p=v&&Yt(v[1])?v[0]:void 0,S=p?L_(n,p):n,T=v?v[1].statusCode:void 0,O=a&&T&&T>=400,w=S.filter((I,P)=>{let{route:K}=I;if(K.lazy)return!0;if(K.loader==null)return!1;if(o)return typeof K.loader!="function"||K.loader.hydrate?!0:t.loaderData[K.id]===void 0&&(!t.errors||t.errors[K.id]===void 0);if(M_(t.loaderData,t.matches[P],I)||l.some(le=>le===I.route.id))return!0;let se=t.matches[P],ee=I;return nm(I,je({currentUrl:h,currentParams:se.params,nextUrl:m,nextParams:ee.params},r,{actionResult:E,unstable_actionStatus:T,defaultShouldRevalidate:O?!1:s||h.pathname+h.search===m.pathname+m.search||h.search!==m.search||s0(se,ee)}))}),k=[];return f.forEach((I,P)=>{if(o||!n.some(Ae=>Ae.route.id===I.routeId)||c.has(P))return;let K=Mi(y,I.path,g);if(!K){k.push({key:P,routeId:I.routeId,path:I.path,matches:null,match:null,controller:null});return}let se=t.fetchers.get(P),ee=df(K,I.path),le=!1;d.has(P)?le=!1:u.includes(P)?le=!0:se&&se.state!=="idle"&&se.data===void 0?le=s:le=nm(ee,je({currentUrl:h,currentParams:t.matches[t.matches.length-1].params,nextUrl:m,nextParams:n[n.length-1].params},r,{actionResult:E,unstable_actionStatus:T,defaultShouldRevalidate:O?!1:s})),le&&k.push({key:P,routeId:I.routeId,path:I.path,matches:K,match:ee,controller:new AbortController})}),[w,k]}function M_(e,t,n){let r=!t||n.route.id!==t.route.id,i=e[n.route.id]===void 0;return r||i}function s0(e,t){let n=e.route.path;return e.pathname!==t.pathname||n!=null&&n.endsWith("*")&&e.params["*"]!==t.params["*"]}function nm(e,t){if(e.route.shouldRevalidate){let n=e.route.shouldRevalidate(t);if(typeof n=="boolean")return n}return t.defaultShouldRevalidate}async function rm(e,t,n){if(!e.lazy)return;let r=await e.lazy();if(!e.lazy)return;let i=n[e.id];fe(i,"No route found in manifest");let o={};for(let a in r){let l=i[a]!==void 0&&a!=="hasErrorBoundary";ro(!l,'Route "'+i.id+'" has a static property "'+a+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+a+'" will be ignored.')),!l&&!o_.has(a)&&(o[a]=r[a])}Object.assign(i,o),Object.assign(i,je({},t(i),{lazy:void 0}))}function F_(e){return Promise.all(e.matches.map(t=>t.resolve()))}async function j_(e,t,n,r,i,o,a,s){let l=r.reduce((f,d)=>f.add(d.route.id),new Set),u=new Set,c=await e({matches:i.map(f=>{let d=l.has(f.route.id);return je({},f,{shouldLoad:d,resolve:g=>(u.add(f.route.id),d?z_(t,n,f,o,a,g,s):Promise.resolve({type:Le.data,result:void 0}))})}),request:n,params:i[0].params,context:s});return i.forEach(f=>fe(u.has(f.route.id),'`match.resolve()` was not called for route id "'+f.route.id+'". You must call `match.resolve()` on every match passed to `dataStrategy` to ensure all routes are properly loaded.')),c.filter((f,d)=>l.has(i[d].route.id))}async function z_(e,t,n,r,i,o,a){let s,l,u=c=>{let f,d=new Promise((v,E)=>f=E);l=()=>f(),t.signal.addEventListener("abort",l);let y=v=>typeof c!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: '+n.route.id+"]"))):c({request:t,params:n.params,context:a},...v!==void 0?[v]:[]),g;return o?g=o(v=>y(v)):g=(async()=>{try{return{type:"data",result:await y()}}catch(v){return{type:"error",result:v}}})(),Promise.race([g,d])};try{let c=n.route[e];if(n.route.lazy)if(c){let f,[d]=await Promise.all([u(c).catch(y=>{f=y}),rm(n.route,i,r)]);if(f!==void 0)throw f;s=d}else if(await rm(n.route,i,r),c=n.route[e],c)s=await u(c);else if(e==="action"){let f=new URL(t.url),d=f.pathname+f.search;throw Ht(405,{method:t.method,pathname:d,routeId:n.route.id})}else return{type:Le.data,result:void 0};else if(c)s=await u(c);else{let f=new URL(t.url),d=f.pathname+f.search;throw Ht(404,{pathname:d})}fe(s.result!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+n.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(c){return{type:Le.error,result:c}}finally{l&&t.signal.removeEventListener("abort",l)}return s}async function U_(e){let{result:t,type:n,status:r}=e;if(u0(t)){let a;try{let s=t.headers.get("Content-Type");s&&/\bapplication\/json\b/.test(s)?t.body==null?a=null:a=await t.json():a=await t.text()}catch(s){return{type:Le.error,error:s}}return n===Le.error?{type:Le.error,error:new Wd(t.status,t.statusText,a),statusCode:t.status,headers:t.headers}:{type:Le.data,data:a,statusCode:t.status,headers:t.headers}}if(n===Le.error)return{type:Le.error,error:t,statusCode:Gd(t)?t.status:r};if(q_(t)){var i,o;return{type:Le.deferred,deferredData:t,statusCode:(i=t.init)==null?void 0:i.status,headers:((o=t.init)==null?void 0:o.headers)&&new Headers(t.init.headers)}}return{type:Le.data,data:t,statusCode:r}}function V_(e,t,n,r,i,o){let a=e.headers.get("Location");if(fe(a,"Redirects returned/thrown from loaders/actions must have a Location header"),!Yd.test(a)){let s=r.slice(0,r.findIndex(l=>l.route.id===n)+1);a=cf(new URL(t.url),s,i,!0,a,o),e.headers.set("Location",a)}return e}function im(e,t,n){if(Yd.test(e)){let r=e,i=r.startsWith("//")?new URL(t.protocol+r):new URL(r),o=fo(i.pathname,n)!=null;if(i.origin===t.origin&&o)return i.pathname+i.search+i.hash}return e}function Si(e,t,n,r){let i=e.createURL(l0(t)).toString(),o={signal:n};if(r&&fn(r.formMethod)){let{formMethod:a,formEncType:s}=r;o.method=a.toUpperCase(),s==="application/json"?(o.headers=new Headers({"Content-Type":s}),o.body=JSON.stringify(r.json)):s==="text/plain"?o.body=r.text:s==="application/x-www-form-urlencoded"&&r.formData?o.body=ff(r.formData):o.body=r.formData}return new Request(i,o)}function ff(e){let t=new URLSearchParams;for(let[n,r]of e.entries())t.append(n,typeof r=="string"?r:r.name);return t}function om(e){let t=new FormData;for(let[n,r]of e.entries())t.append(n,r);return t}function B_(e,t,n,r,i,o){let a={},s=null,l,u=!1,c={},f=r&&Yt(r[1])?r[1].error:void 0;return n.forEach((d,y)=>{let g=t[y].route.id;if(fe(!Kr(d),"Cannot handle redirect results in processLoaderData"),Yt(d)){let v=d.error;f!==void 0&&(v=f,f=void 0),s=s||{};{let E=ea(e,g);s[E.route.id]==null&&(s[E.route.id]=v)}a[g]=void 0,u||(u=!0,l=Gd(d.error)?d.error.status:500),d.headers&&(c[g]=d.headers)}else Xr(d)?(i.set(g,d.deferredData),a[g]=d.deferredData.data,d.statusCode!=null&&d.statusCode!==200&&!u&&(l=d.statusCode),d.headers&&(c[g]=d.headers)):(a[g]=d.data,d.statusCode&&d.statusCode!==200&&!u&&(l=d.statusCode),d.headers&&(c[g]=d.headers))}),f!==void 0&&r&&(s={[r[0]]:f},a[r[0]]=void 0),{loaderData:a,errors:s,statusCode:l||200,loaderHeaders:c}}function am(e,t,n,r,i,o,a,s){let{loaderData:l,errors:u}=B_(t,n,r,i,s);for(let c=0;c<o.length;c++){let{key:f,match:d,controller:y}=o[c];fe(a!==void 0&&a[c]!==void 0,"Did not find corresponding fetcher result");let g=a[c];if(!(y&&y.signal.aborted))if(Yt(g)){let v=ea(e.matches,d==null?void 0:d.route.id);u&&u[v.route.id]||(u=je({},u,{[v.route.id]:g.error})),e.fetchers.delete(f)}else if(Kr(g))fe(!1,"Unhandled fetcher revalidation redirect");else if(Xr(g))fe(!1,"Unhandled fetcher deferred data");else{let v=or(g.data);e.fetchers.set(f,v)}}return{loaderData:l,errors:u}}function sm(e,t,n,r){let i=je({},t);for(let o of n){let a=o.route.id;if(t.hasOwnProperty(a)?t[a]!==void 0&&(i[a]=t[a]):e[a]!==void 0&&o.route.loader&&(i[a]=e[a]),r&&r.hasOwnProperty(a))break}return i}function lm(e){return e?Yt(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function ea(e,t){return(t?e.slice(0,e.findIndex(r=>r.route.id===t)+1):[...e]).reverse().find(r=>r.route.hasErrorBoundary===!0)||e[0]}function um(e){let t=e.length===1?e[0]:e.find(n=>n.index||!n.path||n.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Ht(e,t){let{pathname:n,routeId:r,method:i,type:o}=t===void 0?{}:t,a="Unknown Server Error",s="Unknown @remix-run/router error";return e===400?(a="Bad Request",i&&n&&r?s="You made a "+i+' request to "'+n+'" but '+('did not provide a `loader` for route "'+r+'", ')+"so there is no way to handle the request.":o==="defer-action"?s="defer() is not supported in actions":o==="invalid-body"&&(s="Unable to encode submission body")):e===403?(a="Forbidden",s='Route "'+r+'" does not match URL "'+n+'"'):e===404?(a="Not Found",s='No route matches URL "'+n+'"'):e===405&&(a="Method Not Allowed",i&&n&&r?s="You made a "+i.toUpperCase()+' request to "'+n+'" but '+('did not provide an `action` for route "'+r+'", ')+"so there is no way to handle the request.":i&&(s='Invalid request method "'+i.toUpperCase()+'"')),new Wd(e||500,a,new Error(s),!0)}function cm(e){for(let t=e.length-1;t>=0;t--){let n=e[t];if(Kr(n))return{result:n,idx:t}}}function l0(e){let t=typeof e=="string"?Mr(e):e;return li(je({},t,{hash:""}))}function Q_(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function $_(e){return u0(e.result)&&N_.has(e.result.status)}function Xr(e){return e.type===Le.deferred}function Yt(e){return e.type===Le.error}function Kr(e){return(e&&e.type)===Le.redirect}function q_(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function u0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function H_(e){return O_.has(e.toLowerCase())}function fn(e){return k_.has(e.toLowerCase())}async function fm(e,t,n,r,i,o){for(let a=0;a<n.length;a++){let s=n[a],l=t[a];if(!l)continue;let u=e.find(f=>f.route.id===l.route.id),c=u!=null&&!s0(u,l)&&(o&&o[l.route.id])!==void 0;if(Xr(s)&&(i||c)){let f=r[a];fe(f,"Expected an AbortSignal for revalidating fetcher deferred result"),await c0(s,f,i).then(d=>{d&&(n[a]=d||n[a])})}}}async function c0(e,t,n){if(n===void 0&&(n=!1),!await e.deferredData.resolveData(t)){if(n)try{return{type:Le.data,data:e.deferredData.unwrappedData}}catch(i){return{type:Le.error,error:i}}return{type:Le.data,data:e.deferredData.data}}}function Xd(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function df(e,t){let n=typeof t=="string"?Mr(t).search:t.search;if(e[e.length-1].route.index&&Xd(n||""))return e[e.length-1];let r=i0(e);return r[r.length-1]}function dm(e){let{formMethod:t,formAction:n,formEncType:r,text:i,formData:o,json:a}=e;if(!(!t||!n||!r)){if(i!=null)return{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:void 0,text:i};if(o!=null)return{formMethod:t,formAction:n,formEncType:r,formData:o,json:void 0,text:void 0};if(a!==void 0)return{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:a,text:void 0}}}function qu(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function W_(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function No(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function G_(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function or(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function Y_(e,t){try{let n=e.sessionStorage.getItem(a0);if(n){let r=JSON.parse(n);for(let[i,o]of Object.entries(r||{}))o&&Array.isArray(o)&&t.set(i,new Set(o||[]))}}catch{}}function X_(e,t){if(t.size>0){let n={};for(let[r,i]of t)n[r]=[...i];try{e.sessionStorage.setItem(a0,JSON.stringify(n))}catch(r){ro(!1,"Failed to save applied view transitions in sessionStorage ("+r+").")}}}/**
 * React Router v6.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function wl(){return wl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},wl.apply(this,arguments)}const $l=N.createContext(null),f0=N.createContext(null),hi=N.createContext(null),Kd=N.createContext(null),Yn=N.createContext({outlet:null,matches:[],isDataRoute:!1}),d0=N.createContext(null);function K_(e,t){let{relative:n}=t===void 0?{}:t;Ba()||fe(!1);let{basename:r,navigator:i}=N.useContext(hi),{hash:o,pathname:a,search:s}=m0(e,{relative:n}),l=a;return r!=="/"&&(l=a==="/"?r:Bn([r,a])),i.createHref({pathname:l,search:s,hash:o})}function Ba(){return N.useContext(Kd)!=null}function ql(){return Ba()||fe(!1),N.useContext(Kd).location}function h0(e){N.useContext(hi).static||N.useLayoutEffect(e)}function J_(){let{isDataRoute:e}=N.useContext(Yn);return e?fT():Z_()}function Z_(){Ba()||fe(!1);let e=N.useContext($l),{basename:t,future:n,navigator:r}=N.useContext(hi),{matches:i}=N.useContext(Yn),{pathname:o}=ql(),a=JSON.stringify(qd(i,n.v7_relativeSplatPath)),s=N.useRef(!1);return h0(()=>{s.current=!0}),N.useCallback(function(u,c){if(c===void 0&&(c={}),!s.current)return;if(typeof u=="number"){r.go(u);return}let f=Hd(u,JSON.parse(a),o,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Bn([t,f.pathname])),(c.replace?r.replace:r.push)(f,c.state,c)},[t,r,a,o,e])}const eT=N.createContext(null);function tT(e){let t=N.useContext(Yn).outlet;return t&&N.createElement(eT.Provider,{value:e},t)}function p0(){let{matches:e}=N.useContext(Yn),t=e[e.length-1];return t?t.params:{}}function m0(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=N.useContext(hi),{matches:i}=N.useContext(Yn),{pathname:o}=ql(),a=JSON.stringify(qd(i,r.v7_relativeSplatPath));return N.useMemo(()=>Hd(e,JSON.parse(a),o,n==="path"),[e,a,o,n])}function nT(e,t,n,r){Ba()||fe(!1);let{navigator:i}=N.useContext(hi),{matches:o}=N.useContext(Yn),a=o[o.length-1],s=a?a.params:{};a&&a.pathname;let l=a?a.pathnameBase:"/";a&&a.route;let u=ql(),c;c=u;let f=c.pathname||"/",d=f;if(l!=="/"){let v=l.replace(/^\//,"").split("/");d="/"+f.replace(/^\//,"").split("/").slice(v.length).join("/")}let y=Mi(e,{pathname:d});return sT(y&&y.map(v=>Object.assign({},v,{params:Object.assign({},s,v.params),pathname:Bn([l,i.encodeLocation?i.encodeLocation(v.pathname).pathname:v.pathname]),pathnameBase:v.pathnameBase==="/"?l:Bn([l,i.encodeLocation?i.encodeLocation(v.pathnameBase).pathname:v.pathnameBase])})),o,n,r)}function rT(){let e=g0(),t=Gd(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return N.createElement(N.Fragment,null,N.createElement("h2",null,"Unexpected Application Error!"),N.createElement("h3",{style:{fontStyle:"italic"}},t),n?N.createElement("pre",{style:i},n):null,null)}const iT=N.createElement(rT,null);class oT extends N.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?N.createElement(Yn.Provider,{value:this.props.routeContext},N.createElement(d0.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function aT(e){let{routeContext:t,match:n,children:r}=e,i=N.useContext($l);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),N.createElement(Yn.Provider,{value:t},r)}function sT(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if((o=n)!=null&&o.errors)e=n.matches;else return null}let a=e,s=(i=n)==null?void 0:i.errors;if(s!=null){let c=a.findIndex(f=>f.route.id&&(s==null?void 0:s[f.route.id])!==void 0);c>=0||fe(!1),a=a.slice(0,Math.min(a.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<a.length;c++){let f=a[c];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=c),f.route.id){let{loaderData:d,errors:y}=n,g=f.route.loader&&d[f.route.id]===void 0&&(!y||y[f.route.id]===void 0);if(f.route.lazy||g){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((c,f,d)=>{let y,g=!1,v=null,E=null;n&&(y=s&&f.route.id?s[f.route.id]:void 0,v=f.route.errorElement||iT,l&&(u<0&&d===0?(g=!0,E=null):u===d&&(g=!0,E=f.route.hydrateFallbackElement||null)));let h=t.concat(a.slice(0,d+1)),m=()=>{let p;return y?p=v:g?p=E:f.route.Component?p=N.createElement(f.route.Component,null):f.route.element?p=f.route.element:p=c,N.createElement(aT,{match:f,routeContext:{outlet:c,matches:h,isDataRoute:n!=null},children:p})};return n&&(f.route.ErrorBoundary||f.route.errorElement||d===0)?N.createElement(oT,{location:n.location,revalidation:n.revalidation,component:v,error:y,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()},null)}var v0=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(v0||{}),Sl=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Sl||{});function lT(e){let t=N.useContext($l);return t||fe(!1),t}function uT(e){let t=N.useContext(f0);return t||fe(!1),t}function cT(e){let t=N.useContext(Yn);return t||fe(!1),t}function y0(e){let t=cT(),n=t.matches[t.matches.length-1];return n.route.id||fe(!1),n.route.id}function g0(){var e;let t=N.useContext(d0),n=uT(Sl.UseRouteError),r=y0(Sl.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function fT(){let{router:e}=lT(v0.UseNavigateStable),t=y0(Sl.UseNavigateStable),n=N.useRef(!1);return h0(()=>{n.current=!0}),N.useCallback(function(i,o){o===void 0&&(o={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,wl({fromRouteId:t},o)))},[e,t])}function dT(e){return tT(e.context)}function hT(e){let{basename:t="/",children:n=null,location:r,navigationType:i=Je.Pop,navigator:o,static:a=!1,future:s}=e;Ba()&&fe(!1);let l=t.replace(/^\/*/,"/"),u=N.useMemo(()=>({basename:l,navigator:o,static:a,future:wl({v7_relativeSplatPath:!1},s)}),[l,s,o,a]);typeof r=="string"&&(r=Mr(r));let{pathname:c="/",search:f="",hash:d="",state:y=null,key:g="default"}=r,v=N.useMemo(()=>{let E=fo(c,l);return E==null?null:{location:{pathname:E,search:f,hash:d,state:y,key:g},navigationType:i}},[l,c,f,d,y,g,i]);return v==null?null:N.createElement(hi.Provider,{value:u},N.createElement(Kd.Provider,{children:n,value:v}))}new Promise(()=>{});function pT(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&Object.assign(t,{element:N.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:N.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:N.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}/**
 * React Router DOM v6.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ka(){return ka=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ka.apply(this,arguments)}function mT(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function vT(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function yT(e,t){return e.button===0&&(!t||t==="_self")&&!vT(e)}const gT=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","unstable_viewTransition"],ET="6";try{window.__reactRouterVersion=ET}catch{}function wT(e,t){return P_({basename:void 0,future:ka({},void 0,{v7_prependBasename:!0}),history:n_({window:void 0}),hydrationData:ST(),routes:e,mapRouteProperties:pT,unstable_dataStrategy:void 0,window:void 0}).initialize()}function ST(){var e;let t=(e=window)==null?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=ka({},t,{errors:_T(t.errors)})),t}function _T(e){if(!e)return null;let t=Object.entries(e),n={};for(let[r,i]of t)if(i&&i.__type==="RouteErrorResponse")n[r]=new Wd(i.status,i.statusText,i.data,i.internal===!0);else if(i&&i.__type==="Error"){if(i.__subType){let o=window[i.__subType];if(typeof o=="function")try{let a=new o(i.message);a.stack="",n[r]=a}catch{}}if(n[r]==null){let o=new Error(i.message);o.stack="",n[r]=o}}else n[r]=i;return n}const TT=N.createContext({isTransitioning:!1}),xT=N.createContext(new Map),bT="startTransition",hm=QE[bT],kT="flushSync",pm=t_[kT];function CT(e){hm?hm(e):e()}function Ro(e){pm?pm(e):e()}class OT{constructor(){this.status="pending",this.promise=new Promise((t,n)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",t(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",n(r))}})}}function NT(e){let{fallbackElement:t,router:n,future:r}=e,[i,o]=N.useState(n.state),[a,s]=N.useState(),[l,u]=N.useState({isTransitioning:!1}),[c,f]=N.useState(),[d,y]=N.useState(),[g,v]=N.useState(),E=N.useRef(new Map),{v7_startTransition:h}=r||{},m=N.useCallback(w=>{h?CT(w):w()},[h]),p=N.useCallback((w,k)=>{let{deletedFetchers:I,unstable_flushSync:P,unstable_viewTransitionOpts:K}=k;I.forEach(ee=>E.current.delete(ee)),w.fetchers.forEach((ee,le)=>{ee.data!==void 0&&E.current.set(le,ee.data)});let se=n.window==null||typeof n.window.document.startViewTransition!="function";if(!K||se){P?Ro(()=>o(w)):m(()=>o(w));return}if(P){Ro(()=>{d&&(c&&c.resolve(),d.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:K.currentLocation,nextLocation:K.nextLocation})});let ee=n.window.document.startViewTransition(()=>{Ro(()=>o(w))});ee.finished.finally(()=>{Ro(()=>{f(void 0),y(void 0),s(void 0),u({isTransitioning:!1})})}),Ro(()=>y(ee));return}d?(c&&c.resolve(),d.skipTransition(),v({state:w,currentLocation:K.currentLocation,nextLocation:K.nextLocation})):(s(w),u({isTransitioning:!0,flushSync:!1,currentLocation:K.currentLocation,nextLocation:K.nextLocation}))},[n.window,d,c,E,m]);N.useLayoutEffect(()=>n.subscribe(p),[n,p]),N.useEffect(()=>{l.isTransitioning&&!l.flushSync&&f(new OT)},[l]),N.useEffect(()=>{if(c&&a&&n.window){let w=a,k=c.promise,I=n.window.document.startViewTransition(async()=>{m(()=>o(w)),await k});I.finished.finally(()=>{f(void 0),y(void 0),s(void 0),u({isTransitioning:!1})}),y(I)}},[m,a,c,n.window]),N.useEffect(()=>{c&&a&&i.location.key===a.location.key&&c.resolve()},[c,d,i.location,a]),N.useEffect(()=>{!l.isTransitioning&&g&&(s(g.state),u({isTransitioning:!0,flushSync:!1,currentLocation:g.currentLocation,nextLocation:g.nextLocation}),v(void 0))},[l.isTransitioning,g]),N.useEffect(()=>{},[]);let S=N.useMemo(()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:w=>n.navigate(w),push:(w,k,I)=>n.navigate(w,{state:k,preventScrollReset:I==null?void 0:I.preventScrollReset}),replace:(w,k,I)=>n.navigate(w,{replace:!0,state:k,preventScrollReset:I==null?void 0:I.preventScrollReset})}),[n]),T=n.basename||"/",O=N.useMemo(()=>({router:n,navigator:S,static:!1,basename:T}),[n,S,T]);return N.createElement(N.Fragment,null,N.createElement($l.Provider,{value:O},N.createElement(f0.Provider,{value:i},N.createElement(xT.Provider,{value:E.current},N.createElement(TT.Provider,{value:l},N.createElement(hT,{basename:T,location:i.location,navigationType:i.historyAction,navigator:S,future:{v7_relativeSplatPath:n.future.v7_relativeSplatPath}},i.initialized||n.future.v7_partialHydration?N.createElement(RT,{routes:n.routes,future:n.future,state:i}):t))))),null)}function RT(e){let{routes:t,future:n,state:r}=e;return nT(t,void 0,r,n)}const DT=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",IT=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Hl=N.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:o,replace:a,state:s,target:l,to:u,preventScrollReset:c,unstable_viewTransition:f}=t,d=mT(t,gT),{basename:y}=N.useContext(hi),g,v=!1;if(typeof u=="string"&&IT.test(u)&&(g=u,DT))try{let p=new URL(window.location.href),S=u.startsWith("//")?new URL(p.protocol+u):new URL(u),T=fo(S.pathname,y);S.origin===p.origin&&T!=null?u=T+S.search+S.hash:v=!0}catch{}let E=K_(u,{relative:i}),h=PT(u,{replace:a,state:s,target:l,preventScrollReset:c,relative:i,unstable_viewTransition:f});function m(p){r&&r(p),p.defaultPrevented||h(p)}return N.createElement("a",ka({},d,{href:g||E,onClick:v||o?r:m,ref:n,target:l}))});var mm;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(mm||(mm={}));var vm;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(vm||(vm={}));function PT(e,t){let{target:n,replace:r,state:i,preventScrollReset:o,relative:a,unstable_viewTransition:s}=t===void 0?{}:t,l=J_(),u=ql(),c=m0(e,{relative:a});return N.useCallback(f=>{if(yT(f,n)){f.preventDefault();let d=r!==void 0?r:li(u)===li(c);l(e,{replace:d,state:i,preventScrollReset:o,relative:a,unstable_viewTransition:s})}},[u,l,c,r,i,n,e,o,a,s])}var hf=function(e,t){return hf=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])},hf(e,t)};function Nn(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");hf(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}var b=function(){return b=Object.assign||function(t){for(var n,r=1,i=arguments.length;r<i;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},b.apply(this,arguments)};function kn(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}function ur(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(f){a(f)}}function l(c){try{u(r.throw(c))}catch(f){a(f)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})}function cr(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(u){return function(c){return l([u,c])}}function l(u){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(n=0)),n;)try{if(r=1,i&&(o=u[0]&2?i.return:u[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,u[1])).done)return o;switch(i=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return n.label++,{value:u[1],done:!1};case 5:n.label++,i=u[1],u=[0];continue;case 7:u=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){n=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){n.label=u[1];break}if(u[0]===6&&n.label<o[1]){n.label=o[1],o=u;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(u);break}o[2]&&n.ops.pop(),n.trys.pop();continue}u=t.call(e,n)}catch(c){u=[6,c],i=0}finally{r=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function vn(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,o;r<i;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}var Hu="Invariant Violation",ym=Object.setPrototypeOf,AT=ym===void 0?function(e,t){return e.__proto__=t,e}:ym,E0=function(e){Nn(t,e);function t(n){n===void 0&&(n=Hu);var r=e.call(this,typeof n=="number"?Hu+": "+n+" (see https://github.com/apollographql/invariant-packages)":n)||this;return r.framesToPop=1,r.name=Hu,AT(r,t.prototype),r}return t}(Error);function qr(e,t){if(!e)throw new E0(t)}var w0=["debug","log","warn","error","silent"],LT=w0.indexOf("log");function vs(e){return function(){if(w0.indexOf(e)>=LT){var t=console[e]||console.log;return t.apply(console,arguments)}}}(function(e){e.debug=vs("debug"),e.log=vs("log"),e.warn=vs("warn"),e.error=vs("error")})(qr||(qr={}));var Jd="3.10.2";function hn(e){try{return e()}catch{}}const pf=hn(function(){return globalThis})||hn(function(){return window})||hn(function(){return self})||hn(function(){return global})||hn(function(){return hn.constructor("return this")()});var gm=new Map;function mf(e){var t=gm.get(e)||1;return gm.set(e,t+1),"".concat(e,":").concat(t,":").concat(Math.random().toString(36).slice(2))}function S0(e,t){t===void 0&&(t=0);var n=mf("stringifyForDisplay");return JSON.stringify(e,function(r,i){return i===void 0?n:i},t).split(JSON.stringify(n)).join("<undefined>")}function ys(e){return function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];if(typeof t=="number"){var i=t;t=Zd(i),t||(t=eh(i,n),n=[])}e.apply(void 0,[t].concat(n))}}var X=Object.assign(function(t,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];t||qr(t,Zd(n,r)||eh(n,r))},{debug:ys(qr.debug),log:ys(qr.log),warn:ys(qr.warn),error:ys(qr.error)});function _t(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new E0(Zd(e,t)||eh(e,t))}var Em=Symbol.for("ApolloErrorMessageHandler_"+Jd);function _0(e){return typeof e=="string"?e:S0(e,2).slice(0,1e3)}function Zd(e,t){if(t===void 0&&(t=[]),!!e)return pf[Em]&&pf[Em](e,t.map(_0))}function eh(e,t){if(t===void 0&&(t=[]),!!e)return"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#".concat(encodeURIComponent(JSON.stringify({version:Jd,message:e,args:t.map(_0)})))}function Vs(e,t){if(!!!e)throw new Error(t)}function MT(e){return typeof e=="object"&&e!==null}function FT(e,t){if(!!!e)throw new Error("Unexpected invariant triggered.")}const jT=/\r\n|[\n\r]/g;function vf(e,t){let n=0,r=1;for(const i of e.body.matchAll(jT)){if(typeof i.index=="number"||FT(!1),i.index>=t)break;n=i.index+i[0].length,r+=1}return{line:r,column:t+1-n}}function zT(e){return T0(e.source,vf(e.source,e.start))}function T0(e,t){const n=e.locationOffset.column-1,r="".padStart(n)+e.body,i=t.line-1,o=e.locationOffset.line-1,a=t.line+o,s=t.line===1?n:0,l=t.column+s,u=`${e.name}:${a}:${l}
`,c=r.split(/\r\n|[\n\r]/g),f=c[i];if(f.length>120){const d=Math.floor(l/80),y=l%80,g=[];for(let v=0;v<f.length;v+=80)g.push(f.slice(v,v+80));return u+wm([[`${a} |`,g[0]],...g.slice(1,d+1).map(v=>["|",v]),["|","^".padStart(y)],["|",g[d+1]]])}return u+wm([[`${a-1} |`,c[i-1]],[`${a} |`,f],["|","^".padStart(l)],[`${a+1} |`,c[i+1]]])}function wm(e){const t=e.filter(([r,i])=>i!==void 0),n=Math.max(...t.map(([r])=>r.length));return t.map(([r,i])=>r.padStart(n)+(i?" "+i:"")).join(`
`)}function UT(e){const t=e[0];return t==null||"kind"in t||"length"in t?{nodes:t,source:e[1],positions:e[2],path:e[3],originalError:e[4],extensions:e[5]}:t}class th extends Error{constructor(t,...n){var r,i,o;const{nodes:a,source:s,positions:l,path:u,originalError:c,extensions:f}=UT(n);super(t),this.name="GraphQLError",this.path=u??void 0,this.originalError=c??void 0,this.nodes=Sm(Array.isArray(a)?a:a?[a]:void 0);const d=Sm((r=this.nodes)===null||r===void 0?void 0:r.map(g=>g.loc).filter(g=>g!=null));this.source=s??(d==null||(i=d[0])===null||i===void 0?void 0:i.source),this.positions=l??(d==null?void 0:d.map(g=>g.start)),this.locations=l&&s?l.map(g=>vf(s,g)):d==null?void 0:d.map(g=>vf(g.source,g.start));const y=MT(c==null?void 0:c.extensions)?c==null?void 0:c.extensions:void 0;this.extensions=(o=f??y)!==null&&o!==void 0?o:Object.create(null),Object.defineProperties(this,{message:{writable:!0,enumerable:!0},name:{enumerable:!1},nodes:{enumerable:!1},source:{enumerable:!1},positions:{enumerable:!1},originalError:{enumerable:!1}}),c!=null&&c.stack?Object.defineProperty(this,"stack",{value:c.stack,writable:!0,configurable:!0}):Error.captureStackTrace?Error.captureStackTrace(this,th):Object.defineProperty(this,"stack",{value:Error().stack,writable:!0,configurable:!0})}get[Symbol.toStringTag](){return"GraphQLError"}toString(){let t=this.message;if(this.nodes)for(const n of this.nodes)n.loc&&(t+=`

`+zT(n.loc));else if(this.source&&this.locations)for(const n of this.locations)t+=`

`+T0(this.source,n);return t}toJSON(){const t={message:this.message};return this.locations!=null&&(t.locations=this.locations),this.path!=null&&(t.path=this.path),this.extensions!=null&&Object.keys(this.extensions).length>0&&(t.extensions=this.extensions),t}}function Sm(e){return e===void 0||e.length===0?void 0:e}function at(e,t,n){return new th(`Syntax Error: ${n}`,{source:e,positions:[t]})}class VT{constructor(t,n,r){this.start=t.start,this.end=n.end,this.startToken=t,this.endToken=n,this.source=r}get[Symbol.toStringTag](){return"Location"}toJSON(){return{start:this.start,end:this.end}}}class x0{constructor(t,n,r,i,o,a){this.kind=t,this.start=n,this.end=r,this.line=i,this.column=o,this.value=a,this.prev=null,this.next=null}get[Symbol.toStringTag](){return"Token"}toJSON(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}}}const b0={Name:[],Document:["definitions"],OperationDefinition:["name","variableDefinitions","directives","selectionSet"],VariableDefinition:["variable","type","defaultValue","directives"],Variable:["name"],SelectionSet:["selections"],Field:["alias","name","arguments","directives","selectionSet"],Argument:["name","value"],FragmentSpread:["name","directives"],InlineFragment:["typeCondition","directives","selectionSet"],FragmentDefinition:["name","variableDefinitions","typeCondition","directives","selectionSet"],IntValue:[],FloatValue:[],StringValue:[],BooleanValue:[],NullValue:[],EnumValue:[],ListValue:["values"],ObjectValue:["fields"],ObjectField:["name","value"],Directive:["name","arguments"],NamedType:["name"],ListType:["type"],NonNullType:["type"],SchemaDefinition:["description","directives","operationTypes"],OperationTypeDefinition:["type"],ScalarTypeDefinition:["description","name","directives"],ObjectTypeDefinition:["description","name","interfaces","directives","fields"],FieldDefinition:["description","name","arguments","type","directives"],InputValueDefinition:["description","name","type","defaultValue","directives"],InterfaceTypeDefinition:["description","name","interfaces","directives","fields"],UnionTypeDefinition:["description","name","directives","types"],EnumTypeDefinition:["description","name","directives","values"],EnumValueDefinition:["description","name","directives"],InputObjectTypeDefinition:["description","name","directives","fields"],DirectiveDefinition:["description","name","arguments","locations"],SchemaExtension:["directives","operationTypes"],ScalarTypeExtension:["name","directives"],ObjectTypeExtension:["name","interfaces","directives","fields"],InterfaceTypeExtension:["name","interfaces","directives","fields"],UnionTypeExtension:["name","directives","types"],EnumTypeExtension:["name","directives","values"],InputObjectTypeExtension:["name","directives","fields"]},BT=new Set(Object.keys(b0));function _m(e){const t=e==null?void 0:e.kind;return typeof t=="string"&&BT.has(t)}var Fi;(function(e){e.QUERY="query",e.MUTATION="mutation",e.SUBSCRIPTION="subscription"})(Fi||(Fi={}));var yf;(function(e){e.QUERY="QUERY",e.MUTATION="MUTATION",e.SUBSCRIPTION="SUBSCRIPTION",e.FIELD="FIELD",e.FRAGMENT_DEFINITION="FRAGMENT_DEFINITION",e.FRAGMENT_SPREAD="FRAGMENT_SPREAD",e.INLINE_FRAGMENT="INLINE_FRAGMENT",e.VARIABLE_DEFINITION="VARIABLE_DEFINITION",e.SCHEMA="SCHEMA",e.SCALAR="SCALAR",e.OBJECT="OBJECT",e.FIELD_DEFINITION="FIELD_DEFINITION",e.ARGUMENT_DEFINITION="ARGUMENT_DEFINITION",e.INTERFACE="INTERFACE",e.UNION="UNION",e.ENUM="ENUM",e.ENUM_VALUE="ENUM_VALUE",e.INPUT_OBJECT="INPUT_OBJECT",e.INPUT_FIELD_DEFINITION="INPUT_FIELD_DEFINITION"})(yf||(yf={}));var G;(function(e){e.NAME="Name",e.DOCUMENT="Document",e.OPERATION_DEFINITION="OperationDefinition",e.VARIABLE_DEFINITION="VariableDefinition",e.SELECTION_SET="SelectionSet",e.FIELD="Field",e.ARGUMENT="Argument",e.FRAGMENT_SPREAD="FragmentSpread",e.INLINE_FRAGMENT="InlineFragment",e.FRAGMENT_DEFINITION="FragmentDefinition",e.VARIABLE="Variable",e.INT="IntValue",e.FLOAT="FloatValue",e.STRING="StringValue",e.BOOLEAN="BooleanValue",e.NULL="NullValue",e.ENUM="EnumValue",e.LIST="ListValue",e.OBJECT="ObjectValue",e.OBJECT_FIELD="ObjectField",e.DIRECTIVE="Directive",e.NAMED_TYPE="NamedType",e.LIST_TYPE="ListType",e.NON_NULL_TYPE="NonNullType",e.SCHEMA_DEFINITION="SchemaDefinition",e.OPERATION_TYPE_DEFINITION="OperationTypeDefinition",e.SCALAR_TYPE_DEFINITION="ScalarTypeDefinition",e.OBJECT_TYPE_DEFINITION="ObjectTypeDefinition",e.FIELD_DEFINITION="FieldDefinition",e.INPUT_VALUE_DEFINITION="InputValueDefinition",e.INTERFACE_TYPE_DEFINITION="InterfaceTypeDefinition",e.UNION_TYPE_DEFINITION="UnionTypeDefinition",e.ENUM_TYPE_DEFINITION="EnumTypeDefinition",e.ENUM_VALUE_DEFINITION="EnumValueDefinition",e.INPUT_OBJECT_TYPE_DEFINITION="InputObjectTypeDefinition",e.DIRECTIVE_DEFINITION="DirectiveDefinition",e.SCHEMA_EXTENSION="SchemaExtension",e.SCALAR_TYPE_EXTENSION="ScalarTypeExtension",e.OBJECT_TYPE_EXTENSION="ObjectTypeExtension",e.INTERFACE_TYPE_EXTENSION="InterfaceTypeExtension",e.UNION_TYPE_EXTENSION="UnionTypeExtension",e.ENUM_TYPE_EXTENSION="EnumTypeExtension",e.INPUT_OBJECT_TYPE_EXTENSION="InputObjectTypeExtension"})(G||(G={}));function gf(e){return e===9||e===32}function Ca(e){return e>=48&&e<=57}function k0(e){return e>=97&&e<=122||e>=65&&e<=90}function C0(e){return k0(e)||e===95}function QT(e){return k0(e)||Ca(e)||e===95}function $T(e){var t;let n=Number.MAX_SAFE_INTEGER,r=null,i=-1;for(let a=0;a<e.length;++a){var o;const s=e[a],l=qT(s);l!==s.length&&(r=(o=r)!==null&&o!==void 0?o:a,i=a,a!==0&&l<n&&(n=l))}return e.map((a,s)=>s===0?a:a.slice(n)).slice((t=r)!==null&&t!==void 0?t:0,i+1)}function qT(e){let t=0;for(;t<e.length&&gf(e.charCodeAt(t));)++t;return t}function HT(e,t){const n=e.replace(/"""/g,'\\"""'),r=n.split(/\r\n|[\n\r]/g),i=r.length===1,o=r.length>1&&r.slice(1).every(y=>y.length===0||gf(y.charCodeAt(0))),a=n.endsWith('\\"""'),s=e.endsWith('"')&&!a,l=e.endsWith("\\"),u=s||l,c=!i||e.length>70||u||o||a;let f="";const d=i&&gf(e.charCodeAt(0));return(c&&!d||o)&&(f+=`
`),f+=n,(c||u)&&(f+=`
`),'"""'+f+'"""'}var A;(function(e){e.SOF="<SOF>",e.EOF="<EOF>",e.BANG="!",e.DOLLAR="$",e.AMP="&",e.PAREN_L="(",e.PAREN_R=")",e.SPREAD="...",e.COLON=":",e.EQUALS="=",e.AT="@",e.BRACKET_L="[",e.BRACKET_R="]",e.BRACE_L="{",e.PIPE="|",e.BRACE_R="}",e.NAME="Name",e.INT="Int",e.FLOAT="Float",e.STRING="String",e.BLOCK_STRING="BlockString",e.COMMENT="Comment"})(A||(A={}));class WT{constructor(t){const n=new x0(A.SOF,0,0,0,0);this.source=t,this.lastToken=n,this.token=n,this.line=1,this.lineStart=0}get[Symbol.toStringTag](){return"Lexer"}advance(){return this.lastToken=this.token,this.token=this.lookahead()}lookahead(){let t=this.token;if(t.kind!==A.EOF)do if(t.next)t=t.next;else{const n=YT(this,t.end);t.next=n,n.prev=t,t=n}while(t.kind===A.COMMENT);return t}}function GT(e){return e===A.BANG||e===A.DOLLAR||e===A.AMP||e===A.PAREN_L||e===A.PAREN_R||e===A.SPREAD||e===A.COLON||e===A.EQUALS||e===A.AT||e===A.BRACKET_L||e===A.BRACKET_R||e===A.BRACE_L||e===A.PIPE||e===A.BRACE_R}function ho(e){return e>=0&&e<=55295||e>=57344&&e<=1114111}function Wl(e,t){return O0(e.charCodeAt(t))&&N0(e.charCodeAt(t+1))}function O0(e){return e>=55296&&e<=56319}function N0(e){return e>=56320&&e<=57343}function ui(e,t){const n=e.source.body.codePointAt(t);if(n===void 0)return A.EOF;if(n>=32&&n<=126){const r=String.fromCodePoint(n);return r==='"'?`'"'`:`"${r}"`}return"U+"+n.toString(16).toUpperCase().padStart(4,"0")}function Ke(e,t,n,r,i){const o=e.line,a=1+n-e.lineStart;return new x0(t,n,r,o,a,i)}function YT(e,t){const n=e.source.body,r=n.length;let i=t;for(;i<r;){const o=n.charCodeAt(i);switch(o){case 65279:case 9:case 32:case 44:++i;continue;case 10:++i,++e.line,e.lineStart=i;continue;case 13:n.charCodeAt(i+1)===10?i+=2:++i,++e.line,e.lineStart=i;continue;case 35:return XT(e,i);case 33:return Ke(e,A.BANG,i,i+1);case 36:return Ke(e,A.DOLLAR,i,i+1);case 38:return Ke(e,A.AMP,i,i+1);case 40:return Ke(e,A.PAREN_L,i,i+1);case 41:return Ke(e,A.PAREN_R,i,i+1);case 46:if(n.charCodeAt(i+1)===46&&n.charCodeAt(i+2)===46)return Ke(e,A.SPREAD,i,i+3);break;case 58:return Ke(e,A.COLON,i,i+1);case 61:return Ke(e,A.EQUALS,i,i+1);case 64:return Ke(e,A.AT,i,i+1);case 91:return Ke(e,A.BRACKET_L,i,i+1);case 93:return Ke(e,A.BRACKET_R,i,i+1);case 123:return Ke(e,A.BRACE_L,i,i+1);case 124:return Ke(e,A.PIPE,i,i+1);case 125:return Ke(e,A.BRACE_R,i,i+1);case 34:return n.charCodeAt(i+1)===34&&n.charCodeAt(i+2)===34?nx(e,i):JT(e,i)}if(Ca(o)||o===45)return KT(e,i,o);if(C0(o))return rx(e,i);throw at(e.source,i,o===39?`Unexpected single quote character ('), did you mean to use a double quote (")?`:ho(o)||Wl(n,i)?`Unexpected character: ${ui(e,i)}.`:`Invalid character: ${ui(e,i)}.`)}return Ke(e,A.EOF,r,r)}function XT(e,t){const n=e.source.body,r=n.length;let i=t+1;for(;i<r;){const o=n.charCodeAt(i);if(o===10||o===13)break;if(ho(o))++i;else if(Wl(n,i))i+=2;else break}return Ke(e,A.COMMENT,t,i,n.slice(t+1,i))}function KT(e,t,n){const r=e.source.body;let i=t,o=n,a=!1;if(o===45&&(o=r.charCodeAt(++i)),o===48){if(o=r.charCodeAt(++i),Ca(o))throw at(e.source,i,`Invalid number, unexpected digit after 0: ${ui(e,i)}.`)}else i=Wu(e,i,o),o=r.charCodeAt(i);if(o===46&&(a=!0,o=r.charCodeAt(++i),i=Wu(e,i,o),o=r.charCodeAt(i)),(o===69||o===101)&&(a=!0,o=r.charCodeAt(++i),(o===43||o===45)&&(o=r.charCodeAt(++i)),i=Wu(e,i,o),o=r.charCodeAt(i)),o===46||C0(o))throw at(e.source,i,`Invalid number, expected digit but got: ${ui(e,i)}.`);return Ke(e,a?A.FLOAT:A.INT,t,i,r.slice(t,i))}function Wu(e,t,n){if(!Ca(n))throw at(e.source,t,`Invalid number, expected digit but got: ${ui(e,t)}.`);const r=e.source.body;let i=t+1;for(;Ca(r.charCodeAt(i));)++i;return i}function JT(e,t){const n=e.source.body,r=n.length;let i=t+1,o=i,a="";for(;i<r;){const s=n.charCodeAt(i);if(s===34)return a+=n.slice(o,i),Ke(e,A.STRING,t,i+1,a);if(s===92){a+=n.slice(o,i);const l=n.charCodeAt(i+1)===117?n.charCodeAt(i+2)===123?ZT(e,i):ex(e,i):tx(e,i);a+=l.value,i+=l.size,o=i;continue}if(s===10||s===13)break;if(ho(s))++i;else if(Wl(n,i))i+=2;else throw at(e.source,i,`Invalid character within String: ${ui(e,i)}.`)}throw at(e.source,i,"Unterminated string.")}function ZT(e,t){const n=e.source.body;let r=0,i=3;for(;i<12;){const o=n.charCodeAt(t+i++);if(o===125){if(i<5||!ho(r))break;return{value:String.fromCodePoint(r),size:i}}if(r=r<<4|Qo(o),r<0)break}throw at(e.source,t,`Invalid Unicode escape sequence: "${n.slice(t,t+i)}".`)}function ex(e,t){const n=e.source.body,r=Tm(n,t+2);if(ho(r))return{value:String.fromCodePoint(r),size:6};if(O0(r)&&n.charCodeAt(t+6)===92&&n.charCodeAt(t+7)===117){const i=Tm(n,t+8);if(N0(i))return{value:String.fromCodePoint(r,i),size:12}}throw at(e.source,t,`Invalid Unicode escape sequence: "${n.slice(t,t+6)}".`)}function Tm(e,t){return Qo(e.charCodeAt(t))<<12|Qo(e.charCodeAt(t+1))<<8|Qo(e.charCodeAt(t+2))<<4|Qo(e.charCodeAt(t+3))}function Qo(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}function tx(e,t){const n=e.source.body;switch(n.charCodeAt(t+1)){case 34:return{value:'"',size:2};case 92:return{value:"\\",size:2};case 47:return{value:"/",size:2};case 98:return{value:"\b",size:2};case 102:return{value:"\f",size:2};case 110:return{value:`
`,size:2};case 114:return{value:"\r",size:2};case 116:return{value:"	",size:2}}throw at(e.source,t,`Invalid character escape sequence: "${n.slice(t,t+2)}".`)}function nx(e,t){const n=e.source.body,r=n.length;let i=e.lineStart,o=t+3,a=o,s="";const l=[];for(;o<r;){const u=n.charCodeAt(o);if(u===34&&n.charCodeAt(o+1)===34&&n.charCodeAt(o+2)===34){s+=n.slice(a,o),l.push(s);const c=Ke(e,A.BLOCK_STRING,t,o+3,$T(l).join(`
`));return e.line+=l.length-1,e.lineStart=i,c}if(u===92&&n.charCodeAt(o+1)===34&&n.charCodeAt(o+2)===34&&n.charCodeAt(o+3)===34){s+=n.slice(a,o),a=o+1,o+=4;continue}if(u===10||u===13){s+=n.slice(a,o),l.push(s),u===13&&n.charCodeAt(o+1)===10?o+=2:++o,s="",a=o,i=o;continue}if(ho(u))++o;else if(Wl(n,o))o+=2;else throw at(e.source,o,`Invalid character within String: ${ui(e,o)}.`)}throw at(e.source,o,"Unterminated string.")}function rx(e,t){const n=e.source.body,r=n.length;let i=t+1;for(;i<r;){const o=n.charCodeAt(i);if(QT(o))++i;else break}return Ke(e,A.NAME,t,i,n.slice(t,i))}const ix=10,R0=2;function nh(e){return Gl(e,[])}function Gl(e,t){switch(typeof e){case"string":return JSON.stringify(e);case"function":return e.name?`[function ${e.name}]`:"[function]";case"object":return ox(e,t);default:return String(e)}}function ox(e,t){if(e===null)return"null";if(t.includes(e))return"[Circular]";const n=[...t,e];if(ax(e)){const r=e.toJSON();if(r!==e)return typeof r=="string"?r:Gl(r,n)}else if(Array.isArray(e))return lx(e,n);return sx(e,n)}function ax(e){return typeof e.toJSON=="function"}function sx(e,t){const n=Object.entries(e);return n.length===0?"{}":t.length>R0?"["+ux(e)+"]":"{ "+n.map(([i,o])=>i+": "+Gl(o,t)).join(", ")+" }"}function lx(e,t){if(e.length===0)return"[]";if(t.length>R0)return"[Array]";const n=Math.min(ix,e.length),r=e.length-n,i=[];for(let o=0;o<n;++o)i.push(Gl(e[o],t));return r===1?i.push("... 1 more item"):r>1&&i.push(`... ${r} more items`),"["+i.join(", ")+"]"}function ux(e){const t=Object.prototype.toString.call(e).replace(/^\[object /,"").replace(/]$/,"");if(t==="Object"&&typeof e.constructor=="function"){const n=e.constructor.name;if(typeof n=="string"&&n!=="")return n}return t}const cx=globalThis.process?function(t,n){return t instanceof n}:function(t,n){if(t instanceof n)return!0;if(typeof t=="object"&&t!==null){var r;const i=n.prototype[Symbol.toStringTag],o=Symbol.toStringTag in t?t[Symbol.toStringTag]:(r=t.constructor)===null||r===void 0?void 0:r.name;if(i===o){const a=nh(t);throw new Error(`Cannot use ${i} "${a}" from another module or realm.

Ensure that there is only one instance of "graphql" in the node_modules
directory. If different versions of "graphql" are the dependencies of other
relied on modules, use "resolutions" to ensure only one version is installed.

https://yarnpkg.com/en/docs/selective-version-resolutions

Duplicate "graphql" modules cannot be used at the same time since different
versions may have different capabilities and behavior. The data from one
version used in the function from another could produce confusing and
spurious results.`)}}return!1};class D0{constructor(t,n="GraphQL request",r={line:1,column:1}){typeof t=="string"||Vs(!1,`Body must be a string. Received: ${nh(t)}.`),this.body=t,this.name=n,this.locationOffset=r,this.locationOffset.line>0||Vs(!1,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||Vs(!1,"column in locationOffset is 1-indexed and must be positive.")}get[Symbol.toStringTag](){return"Source"}}function fx(e){return cx(e,D0)}function dx(e,t){return new hx(e,t).parseDocument()}class hx{constructor(t,n={}){const r=fx(t)?t:new D0(t);this._lexer=new WT(r),this._options=n,this._tokenCounter=0}parseName(){const t=this.expectToken(A.NAME);return this.node(t,{kind:G.NAME,value:t.value})}parseDocument(){return this.node(this._lexer.token,{kind:G.DOCUMENT,definitions:this.many(A.SOF,this.parseDefinition,A.EOF)})}parseDefinition(){if(this.peek(A.BRACE_L))return this.parseOperationDefinition();const t=this.peekDescription(),n=t?this._lexer.lookahead():this._lexer.token;if(n.kind===A.NAME){switch(n.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}if(t)throw at(this._lexer.source,this._lexer.token.start,"Unexpected description, descriptions are supported only on type definitions.");switch(n.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"extend":return this.parseTypeSystemExtension()}}throw this.unexpected(n)}parseOperationDefinition(){const t=this._lexer.token;if(this.peek(A.BRACE_L))return this.node(t,{kind:G.OPERATION_DEFINITION,operation:Fi.QUERY,name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet()});const n=this.parseOperationType();let r;return this.peek(A.NAME)&&(r=this.parseName()),this.node(t,{kind:G.OPERATION_DEFINITION,operation:n,name:r,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseOperationType(){const t=this.expectToken(A.NAME);switch(t.value){case"query":return Fi.QUERY;case"mutation":return Fi.MUTATION;case"subscription":return Fi.SUBSCRIPTION}throw this.unexpected(t)}parseVariableDefinitions(){return this.optionalMany(A.PAREN_L,this.parseVariableDefinition,A.PAREN_R)}parseVariableDefinition(){return this.node(this._lexer.token,{kind:G.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(A.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(A.EQUALS)?this.parseConstValueLiteral():void 0,directives:this.parseConstDirectives()})}parseVariable(){const t=this._lexer.token;return this.expectToken(A.DOLLAR),this.node(t,{kind:G.VARIABLE,name:this.parseName()})}parseSelectionSet(){return this.node(this._lexer.token,{kind:G.SELECTION_SET,selections:this.many(A.BRACE_L,this.parseSelection,A.BRACE_R)})}parseSelection(){return this.peek(A.SPREAD)?this.parseFragment():this.parseField()}parseField(){const t=this._lexer.token,n=this.parseName();let r,i;return this.expectOptionalToken(A.COLON)?(r=n,i=this.parseName()):i=n,this.node(t,{kind:G.FIELD,alias:r,name:i,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(A.BRACE_L)?this.parseSelectionSet():void 0})}parseArguments(t){const n=t?this.parseConstArgument:this.parseArgument;return this.optionalMany(A.PAREN_L,n,A.PAREN_R)}parseArgument(t=!1){const n=this._lexer.token,r=this.parseName();return this.expectToken(A.COLON),this.node(n,{kind:G.ARGUMENT,name:r,value:this.parseValueLiteral(t)})}parseConstArgument(){return this.parseArgument(!0)}parseFragment(){const t=this._lexer.token;this.expectToken(A.SPREAD);const n=this.expectOptionalKeyword("on");return!n&&this.peek(A.NAME)?this.node(t,{kind:G.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1)}):this.node(t,{kind:G.INLINE_FRAGMENT,typeCondition:n?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentDefinition(){const t=this._lexer.token;return this.expectKeyword("fragment"),this._options.allowLegacyFragmentVariables===!0?this.node(t,{kind:G.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()}):this.node(t,{kind:G.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentName(){if(this._lexer.token.value==="on")throw this.unexpected();return this.parseName()}parseValueLiteral(t){const n=this._lexer.token;switch(n.kind){case A.BRACKET_L:return this.parseList(t);case A.BRACE_L:return this.parseObject(t);case A.INT:return this.advanceLexer(),this.node(n,{kind:G.INT,value:n.value});case A.FLOAT:return this.advanceLexer(),this.node(n,{kind:G.FLOAT,value:n.value});case A.STRING:case A.BLOCK_STRING:return this.parseStringLiteral();case A.NAME:switch(this.advanceLexer(),n.value){case"true":return this.node(n,{kind:G.BOOLEAN,value:!0});case"false":return this.node(n,{kind:G.BOOLEAN,value:!1});case"null":return this.node(n,{kind:G.NULL});default:return this.node(n,{kind:G.ENUM,value:n.value})}case A.DOLLAR:if(t)if(this.expectToken(A.DOLLAR),this._lexer.token.kind===A.NAME){const r=this._lexer.token.value;throw at(this._lexer.source,n.start,`Unexpected variable "$${r}" in constant value.`)}else throw this.unexpected(n);return this.parseVariable();default:throw this.unexpected()}}parseConstValueLiteral(){return this.parseValueLiteral(!0)}parseStringLiteral(){const t=this._lexer.token;return this.advanceLexer(),this.node(t,{kind:G.STRING,value:t.value,block:t.kind===A.BLOCK_STRING})}parseList(t){const n=()=>this.parseValueLiteral(t);return this.node(this._lexer.token,{kind:G.LIST,values:this.any(A.BRACKET_L,n,A.BRACKET_R)})}parseObject(t){const n=()=>this.parseObjectField(t);return this.node(this._lexer.token,{kind:G.OBJECT,fields:this.any(A.BRACE_L,n,A.BRACE_R)})}parseObjectField(t){const n=this._lexer.token,r=this.parseName();return this.expectToken(A.COLON),this.node(n,{kind:G.OBJECT_FIELD,name:r,value:this.parseValueLiteral(t)})}parseDirectives(t){const n=[];for(;this.peek(A.AT);)n.push(this.parseDirective(t));return n}parseConstDirectives(){return this.parseDirectives(!0)}parseDirective(t){const n=this._lexer.token;return this.expectToken(A.AT),this.node(n,{kind:G.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(t)})}parseTypeReference(){const t=this._lexer.token;let n;if(this.expectOptionalToken(A.BRACKET_L)){const r=this.parseTypeReference();this.expectToken(A.BRACKET_R),n=this.node(t,{kind:G.LIST_TYPE,type:r})}else n=this.parseNamedType();return this.expectOptionalToken(A.BANG)?this.node(t,{kind:G.NON_NULL_TYPE,type:n}):n}parseNamedType(){return this.node(this._lexer.token,{kind:G.NAMED_TYPE,name:this.parseName()})}peekDescription(){return this.peek(A.STRING)||this.peek(A.BLOCK_STRING)}parseDescription(){if(this.peekDescription())return this.parseStringLiteral()}parseSchemaDefinition(){const t=this._lexer.token,n=this.parseDescription();this.expectKeyword("schema");const r=this.parseConstDirectives(),i=this.many(A.BRACE_L,this.parseOperationTypeDefinition,A.BRACE_R);return this.node(t,{kind:G.SCHEMA_DEFINITION,description:n,directives:r,operationTypes:i})}parseOperationTypeDefinition(){const t=this._lexer.token,n=this.parseOperationType();this.expectToken(A.COLON);const r=this.parseNamedType();return this.node(t,{kind:G.OPERATION_TYPE_DEFINITION,operation:n,type:r})}parseScalarTypeDefinition(){const t=this._lexer.token,n=this.parseDescription();this.expectKeyword("scalar");const r=this.parseName(),i=this.parseConstDirectives();return this.node(t,{kind:G.SCALAR_TYPE_DEFINITION,description:n,name:r,directives:i})}parseObjectTypeDefinition(){const t=this._lexer.token,n=this.parseDescription();this.expectKeyword("type");const r=this.parseName(),i=this.parseImplementsInterfaces(),o=this.parseConstDirectives(),a=this.parseFieldsDefinition();return this.node(t,{kind:G.OBJECT_TYPE_DEFINITION,description:n,name:r,interfaces:i,directives:o,fields:a})}parseImplementsInterfaces(){return this.expectOptionalKeyword("implements")?this.delimitedMany(A.AMP,this.parseNamedType):[]}parseFieldsDefinition(){return this.optionalMany(A.BRACE_L,this.parseFieldDefinition,A.BRACE_R)}parseFieldDefinition(){const t=this._lexer.token,n=this.parseDescription(),r=this.parseName(),i=this.parseArgumentDefs();this.expectToken(A.COLON);const o=this.parseTypeReference(),a=this.parseConstDirectives();return this.node(t,{kind:G.FIELD_DEFINITION,description:n,name:r,arguments:i,type:o,directives:a})}parseArgumentDefs(){return this.optionalMany(A.PAREN_L,this.parseInputValueDef,A.PAREN_R)}parseInputValueDef(){const t=this._lexer.token,n=this.parseDescription(),r=this.parseName();this.expectToken(A.COLON);const i=this.parseTypeReference();let o;this.expectOptionalToken(A.EQUALS)&&(o=this.parseConstValueLiteral());const a=this.parseConstDirectives();return this.node(t,{kind:G.INPUT_VALUE_DEFINITION,description:n,name:r,type:i,defaultValue:o,directives:a})}parseInterfaceTypeDefinition(){const t=this._lexer.token,n=this.parseDescription();this.expectKeyword("interface");const r=this.parseName(),i=this.parseImplementsInterfaces(),o=this.parseConstDirectives(),a=this.parseFieldsDefinition();return this.node(t,{kind:G.INTERFACE_TYPE_DEFINITION,description:n,name:r,interfaces:i,directives:o,fields:a})}parseUnionTypeDefinition(){const t=this._lexer.token,n=this.parseDescription();this.expectKeyword("union");const r=this.parseName(),i=this.parseConstDirectives(),o=this.parseUnionMemberTypes();return this.node(t,{kind:G.UNION_TYPE_DEFINITION,description:n,name:r,directives:i,types:o})}parseUnionMemberTypes(){return this.expectOptionalToken(A.EQUALS)?this.delimitedMany(A.PIPE,this.parseNamedType):[]}parseEnumTypeDefinition(){const t=this._lexer.token,n=this.parseDescription();this.expectKeyword("enum");const r=this.parseName(),i=this.parseConstDirectives(),o=this.parseEnumValuesDefinition();return this.node(t,{kind:G.ENUM_TYPE_DEFINITION,description:n,name:r,directives:i,values:o})}parseEnumValuesDefinition(){return this.optionalMany(A.BRACE_L,this.parseEnumValueDefinition,A.BRACE_R)}parseEnumValueDefinition(){const t=this._lexer.token,n=this.parseDescription(),r=this.parseEnumValueName(),i=this.parseConstDirectives();return this.node(t,{kind:G.ENUM_VALUE_DEFINITION,description:n,name:r,directives:i})}parseEnumValueName(){if(this._lexer.token.value==="true"||this._lexer.token.value==="false"||this._lexer.token.value==="null")throw at(this._lexer.source,this._lexer.token.start,`${gs(this._lexer.token)} is reserved and cannot be used for an enum value.`);return this.parseName()}parseInputObjectTypeDefinition(){const t=this._lexer.token,n=this.parseDescription();this.expectKeyword("input");const r=this.parseName(),i=this.parseConstDirectives(),o=this.parseInputFieldsDefinition();return this.node(t,{kind:G.INPUT_OBJECT_TYPE_DEFINITION,description:n,name:r,directives:i,fields:o})}parseInputFieldsDefinition(){return this.optionalMany(A.BRACE_L,this.parseInputValueDef,A.BRACE_R)}parseTypeSystemExtension(){const t=this._lexer.lookahead();if(t.kind===A.NAME)switch(t.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(t)}parseSchemaExtension(){const t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");const n=this.parseConstDirectives(),r=this.optionalMany(A.BRACE_L,this.parseOperationTypeDefinition,A.BRACE_R);if(n.length===0&&r.length===0)throw this.unexpected();return this.node(t,{kind:G.SCHEMA_EXTENSION,directives:n,operationTypes:r})}parseScalarTypeExtension(){const t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");const n=this.parseName(),r=this.parseConstDirectives();if(r.length===0)throw this.unexpected();return this.node(t,{kind:G.SCALAR_TYPE_EXTENSION,name:n,directives:r})}parseObjectTypeExtension(){const t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");const n=this.parseName(),r=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),o=this.parseFieldsDefinition();if(r.length===0&&i.length===0&&o.length===0)throw this.unexpected();return this.node(t,{kind:G.OBJECT_TYPE_EXTENSION,name:n,interfaces:r,directives:i,fields:o})}parseInterfaceTypeExtension(){const t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");const n=this.parseName(),r=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),o=this.parseFieldsDefinition();if(r.length===0&&i.length===0&&o.length===0)throw this.unexpected();return this.node(t,{kind:G.INTERFACE_TYPE_EXTENSION,name:n,interfaces:r,directives:i,fields:o})}parseUnionTypeExtension(){const t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");const n=this.parseName(),r=this.parseConstDirectives(),i=this.parseUnionMemberTypes();if(r.length===0&&i.length===0)throw this.unexpected();return this.node(t,{kind:G.UNION_TYPE_EXTENSION,name:n,directives:r,types:i})}parseEnumTypeExtension(){const t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");const n=this.parseName(),r=this.parseConstDirectives(),i=this.parseEnumValuesDefinition();if(r.length===0&&i.length===0)throw this.unexpected();return this.node(t,{kind:G.ENUM_TYPE_EXTENSION,name:n,directives:r,values:i})}parseInputObjectTypeExtension(){const t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");const n=this.parseName(),r=this.parseConstDirectives(),i=this.parseInputFieldsDefinition();if(r.length===0&&i.length===0)throw this.unexpected();return this.node(t,{kind:G.INPUT_OBJECT_TYPE_EXTENSION,name:n,directives:r,fields:i})}parseDirectiveDefinition(){const t=this._lexer.token,n=this.parseDescription();this.expectKeyword("directive"),this.expectToken(A.AT);const r=this.parseName(),i=this.parseArgumentDefs(),o=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");const a=this.parseDirectiveLocations();return this.node(t,{kind:G.DIRECTIVE_DEFINITION,description:n,name:r,arguments:i,repeatable:o,locations:a})}parseDirectiveLocations(){return this.delimitedMany(A.PIPE,this.parseDirectiveLocation)}parseDirectiveLocation(){const t=this._lexer.token,n=this.parseName();if(Object.prototype.hasOwnProperty.call(yf,n.value))return n;throw this.unexpected(t)}node(t,n){return this._options.noLocation!==!0&&(n.loc=new VT(t,this._lexer.lastToken,this._lexer.source)),n}peek(t){return this._lexer.token.kind===t}expectToken(t){const n=this._lexer.token;if(n.kind===t)return this.advanceLexer(),n;throw at(this._lexer.source,n.start,`Expected ${I0(t)}, found ${gs(n)}.`)}expectOptionalToken(t){return this._lexer.token.kind===t?(this.advanceLexer(),!0):!1}expectKeyword(t){const n=this._lexer.token;if(n.kind===A.NAME&&n.value===t)this.advanceLexer();else throw at(this._lexer.source,n.start,`Expected "${t}", found ${gs(n)}.`)}expectOptionalKeyword(t){const n=this._lexer.token;return n.kind===A.NAME&&n.value===t?(this.advanceLexer(),!0):!1}unexpected(t){const n=t??this._lexer.token;return at(this._lexer.source,n.start,`Unexpected ${gs(n)}.`)}any(t,n,r){this.expectToken(t);const i=[];for(;!this.expectOptionalToken(r);)i.push(n.call(this));return i}optionalMany(t,n,r){if(this.expectOptionalToken(t)){const i=[];do i.push(n.call(this));while(!this.expectOptionalToken(r));return i}return[]}many(t,n,r){this.expectToken(t);const i=[];do i.push(n.call(this));while(!this.expectOptionalToken(r));return i}delimitedMany(t,n){this.expectOptionalToken(t);const r=[];do r.push(n.call(this));while(this.expectOptionalToken(t));return r}advanceLexer(){const{maxTokens:t}=this._options,n=this._lexer.advance();if(t!==void 0&&n.kind!==A.EOF&&(++this._tokenCounter,this._tokenCounter>t))throw at(this._lexer.source,n.start,`Document contains more that ${t} tokens. Parsing aborted.`)}}function gs(e){const t=e.value;return I0(e.kind)+(t!=null?` "${t}"`:"")}function I0(e){return GT(e)?`"${e}"`:e}function px(e){return`"${e.replace(mx,vx)}"`}const mx=/[\x00-\x1f\x22\x5c\x7f-\x9f]/g;function vx(e){return yx[e.charCodeAt(0)]}const yx=["\\u0000","\\u0001","\\u0002","\\u0003","\\u0004","\\u0005","\\u0006","\\u0007","\\b","\\t","\\n","\\u000B","\\f","\\r","\\u000E","\\u000F","\\u0010","\\u0011","\\u0012","\\u0013","\\u0014","\\u0015","\\u0016","\\u0017","\\u0018","\\u0019","\\u001A","\\u001B","\\u001C","\\u001D","\\u001E","\\u001F","","",'\\"',"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\\\","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\u007F","\\u0080","\\u0081","\\u0082","\\u0083","\\u0084","\\u0085","\\u0086","\\u0087","\\u0088","\\u0089","\\u008A","\\u008B","\\u008C","\\u008D","\\u008E","\\u008F","\\u0090","\\u0091","\\u0092","\\u0093","\\u0094","\\u0095","\\u0096","\\u0097","\\u0098","\\u0099","\\u009A","\\u009B","\\u009C","\\u009D","\\u009E","\\u009F"],rh=Object.freeze({});function Wn(e,t,n=b0){const r=new Map;for(const h of Object.values(G))r.set(h,gx(t,h));let i,o=Array.isArray(e),a=[e],s=-1,l=[],u=e,c,f;const d=[],y=[];do{s++;const h=s===a.length,m=h&&l.length!==0;if(h){if(c=y.length===0?void 0:d[d.length-1],u=f,f=y.pop(),m)if(o){u=u.slice();let S=0;for(const[T,O]of l){const w=T-S;O===null?(u.splice(w,1),S++):u[w]=O}}else{u=Object.defineProperties({},Object.getOwnPropertyDescriptors(u));for(const[S,T]of l)u[S]=T}s=i.index,a=i.keys,l=i.edits,o=i.inArray,i=i.prev}else if(f){if(c=o?s:a[s],u=f[c],u==null)continue;d.push(c)}let p;if(!Array.isArray(u)){var g,v;_m(u)||Vs(!1,`Invalid AST Node: ${nh(u)}.`);const S=h?(g=r.get(u.kind))===null||g===void 0?void 0:g.leave:(v=r.get(u.kind))===null||v===void 0?void 0:v.enter;if(p=S==null?void 0:S.call(t,u,c,f,d,y),p===rh)break;if(p===!1){if(!h){d.pop();continue}}else if(p!==void 0&&(l.push([c,p]),!h))if(_m(p))u=p;else{d.pop();continue}}if(p===void 0&&m&&l.push([c,u]),h)d.pop();else{var E;i={inArray:o,index:s,keys:a,edits:l,prev:i},o=Array.isArray(u),a=o?u:(E=n[u.kind])!==null&&E!==void 0?E:[],s=-1,l=[],f&&y.push(f),f=u}}while(i!==void 0);return l.length!==0?l[l.length-1][1]:e}function gx(e,t){const n=e[t];return typeof n=="object"?n:typeof n=="function"?{enter:n,leave:void 0}:{enter:e.enter,leave:e.leave}}function Ex(e){return Wn(e,Sx)}const wx=80,Sx={Name:{leave:e=>e.value},Variable:{leave:e=>"$"+e.name},Document:{leave:e=>Y(e.definitions,`

`)},OperationDefinition:{leave(e){const t=he("(",Y(e.variableDefinitions,", "),")"),n=Y([e.operation,Y([e.name,t]),Y(e.directives," ")]," ");return(n==="query"?"":n+" ")+e.selectionSet}},VariableDefinition:{leave:({variable:e,type:t,defaultValue:n,directives:r})=>e+": "+t+he(" = ",n)+he(" ",Y(r," "))},SelectionSet:{leave:({selections:e})=>sn(e)},Field:{leave({alias:e,name:t,arguments:n,directives:r,selectionSet:i}){const o=he("",e,": ")+t;let a=o+he("(",Y(n,", "),")");return a.length>wx&&(a=o+he(`(
`,Bs(Y(n,`
`)),`
)`)),Y([a,Y(r," "),i]," ")}},Argument:{leave:({name:e,value:t})=>e+": "+t},FragmentSpread:{leave:({name:e,directives:t})=>"..."+e+he(" ",Y(t," "))},InlineFragment:{leave:({typeCondition:e,directives:t,selectionSet:n})=>Y(["...",he("on ",e),Y(t," "),n]," ")},FragmentDefinition:{leave:({name:e,typeCondition:t,variableDefinitions:n,directives:r,selectionSet:i})=>`fragment ${e}${he("(",Y(n,", "),")")} on ${t} ${he("",Y(r," ")," ")}`+i},IntValue:{leave:({value:e})=>e},FloatValue:{leave:({value:e})=>e},StringValue:{leave:({value:e,block:t})=>t?HT(e):px(e)},BooleanValue:{leave:({value:e})=>e?"true":"false"},NullValue:{leave:()=>"null"},EnumValue:{leave:({value:e})=>e},ListValue:{leave:({values:e})=>"["+Y(e,", ")+"]"},ObjectValue:{leave:({fields:e})=>"{"+Y(e,", ")+"}"},ObjectField:{leave:({name:e,value:t})=>e+": "+t},Directive:{leave:({name:e,arguments:t})=>"@"+e+he("(",Y(t,", "),")")},NamedType:{leave:({name:e})=>e},ListType:{leave:({type:e})=>"["+e+"]"},NonNullType:{leave:({type:e})=>e+"!"},SchemaDefinition:{leave:({description:e,directives:t,operationTypes:n})=>he("",e,`
`)+Y(["schema",Y(t," "),sn(n)]," ")},OperationTypeDefinition:{leave:({operation:e,type:t})=>e+": "+t},ScalarTypeDefinition:{leave:({description:e,name:t,directives:n})=>he("",e,`
`)+Y(["scalar",t,Y(n," ")]," ")},ObjectTypeDefinition:{leave:({description:e,name:t,interfaces:n,directives:r,fields:i})=>he("",e,`
`)+Y(["type",t,he("implements ",Y(n," & ")),Y(r," "),sn(i)]," ")},FieldDefinition:{leave:({description:e,name:t,arguments:n,type:r,directives:i})=>he("",e,`
`)+t+(xm(n)?he(`(
`,Bs(Y(n,`
`)),`
)`):he("(",Y(n,", "),")"))+": "+r+he(" ",Y(i," "))},InputValueDefinition:{leave:({description:e,name:t,type:n,defaultValue:r,directives:i})=>he("",e,`
`)+Y([t+": "+n,he("= ",r),Y(i," ")]," ")},InterfaceTypeDefinition:{leave:({description:e,name:t,interfaces:n,directives:r,fields:i})=>he("",e,`
`)+Y(["interface",t,he("implements ",Y(n," & ")),Y(r," "),sn(i)]," ")},UnionTypeDefinition:{leave:({description:e,name:t,directives:n,types:r})=>he("",e,`
`)+Y(["union",t,Y(n," "),he("= ",Y(r," | "))]," ")},EnumTypeDefinition:{leave:({description:e,name:t,directives:n,values:r})=>he("",e,`
`)+Y(["enum",t,Y(n," "),sn(r)]," ")},EnumValueDefinition:{leave:({description:e,name:t,directives:n})=>he("",e,`
`)+Y([t,Y(n," ")]," ")},InputObjectTypeDefinition:{leave:({description:e,name:t,directives:n,fields:r})=>he("",e,`
`)+Y(["input",t,Y(n," "),sn(r)]," ")},DirectiveDefinition:{leave:({description:e,name:t,arguments:n,repeatable:r,locations:i})=>he("",e,`
`)+"directive @"+t+(xm(n)?he(`(
`,Bs(Y(n,`
`)),`
)`):he("(",Y(n,", "),")"))+(r?" repeatable":"")+" on "+Y(i," | ")},SchemaExtension:{leave:({directives:e,operationTypes:t})=>Y(["extend schema",Y(e," "),sn(t)]," ")},ScalarTypeExtension:{leave:({name:e,directives:t})=>Y(["extend scalar",e,Y(t," ")]," ")},ObjectTypeExtension:{leave:({name:e,interfaces:t,directives:n,fields:r})=>Y(["extend type",e,he("implements ",Y(t," & ")),Y(n," "),sn(r)]," ")},InterfaceTypeExtension:{leave:({name:e,interfaces:t,directives:n,fields:r})=>Y(["extend interface",e,he("implements ",Y(t," & ")),Y(n," "),sn(r)]," ")},UnionTypeExtension:{leave:({name:e,directives:t,types:n})=>Y(["extend union",e,Y(t," "),he("= ",Y(n," | "))]," ")},EnumTypeExtension:{leave:({name:e,directives:t,values:n})=>Y(["extend enum",e,Y(t," "),sn(n)]," ")},InputObjectTypeExtension:{leave:({name:e,directives:t,fields:n})=>Y(["extend input",e,Y(t," "),sn(n)]," ")}};function Y(e,t=""){var n;return(n=e==null?void 0:e.filter(r=>r).join(t))!==null&&n!==void 0?n:""}function sn(e){return he(`{
`,Bs(Y(e,`
`)),`
}`)}function he(e,t,n=""){return t!=null&&t!==""?e+t+n:""}function Bs(e){return he("  ",e.replace(/\n/g,`
  `))}function xm(e){var t;return(t=e==null?void 0:e.some(n=>n.includes(`
`)))!==null&&t!==void 0?t:!1}function bm(e){return e.kind===G.FIELD||e.kind===G.FRAGMENT_SPREAD||e.kind===G.INLINE_FRAGMENT}function Qa(e,t){var n=e.directives;return!n||!n.length?!0:xx(n).every(function(r){var i=r.directive,o=r.ifArgument,a=!1;return o.value.kind==="Variable"?(a=t&&t[o.value.name.value],X(a!==void 0,67,i.name.value)):a=o.value.value,i.name.value==="skip"?!a:a})}function Oa(e,t,n){var r=new Set(e),i=r.size;return Wn(t,{Directive:function(o){if(r.delete(o.name.value)&&(!n||!r.size))return rh}}),n?!r.size:r.size<i}function _x(e){return e&&Oa(["client","export"],e,!0)}function Tx(e){var t=e.name.value;return t==="skip"||t==="include"}function xx(e){var t=[];return e&&e.length&&e.forEach(function(n){if(Tx(n)){var r=n.arguments,i=n.name.value;X(r&&r.length===1,68,i);var o=r[0];X(o.name&&o.name.value==="if",69,i);var a=o.value;X(a&&(a.kind==="Variable"||a.kind==="BooleanValue"),70,i),t.push({directive:n,ifArgument:o})}}),t}const bx=()=>Object.create(null),{forEach:kx,slice:km}=Array.prototype,{hasOwnProperty:Cx}=Object.prototype;let po=class P0{constructor(t=!0,n=bx){this.weakness=t,this.makeData=n}lookup(){return this.lookupArray(arguments)}lookupArray(t){let n=this;return kx.call(t,r=>n=n.getChildTrie(r)),Cx.call(n,"data")?n.data:n.data=this.makeData(km.call(t))}peek(){return this.peekArray(arguments)}peekArray(t){let n=this;for(let r=0,i=t.length;n&&r<i;++r){const o=n.mapFor(t[r],!1);n=o&&o.get(t[r])}return n&&n.data}remove(){return this.removeArray(arguments)}removeArray(t){let n;if(t.length){const r=t[0],i=this.mapFor(r,!1),o=i&&i.get(r);o&&(n=o.removeArray(km.call(t,1)),!o.data&&!o.weak&&!(o.strong&&o.strong.size)&&i.delete(r))}else n=this.data,delete this.data;return n}getChildTrie(t){const n=this.mapFor(t,!0);let r=n.get(t);return r||n.set(t,r=new P0(this.weakness,this.makeData)),r}mapFor(t,n){return this.weakness&&Ox(t)?this.weak||(n?this.weak=new WeakMap:void 0):this.strong||(n?this.strong=new Map:void 0)}};function Ox(e){switch(typeof e){case"object":if(e===null)break;case"function":return!0}return!1}var pi=typeof WeakMap=="function"&&!hn(function(){return navigator.product=="ReactNative"&&!global.HermesInternal}),A0=typeof WeakSet=="function",ih=typeof Symbol=="function"&&typeof Symbol.for=="function",Yl=ih&&Symbol.asyncIterator,Nx=typeof hn(function(){return window.document.createElement})=="function",Rx=hn(function(){return navigator.userAgent.indexOf("jsdom")>=0})||!1,Dx=Nx&&!Rx;function Qe(e){return e!==null&&typeof e=="object"}function Ix(e,t){var n=t,r=[];e.definitions.forEach(function(o){if(o.kind==="OperationDefinition")throw _t(71,o.operation,o.name?" named '".concat(o.name.value,"'"):"");o.kind==="FragmentDefinition"&&r.push(o)}),typeof n>"u"&&(X(r.length===1,72,r.length),n=r[0].name.value);var i=b(b({},e),{definitions:vn([{kind:"OperationDefinition",operation:"query",selectionSet:{kind:"SelectionSet",selections:[{kind:"FragmentSpread",name:{kind:"Name",value:n}}]}}],e.definitions,!0)});return i}function Xl(e){e===void 0&&(e=[]);var t={};return e.forEach(function(n){t[n.name.value]=n}),t}function Kl(e,t){switch(e.kind){case"InlineFragment":return e;case"FragmentSpread":{var n=e.name.value;if(typeof t=="function")return t(n);var r=t&&t[n];return X(r,73,n),r||null}default:return null}}function Px(){}class Ef{constructor(t=1/0,n=Px){this.max=t,this.dispose=n,this.map=new Map,this.newest=null,this.oldest=null}has(t){return this.map.has(t)}get(t){const n=this.getNode(t);return n&&n.value}get size(){return this.map.size}getNode(t){const n=this.map.get(t);if(n&&n!==this.newest){const{older:r,newer:i}=n;i&&(i.older=r),r&&(r.newer=i),n.older=this.newest,n.older.newer=n,n.newer=null,this.newest=n,n===this.oldest&&(this.oldest=i)}return n}set(t,n){let r=this.getNode(t);return r?r.value=n:(r={key:t,value:n,newer:null,older:this.newest},this.newest&&(this.newest.newer=r),this.newest=r,this.oldest=this.oldest||r,this.map.set(t,r),r.value)}clean(){for(;this.oldest&&this.map.size>this.max;)this.delete(this.oldest.key)}delete(t){const n=this.map.get(t);return n?(n===this.newest&&(this.newest=n.older),n===this.oldest&&(this.oldest=n.newer),n.newer&&(n.newer.older=n.older),n.older&&(n.older.newer=n.newer),this.map.delete(t),this.dispose(n.value,t),!0):!1}}function wf(){}const Ax=wf,Lx=typeof WeakRef<"u"?WeakRef:function(e){return{deref:()=>e}},Mx=typeof WeakMap<"u"?WeakMap:Map,Fx=typeof FinalizationRegistry<"u"?FinalizationRegistry:function(){return{register:wf,unregister:wf}},jx=10024;class _l{constructor(t=1/0,n=Ax){this.max=t,this.dispose=n,this.map=new Mx,this.newest=null,this.oldest=null,this.unfinalizedNodes=new Set,this.finalizationScheduled=!1,this.size=0,this.finalize=()=>{const r=this.unfinalizedNodes.values();for(let i=0;i<jx;i++){const o=r.next().value;if(!o)break;this.unfinalizedNodes.delete(o);const a=o.key;delete o.key,o.keyRef=new Lx(a),this.registry.register(a,o,o)}this.unfinalizedNodes.size>0?queueMicrotask(this.finalize):this.finalizationScheduled=!1},this.registry=new Fx(this.deleteNode.bind(this))}has(t){return this.map.has(t)}get(t){const n=this.getNode(t);return n&&n.value}getNode(t){const n=this.map.get(t);if(n&&n!==this.newest){const{older:r,newer:i}=n;i&&(i.older=r),r&&(r.newer=i),n.older=this.newest,n.older.newer=n,n.newer=null,this.newest=n,n===this.oldest&&(this.oldest=i)}return n}set(t,n){let r=this.getNode(t);return r?r.value=n:(r={key:t,value:n,newer:null,older:this.newest},this.newest&&(this.newest.newer=r),this.newest=r,this.oldest=this.oldest||r,this.scheduleFinalization(r),this.map.set(t,r),this.size++,r.value)}clean(){for(;this.oldest&&this.size>this.max;)this.deleteNode(this.oldest)}deleteNode(t){t===this.newest&&(this.newest=t.older),t===this.oldest&&(this.oldest=t.newer),t.newer&&(t.newer.older=t.older),t.older&&(t.older.newer=t.newer),this.size--;const n=t.key||t.keyRef&&t.keyRef.deref();this.dispose(t.value,n),t.keyRef?this.registry.unregister(t):this.unfinalizedNodes.delete(t),n&&this.map.delete(n)}delete(t){const n=this.map.get(t);return n?(this.deleteNode(n),!0):!1}scheduleFinalization(t){this.unfinalizedNodes.add(t),this.finalizationScheduled||(this.finalizationScheduled=!0,queueMicrotask(this.finalize))}}var Gu=new WeakSet;function L0(e){e.size<=(e.max||-1)||Gu.has(e)||(Gu.add(e),setTimeout(function(){e.clean(),Gu.delete(e)},100))}var oh=function(e,t){var n=new _l(e,t);return n.set=function(r,i){var o=_l.prototype.set.call(this,r,i);return L0(this),o},n},zx=function(e,t){var n=new Ef(e,t);return n.set=function(r,i){var o=Ef.prototype.set.call(this,r,i);return L0(this),o},n},Ux=Symbol.for("apollo.cacheSize"),Cn=b({},pf[Ux]),Qr={};function ah(e,t){Qr[e]=t}var Vx=globalThis.__DEV__!==!1?qx:void 0,Bx=globalThis.__DEV__!==!1?Hx:void 0,Qx=globalThis.__DEV__!==!1?M0:void 0;function $x(){var e={parser:1e3,canonicalStringify:1e3,print:2e3,"documentTransform.cache":2e3,"queryManager.getDocumentInfo":2e3,"PersistedQueryLink.persistedQueryHashes":2e3,"fragmentRegistry.transform":2e3,"fragmentRegistry.lookup":1e3,"fragmentRegistry.findFragmentSpreads":4e3,"cache.fragmentQueryDocuments":1e3,"removeTypenameFromVariables.getVariableDefinitions":2e3,"inMemoryCache.maybeBroadcastWatch":5e3,"inMemoryCache.executeSelectionSet":5e4,"inMemoryCache.executeSubSelectedArray":1e4};return Object.fromEntries(Object.entries(e).map(function(t){var n=t[0],r=t[1];return[n,Cn[n]||r]}))}function qx(){var e,t,n,r,i;if(globalThis.__DEV__===!1)throw new Error("only supported in development mode");return{limits:$x(),sizes:b({print:(e=Qr.print)===null||e===void 0?void 0:e.call(Qr),parser:(t=Qr.parser)===null||t===void 0?void 0:t.call(Qr),canonicalStringify:(n=Qr.canonicalStringify)===null||n===void 0?void 0:n.call(Qr),links:_f(this.link),queryManager:{getDocumentInfo:this.queryManager.transformCache.size,documentTransforms:j0(this.queryManager.documentTransform)}},(i=(r=this.cache).getMemoryInternals)===null||i===void 0?void 0:i.call(r))}}function M0(){return{cache:{fragmentQueryDocuments:fr(this.getFragmentDoc)}}}function Hx(){var e=this.config.fragments;return b(b({},M0.apply(this)),{addTypenameDocumentTransform:j0(this.addTypenameTransform),inMemoryCache:{executeSelectionSet:fr(this.storeReader.executeSelectionSet),executeSubSelectedArray:fr(this.storeReader.executeSubSelectedArray),maybeBroadcastWatch:fr(this.maybeBroadcastWatch)},fragmentRegistry:{findFragmentSpreads:fr(e==null?void 0:e.findFragmentSpreads),lookup:fr(e==null?void 0:e.lookup),transform:fr(e==null?void 0:e.transform)}})}function Wx(e){return!!e&&"dirtyKey"in e}function fr(e){return Wx(e)?e.size:void 0}function F0(e){return e!=null}function j0(e){return Sf(e).map(function(t){return{cache:t}})}function Sf(e){return e?vn(vn([fr(e==null?void 0:e.performWork)],Sf(e==null?void 0:e.left),!0),Sf(e==null?void 0:e.right),!0).filter(F0):[]}function _f(e){var t;return e?vn(vn([(t=e==null?void 0:e.getMemoryInternals)===null||t===void 0?void 0:t.call(e)],_f(e==null?void 0:e.left),!0),_f(e==null?void 0:e.right),!0).filter(F0):[]}var br=Object.assign(function(t){return JSON.stringify(t,Gx)},{reset:function(){ji=new zx(Cn.canonicalStringify||1e3)}});globalThis.__DEV__!==!1&&ah("canonicalStringify",function(){return ji.size});var ji;br.reset();function Gx(e,t){if(t&&typeof t=="object"){var n=Object.getPrototypeOf(t);if(n===Object.prototype||n===null){var r=Object.keys(t);if(r.every(Yx))return t;var i=JSON.stringify(r),o=ji.get(i);if(!o){r.sort();var a=JSON.stringify(r);o=ji.get(a)||r,ji.set(i,o),ji.set(a,o)}var s=Object.create(n);return o.forEach(function(l){s[l]=t[l]}),s}}return t}function Yx(e,t,n){return t===0||n[t-1]<=e}function Wi(e){return{__ref:String(e)}}function _e(e){return!!(e&&typeof e=="object"&&typeof e.__ref=="string")}function Xx(e){return Qe(e)&&e.kind==="Document"&&Array.isArray(e.definitions)}function Kx(e){return e.kind==="StringValue"}function Jx(e){return e.kind==="BooleanValue"}function Zx(e){return e.kind==="IntValue"}function eb(e){return e.kind==="FloatValue"}function tb(e){return e.kind==="Variable"}function nb(e){return e.kind==="ObjectValue"}function rb(e){return e.kind==="ListValue"}function ib(e){return e.kind==="EnumValue"}function ob(e){return e.kind==="NullValue"}function io(e,t,n,r){if(Zx(n)||eb(n))e[t.value]=Number(n.value);else if(Jx(n)||Kx(n))e[t.value]=n.value;else if(nb(n)){var i={};n.fields.map(function(a){return io(i,a.name,a.value,r)}),e[t.value]=i}else if(tb(n)){var o=(r||{})[n.name.value];e[t.value]=o}else if(rb(n))e[t.value]=n.values.map(function(a){var s={};return io(s,t,a,r),s[t.value]});else if(ib(n))e[t.value]=n.value;else if(ob(n))e[t.value]=null;else throw _t(82,t.value,n.kind)}function ab(e,t){var n=null;e.directives&&(n={},e.directives.forEach(function(i){n[i.name.value]={},i.arguments&&i.arguments.forEach(function(o){var a=o.name,s=o.value;return io(n[i.name.value],a,s,t)})}));var r=null;return e.arguments&&e.arguments.length&&(r={},e.arguments.forEach(function(i){var o=i.name,a=i.value;return io(r,o,a,t)})),z0(e.name.value,r,n)}var sb=["connection","include","skip","client","rest","export","nonreactive"],Do=br,z0=Object.assign(function(e,t,n){if(t&&n&&n.connection&&n.connection.key)if(n.connection.filter&&n.connection.filter.length>0){var r=n.connection.filter?n.connection.filter:[];r.sort();var i={};return r.forEach(function(s){i[s]=t[s]}),"".concat(n.connection.key,"(").concat(Do(i),")")}else return n.connection.key;var o=e;if(t){var a=Do(t);o+="(".concat(a,")")}return n&&Object.keys(n).forEach(function(s){sb.indexOf(s)===-1&&(n[s]&&Object.keys(n[s]).length?o+="@".concat(s,"(").concat(Do(n[s]),")"):o+="@".concat(s))}),o},{setStringify:function(e){var t=Do;return Do=e,t}});function Jl(e,t){if(e.arguments&&e.arguments.length){var n={};return e.arguments.forEach(function(r){var i=r.name,o=r.value;return io(n,i,o,t)}),n}return null}function Or(e){return e.alias?e.alias.value:e.name.value}function Tf(e,t,n){for(var r,i=0,o=t.selections;i<o.length;i++){var a=o[i];if(Nr(a)){if(a.name.value==="__typename")return e[Or(a)]}else r?r.push(a):r=[a]}if(typeof e.__typename=="string")return e.__typename;if(r)for(var s=0,l=r;s<l.length;s++){var a=l[s],u=Tf(e,Kl(a,n).selectionSet,n);if(typeof u=="string")return u}}function Nr(e){return e.kind==="Field"}function lb(e){return e.kind==="InlineFragment"}function $a(e){X(e&&e.kind==="Document",74);var t=e.definitions.filter(function(n){return n.kind!=="FragmentDefinition"}).map(function(n){if(n.kind!=="OperationDefinition")throw _t(75,n.kind);return n});return X(t.length<=1,76,t.length),e}function qa(e){return $a(e),e.definitions.filter(function(t){return t.kind==="OperationDefinition"})[0]}function xf(e){return e.definitions.filter(function(t){return t.kind==="OperationDefinition"&&!!t.name}).map(function(t){return t.name.value})[0]||null}function Zl(e){return e.definitions.filter(function(t){return t.kind==="FragmentDefinition"})}function U0(e){var t=qa(e);return X(t&&t.operation==="query",77),t}function ub(e){X(e.kind==="Document",78),X(e.definitions.length<=1,79);var t=e.definitions[0];return X(t.kind==="FragmentDefinition",80),t}function Ha(e){$a(e);for(var t,n=0,r=e.definitions;n<r.length;n++){var i=r[n];if(i.kind==="OperationDefinition"){var o=i.operation;if(o==="query"||o==="mutation"||o==="subscription")return i}i.kind==="FragmentDefinition"&&!t&&(t=i)}if(t)return t;throw _t(81)}function sh(e){var t=Object.create(null),n=e&&e.variableDefinitions;return n&&n.length&&n.forEach(function(r){r.defaultValue&&io(t,r.variable.name,r.defaultValue)}),t}const cb=()=>Object.create(null),{forEach:fb,slice:db}=Array.prototype,{hasOwnProperty:hb}=Object.prototype;class lh{constructor(t=!0,n=cb){this.weakness=t,this.makeData=n}lookup(...t){return this.lookupArray(t)}lookupArray(t){let n=this;return fb.call(t,r=>n=n.getChildTrie(r)),hb.call(n,"data")?n.data:n.data=this.makeData(db.call(t))}peek(...t){return this.peekArray(t)}peekArray(t){let n=this;for(let r=0,i=t.length;n&&r<i;++r){const o=this.weakness&&Cm(t[r])?n.weak:n.strong;n=o&&o.get(t[r])}return n&&n.data}getChildTrie(t){const n=this.weakness&&Cm(t)?this.weak||(this.weak=new WeakMap):this.strong||(this.strong=new Map);let r=n.get(t);return r||n.set(t,r=new lh(this.weakness,this.makeData)),r}}function Cm(e){switch(typeof e){case"object":if(e===null)break;case"function":return!0}return!1}let lt=null;const Om={};let pb=1;const mb=()=>class{constructor(){this.id=["slot",pb++,Date.now(),Math.random().toString(36).slice(2)].join(":")}hasValue(){for(let t=lt;t;t=t.parent)if(this.id in t.slots){const n=t.slots[this.id];if(n===Om)break;return t!==lt&&(lt.slots[this.id]=n),!0}return lt&&(lt.slots[this.id]=Om),!1}getValue(){if(this.hasValue())return lt.slots[this.id]}withValue(t,n,r,i){const o={__proto__:null,[this.id]:t},a=lt;lt={parent:a,slots:o};try{return n.apply(i,r)}finally{lt=a}}static bind(t){const n=lt;return function(){const r=lt;try{return lt=n,t.apply(this,arguments)}finally{lt=r}}}static noContext(t,n,r){if(lt){const i=lt;try{return lt=null,t.apply(r,n)}finally{lt=i}}else return t.apply(r,n)}};function Nm(e){try{return e()}catch{}}const Yu="@wry/context:Slot",vb=Nm(()=>globalThis)||Nm(()=>global)||Object.create(null),Rm=vb,V0=Rm[Yu]||Array[Yu]||function(e){try{Object.defineProperty(Rm,Yu,{value:e,enumerable:!1,writable:!1,configurable:!0})}finally{return e}}(mb()),eu=new V0,{hasOwnProperty:yb}=Object.prototype,uh=Array.from||function(e){const t=[];return e.forEach(n=>t.push(n)),t};function ch(e){const{unsubscribe:t}=e;typeof t=="function"&&(e.unsubscribe=void 0,t())}const Na=[],gb=100;function oo(e,t){if(!e)throw new Error(t||"assertion failure")}function B0(e,t){const n=e.length;return n>0&&n===t.length&&e[n-1]===t[n-1]}function Q0(e){switch(e.length){case 0:throw new Error("unknown value");case 1:return e[0];case 2:throw e[1]}}function $0(e){return e.slice(0)}class tu{constructor(t){this.fn=t,this.parents=new Set,this.childValues=new Map,this.dirtyChildren=null,this.dirty=!0,this.recomputing=!1,this.value=[],this.deps=null,++tu.count}peek(){if(this.value.length===1&&!Rr(this))return Dm(this),this.value[0]}recompute(t){return oo(!this.recomputing,"already recomputing"),Dm(this),Rr(this)?Eb(this,t):Q0(this.value)}setDirty(){this.dirty||(this.dirty=!0,q0(this),ch(this))}dispose(){this.setDirty(),X0(this),fh(this,(t,n)=>{t.setDirty(),K0(t,this)})}forget(){this.dispose()}dependOn(t){t.add(this),this.deps||(this.deps=Na.pop()||new Set),this.deps.add(t)}forgetDeps(){this.deps&&(uh(this.deps).forEach(t=>t.delete(this)),this.deps.clear(),Na.push(this.deps),this.deps=null)}}tu.count=0;function Dm(e){const t=eu.getValue();if(t)return e.parents.add(t),t.childValues.has(e)||t.childValues.set(e,[]),Rr(e)?W0(t,e):G0(t,e),t}function Eb(e,t){return X0(e),eu.withValue(e,wb,[e,t]),_b(e,t)&&Sb(e),Q0(e.value)}function wb(e,t){e.recomputing=!0;const{normalizeResult:n}=e;let r;n&&e.value.length===1&&(r=$0(e.value)),e.value.length=0;try{if(e.value[0]=e.fn.apply(null,t),n&&r&&!B0(r,e.value))try{e.value[0]=n(e.value[0],r[0])}catch{}}catch(i){e.value[1]=i}e.recomputing=!1}function Rr(e){return e.dirty||!!(e.dirtyChildren&&e.dirtyChildren.size)}function Sb(e){e.dirty=!1,!Rr(e)&&H0(e)}function q0(e){fh(e,W0)}function H0(e){fh(e,G0)}function fh(e,t){const n=e.parents.size;if(n){const r=uh(e.parents);for(let i=0;i<n;++i)t(r[i],e)}}function W0(e,t){oo(e.childValues.has(t)),oo(Rr(t));const n=!Rr(e);if(!e.dirtyChildren)e.dirtyChildren=Na.pop()||new Set;else if(e.dirtyChildren.has(t))return;e.dirtyChildren.add(t),n&&q0(e)}function G0(e,t){oo(e.childValues.has(t)),oo(!Rr(t));const n=e.childValues.get(t);n.length===0?e.childValues.set(t,$0(t.value)):B0(n,t.value)||e.setDirty(),Y0(e,t),!Rr(e)&&H0(e)}function Y0(e,t){const n=e.dirtyChildren;n&&(n.delete(t),n.size===0&&(Na.length<gb&&Na.push(n),e.dirtyChildren=null))}function X0(e){e.childValues.size>0&&e.childValues.forEach((t,n)=>{K0(e,n)}),e.forgetDeps(),oo(e.dirtyChildren===null)}function K0(e,t){t.parents.delete(e),e.childValues.delete(t),Y0(e,t)}function _b(e,t){if(typeof e.subscribe=="function")try{ch(e),e.unsubscribe=e.subscribe.apply(null,t)}catch{return e.setDirty(),!1}return!0}const Tb={setDirty:!0,dispose:!0,forget:!0};function J0(e){const t=new Map;function n(r){const i=eu.getValue();if(i){let o=t.get(r);o||t.set(r,o=new Set),i.dependOn(o)}}return n.dirty=function(i,o){const a=t.get(i);if(a){const s=o&&yb.call(Tb,o)?o:"setDirty";uh(a).forEach(l=>l[s]()),t.delete(i),ch(a)}},n}let Im;function xb(...e){return(Im||(Im=new lh(typeof WeakMap=="function"))).lookupArray(e)}const Xu=new Set;function Ra(e,{max:t=Math.pow(2,16),keyArgs:n,makeCacheKey:r=xb,normalizeResult:i,subscribe:o,cache:a=Ef}=Object.create(null)){const s=typeof a=="function"?new a(t,d=>d.dispose()):a,l=function(){const d=r.apply(null,n?n.apply(null,arguments):arguments);if(d===void 0)return e.apply(null,arguments);let y=s.get(d);y||(s.set(d,y=new tu(e)),y.normalizeResult=i,y.subscribe=o,y.forget=()=>s.delete(d));const g=y.recompute(Array.prototype.slice.call(arguments));return s.set(d,y),Xu.add(s),eu.hasValue()||(Xu.forEach(v=>v.clean()),Xu.clear()),g};Object.defineProperty(l,"size",{get:()=>s.size,configurable:!1,enumerable:!1}),Object.freeze(l.options={max:t,keyArgs:n,makeCacheKey:r,normalizeResult:i,subscribe:o,cache:s});function u(d){const y=d&&s.get(d);y&&y.setDirty()}l.dirtyKey=u,l.dirty=function(){u(r.apply(null,arguments))};function c(d){const y=d&&s.get(d);if(y)return y.peek()}l.peekKey=c,l.peek=function(){return c(r.apply(null,arguments))};function f(d){return d?s.delete(d):!1}return l.forgetKey=f,l.forget=function(){return f(r.apply(null,arguments))},l.makeCacheKey=r,l.getKey=n?function(){return r.apply(null,n.apply(null,arguments))}:r,Object.freeze(l)}function bb(e){return e}var Z0=function(){function e(t,n){n===void 0&&(n=Object.create(null)),this.resultCache=A0?new WeakSet:new Set,this.transform=t,n.getCacheKey&&(this.getCacheKey=n.getCacheKey),this.cached=n.cache!==!1,this.resetCache()}return e.prototype.getCacheKey=function(t){return[t]},e.identity=function(){return new e(bb,{cache:!1})},e.split=function(t,n,r){return r===void 0&&(r=e.identity()),Object.assign(new e(function(i){var o=t(i)?n:r;return o.transformDocument(i)},{cache:!1}),{left:n,right:r})},e.prototype.resetCache=function(){var t=this;if(this.cached){var n=new po(pi);this.performWork=Ra(e.prototype.performWork.bind(this),{makeCacheKey:function(r){var i=t.getCacheKey(r);if(i)return X(Array.isArray(i),66),n.lookupArray(i)},max:Cn["documentTransform.cache"],cache:_l})}},e.prototype.performWork=function(t){return $a(t),this.transform(t)},e.prototype.transformDocument=function(t){if(this.resultCache.has(t))return t;var n=this.performWork(t);return this.resultCache.add(n),n},e.prototype.concat=function(t){var n=this;return Object.assign(new e(function(r){return t.transformDocument(n.transformDocument(r))},{cache:!1}),{left:this,right:t})},e}(),ta,nu=Object.assign(function(e){var t=ta.get(e);return t||(t=Ex(e),ta.set(e,t)),t},{reset:function(){ta=new oh(Cn.print||2e3)}});nu.reset();globalThis.__DEV__!==!1&&ah("print",function(){return ta?ta.size:0});var $e=Array.isArray;function en(e){return Array.isArray(e)&&e.length>0}var Pm={kind:G.FIELD,name:{kind:G.NAME,value:"__typename"}};function e1(e,t){return!e||e.selectionSet.selections.every(function(n){return n.kind===G.FRAGMENT_SPREAD&&e1(t[n.name.value],t)})}function kb(e){return e1(qa(e)||ub(e),Xl(Zl(e)))?null:e}function Cb(e){var t=new Map,n=new Map;return e.forEach(function(r){r&&(r.name?t.set(r.name,r):r.test&&n.set(r.test,r))}),function(r){var i=t.get(r.name.value);return!i&&n.size&&n.forEach(function(o,a){a(r)&&(i=o)}),i}}function Am(e){var t=new Map;return function(r){r===void 0&&(r=e);var i=t.get(r);return i||t.set(r,i={variables:new Set,fragmentSpreads:new Set}),i}}function t1(e,t){$a(t);for(var n=Am(""),r=Am(""),i=function(h){for(var m=0,p=void 0;m<h.length&&(p=h[m]);++m)if(!$e(p)){if(p.kind===G.OPERATION_DEFINITION)return n(p.name&&p.name.value);if(p.kind===G.FRAGMENT_DEFINITION)return r(p.name.value)}return globalThis.__DEV__!==!1&&X.error(83),null},o=0,a=t.definitions.length-1;a>=0;--a)t.definitions[a].kind===G.OPERATION_DEFINITION&&++o;var s=Cb(e),l=function(h){return en(h)&&h.map(s).some(function(m){return m&&m.remove})},u=new Map,c=!1,f={enter:function(h){if(l(h.directives))return c=!0,null}},d=Wn(t,{Field:f,InlineFragment:f,VariableDefinition:{enter:function(){return!1}},Variable:{enter:function(h,m,p,S,T){var O=i(T);O&&O.variables.add(h.name.value)}},FragmentSpread:{enter:function(h,m,p,S,T){if(l(h.directives))return c=!0,null;var O=i(T);O&&O.fragmentSpreads.add(h.name.value)}},FragmentDefinition:{enter:function(h,m,p,S){u.set(JSON.stringify(S),h)},leave:function(h,m,p,S){var T=u.get(JSON.stringify(S));if(h===T)return h;if(o>0&&h.selectionSet.selections.every(function(O){return O.kind===G.FIELD&&O.name.value==="__typename"}))return r(h.name.value).removed=!0,c=!0,null}},Directive:{leave:function(h){if(s(h))return c=!0,null}}});if(!c)return t;var y=function(h){return h.transitiveVars||(h.transitiveVars=new Set(h.variables),h.removed||h.fragmentSpreads.forEach(function(m){y(r(m)).transitiveVars.forEach(function(p){h.transitiveVars.add(p)})})),h},g=new Set;d.definitions.forEach(function(h){h.kind===G.OPERATION_DEFINITION?y(n(h.name&&h.name.value)).fragmentSpreads.forEach(function(m){g.add(m)}):h.kind===G.FRAGMENT_DEFINITION&&o===0&&!r(h.name.value).removed&&g.add(h.name.value)}),g.forEach(function(h){y(r(h)).fragmentSpreads.forEach(function(m){g.add(m)})});var v=function(h){return!!(!g.has(h)||r(h).removed)},E={enter:function(h){if(v(h.name.value))return null}};return kb(Wn(d,{FragmentSpread:E,FragmentDefinition:E,OperationDefinition:{leave:function(h){if(h.variableDefinitions){var m=y(n(h.name&&h.name.value)).transitiveVars;if(m.size<h.variableDefinitions.length)return b(b({},h),{variableDefinitions:h.variableDefinitions.filter(function(p){return m.has(p.variable.name.value)})})}}}}))}var dh=Object.assign(function(e){return Wn(e,{SelectionSet:{enter:function(t,n,r){if(!(r&&r.kind===G.OPERATION_DEFINITION)){var i=t.selections;if(i){var o=i.some(function(s){return Nr(s)&&(s.name.value==="__typename"||s.name.value.lastIndexOf("__",0)===0)});if(!o){var a=r;if(!(Nr(a)&&a.directives&&a.directives.some(function(s){return s.name.value==="export"})))return b(b({},t),{selections:vn(vn([],i,!0),[Pm],!1)})}}}}}})},{added:function(e){return e===Pm}});function Ob(e){var t=Ha(e),n=t.operation;if(n==="query")return e;var r=Wn(e,{OperationDefinition:{enter:function(i){return b(b({},i),{operation:"query"})}}});return r}function n1(e){$a(e);var t=t1([{test:function(n){return n.name.value==="client"},remove:!0}],e);return t}var Nb=Object.prototype.hasOwnProperty;function Lm(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ru(e)}function ru(e){var t=e[0]||{},n=e.length;if(n>1)for(var r=new Dr,i=1;i<n;++i)t=r.merge(t,e[i]);return t}var Rb=function(e,t,n){return this.merge(e[n],t[n])},Dr=function(){function e(t){t===void 0&&(t=Rb),this.reconciler=t,this.isObject=Qe,this.pastCopies=new Set}return e.prototype.merge=function(t,n){for(var r=this,i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];return Qe(n)&&Qe(t)?(Object.keys(n).forEach(function(a){if(Nb.call(t,a)){var s=t[a];if(n[a]!==s){var l=r.reconciler.apply(r,vn([t,n,a],i,!1));l!==s&&(t=r.shallowCopyForMerge(t),t[a]=l)}}else t=r.shallowCopyForMerge(t),t[a]=n[a]}),t):n},e.prototype.shallowCopyForMerge=function(t){return Qe(t)&&(this.pastCopies.has(t)||(Array.isArray(t)?t=t.slice(0):t=b({__proto__:Object.getPrototypeOf(t)},t),this.pastCopies.add(t))),t},e}();function Db(e,t){var n=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=Ib(e))||t){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ib(e,t){if(e){if(typeof e=="string")return Mm(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Mm(e,t)}}function Mm(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Fm(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function hh(e,t,n){return t&&Fm(e.prototype,t),n&&Fm(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var ph=function(){return typeof Symbol=="function"},mh=function(e){return ph()&&!!Symbol[e]},vh=function(e){return mh(e)?Symbol[e]:"@@"+e};ph()&&!mh("observable")&&(Symbol.observable=Symbol("observable"));var Pb=vh("iterator"),bf=vh("observable"),r1=vh("species");function Tl(e,t){var n=e[t];if(n!=null){if(typeof n!="function")throw new TypeError(n+" is not a function");return n}}function Io(e){var t=e.constructor;return t!==void 0&&(t=t[r1],t===null&&(t=void 0)),t!==void 0?t:Re}function Ab(e){return e instanceof Re}function ao(e){ao.log?ao.log(e):setTimeout(function(){throw e})}function Qs(e){Promise.resolve().then(function(){try{e()}catch(t){ao(t)}})}function i1(e){var t=e._cleanup;if(t!==void 0&&(e._cleanup=void 0,!!t))try{if(typeof t=="function")t();else{var n=Tl(t,"unsubscribe");n&&n.call(t)}}catch(r){ao(r)}}function kf(e){e._observer=void 0,e._queue=void 0,e._state="closed"}function Lb(e){var t=e._queue;if(t){e._queue=void 0,e._state="ready";for(var n=0;n<t.length&&(o1(e,t[n].type,t[n].value),e._state!=="closed");++n);}}function o1(e,t,n){e._state="running";var r=e._observer;try{var i=Tl(r,t);switch(t){case"next":i&&i.call(r,n);break;case"error":if(kf(e),i)i.call(r,n);else throw n;break;case"complete":kf(e),i&&i.call(r);break}}catch(o){ao(o)}e._state==="closed"?i1(e):e._state==="running"&&(e._state="ready")}function Ku(e,t,n){if(e._state!=="closed"){if(e._state==="buffering"){e._queue.push({type:t,value:n});return}if(e._state!=="ready"){e._state="buffering",e._queue=[{type:t,value:n}],Qs(function(){return Lb(e)});return}o1(e,t,n)}}var Mb=function(){function e(n,r){this._cleanup=void 0,this._observer=n,this._queue=void 0,this._state="initializing";var i=new Fb(this);try{this._cleanup=r.call(void 0,i)}catch(o){i.error(o)}this._state==="initializing"&&(this._state="ready")}var t=e.prototype;return t.unsubscribe=function(){this._state!=="closed"&&(kf(this),i1(this))},hh(e,[{key:"closed",get:function(){return this._state==="closed"}}]),e}(),Fb=function(){function e(n){this._subscription=n}var t=e.prototype;return t.next=function(r){Ku(this._subscription,"next",r)},t.error=function(r){Ku(this._subscription,"error",r)},t.complete=function(){Ku(this._subscription,"complete")},hh(e,[{key:"closed",get:function(){return this._subscription._state==="closed"}}]),e}(),Re=function(){function e(n){if(!(this instanceof e))throw new TypeError("Observable cannot be called as a function");if(typeof n!="function")throw new TypeError("Observable initializer must be a function");this._subscriber=n}var t=e.prototype;return t.subscribe=function(r){return(typeof r!="object"||r===null)&&(r={next:r,error:arguments[1],complete:arguments[2]}),new Mb(r,this._subscriber)},t.forEach=function(r){var i=this;return new Promise(function(o,a){if(typeof r!="function"){a(new TypeError(r+" is not a function"));return}function s(){l.unsubscribe(),o()}var l=i.subscribe({next:function(u){try{r(u,s)}catch(c){a(c),l.unsubscribe()}},error:a,complete:o})})},t.map=function(r){var i=this;if(typeof r!="function")throw new TypeError(r+" is not a function");var o=Io(this);return new o(function(a){return i.subscribe({next:function(s){try{s=r(s)}catch(l){return a.error(l)}a.next(s)},error:function(s){a.error(s)},complete:function(){a.complete()}})})},t.filter=function(r){var i=this;if(typeof r!="function")throw new TypeError(r+" is not a function");var o=Io(this);return new o(function(a){return i.subscribe({next:function(s){try{if(!r(s))return}catch(l){return a.error(l)}a.next(s)},error:function(s){a.error(s)},complete:function(){a.complete()}})})},t.reduce=function(r){var i=this;if(typeof r!="function")throw new TypeError(r+" is not a function");var o=Io(this),a=arguments.length>1,s=!1,l=arguments[1],u=l;return new o(function(c){return i.subscribe({next:function(f){var d=!s;if(s=!0,!d||a)try{u=r(u,f)}catch(y){return c.error(y)}else u=f},error:function(f){c.error(f)},complete:function(){if(!s&&!a)return c.error(new TypeError("Cannot reduce an empty sequence"));c.next(u),c.complete()}})})},t.concat=function(){for(var r=this,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];var s=Io(this);return new s(function(l){var u,c=0;function f(d){u=d.subscribe({next:function(y){l.next(y)},error:function(y){l.error(y)},complete:function(){c===o.length?(u=void 0,l.complete()):f(s.from(o[c++]))}})}return f(r),function(){u&&(u.unsubscribe(),u=void 0)}})},t.flatMap=function(r){var i=this;if(typeof r!="function")throw new TypeError(r+" is not a function");var o=Io(this);return new o(function(a){var s=[],l=i.subscribe({next:function(c){if(r)try{c=r(c)}catch(d){return a.error(d)}var f=o.from(c).subscribe({next:function(d){a.next(d)},error:function(d){a.error(d)},complete:function(){var d=s.indexOf(f);d>=0&&s.splice(d,1),u()}});s.push(f)},error:function(c){a.error(c)},complete:function(){u()}});function u(){l.closed&&s.length===0&&a.complete()}return function(){s.forEach(function(c){return c.unsubscribe()}),l.unsubscribe()}})},t[bf]=function(){return this},e.from=function(r){var i=typeof this=="function"?this:e;if(r==null)throw new TypeError(r+" is not an object");var o=Tl(r,bf);if(o){var a=o.call(r);if(Object(a)!==a)throw new TypeError(a+" is not an object");return Ab(a)&&a.constructor===i?a:new i(function(s){return a.subscribe(s)})}if(mh("iterator")&&(o=Tl(r,Pb),o))return new i(function(s){Qs(function(){if(!s.closed){for(var l=Db(o.call(r)),u;!(u=l()).done;){var c=u.value;if(s.next(c),s.closed)return}s.complete()}})});if(Array.isArray(r))return new i(function(s){Qs(function(){if(!s.closed){for(var l=0;l<r.length;++l)if(s.next(r[l]),s.closed)return;s.complete()}})});throw new TypeError(r+" is not observable")},e.of=function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];var a=typeof this=="function"?this:e;return new a(function(s){Qs(function(){if(!s.closed){for(var l=0;l<i.length;++l)if(s.next(i[l]),s.closed)return;s.complete()}})})},hh(e,null,[{key:r1,get:function(){return this}}]),e}();ph()&&Object.defineProperty(Re,Symbol("extensions"),{value:{symbol:bf,hostReportError:ao},configurable:!0});function jb(e){var t,n=e.Symbol;if(typeof n=="function")if(n.observable)t=n.observable;else{typeof n.for=="function"?t=n.for("https://github.com/benlesh/symbol-observable"):t=n("https://github.com/benlesh/symbol-observable");try{n.observable=t}catch{}}else t="@@observable";return t}var _i;typeof self<"u"?_i=self:typeof window<"u"?_i=window:typeof global<"u"?_i=global:typeof Gf<"u"?_i=Gf:_i=Function("return this")();jb(_i);var jm=Re.prototype,zm="@@observable";jm[zm]||(jm[zm]=function(){return this});var zb=Object.prototype.toString;function a1(e){return Cf(e)}function Cf(e,t){switch(zb.call(e)){case"[object Array]":{if(t=t||new Map,t.has(e))return t.get(e);var n=e.slice(0);return t.set(e,n),n.forEach(function(i,o){n[o]=Cf(i,t)}),n}case"[object Object]":{if(t=t||new Map,t.has(e))return t.get(e);var r=Object.create(Object.getPrototypeOf(e));return t.set(e,r),Object.keys(e).forEach(function(i){r[i]=Cf(e[i],t)}),r}default:return e}}function Ub(e){var t=new Set([e]);return t.forEach(function(n){Qe(n)&&Vb(n)===n&&Object.getOwnPropertyNames(n).forEach(function(r){Qe(n[r])&&t.add(n[r])})}),e}function Vb(e){if(globalThis.__DEV__!==!1&&!Object.isFrozen(e))try{Object.freeze(e)}catch(t){if(t instanceof TypeError)return null;throw t}return e}function Da(e){return globalThis.__DEV__!==!1&&Ub(e),e}function na(e,t,n){var r=[];e.forEach(function(i){return i[t]&&r.push(i)}),r.forEach(function(i){return i[t](n)})}function Ju(e,t,n){return new Re(function(r){var i={then:function(l){return new Promise(function(u){return u(l())})}};function o(l,u){return function(c){if(l){var f=function(){return r.closed?0:l(c)};i=i.then(f,f).then(function(d){return r.next(d)},function(d){return r.error(d)})}else r[u](c)}}var a={next:o(t,"next"),error:o(n,"error"),complete:function(){i.then(function(){return r.complete()})}},s=e.subscribe(a);return function(){return s.unsubscribe()}})}function s1(e){function t(n){Object.defineProperty(e,n,{value:Re})}return ih&&Symbol.species&&t(Symbol.species),t("@@species"),e}function Um(e){return e&&typeof e.then=="function"}var Ti=function(e){Nn(t,e);function t(n){var r=e.call(this,function(i){return r.addObserver(i),function(){return r.removeObserver(i)}})||this;return r.observers=new Set,r.promise=new Promise(function(i,o){r.resolve=i,r.reject=o}),r.handlers={next:function(i){r.sub!==null&&(r.latest=["next",i],r.notify("next",i),na(r.observers,"next",i))},error:function(i){var o=r.sub;o!==null&&(o&&setTimeout(function(){return o.unsubscribe()}),r.sub=null,r.latest=["error",i],r.reject(i),r.notify("error",i),na(r.observers,"error",i))},complete:function(){var i=r,o=i.sub,a=i.sources,s=a===void 0?[]:a;if(o!==null){var l=s.shift();l?Um(l)?l.then(function(u){return r.sub=u.subscribe(r.handlers)},r.handlers.error):r.sub=l.subscribe(r.handlers):(o&&setTimeout(function(){return o.unsubscribe()}),r.sub=null,r.latest&&r.latest[0]==="next"?r.resolve(r.latest[1]):r.resolve(),r.notify("complete"),na(r.observers,"complete"))}}},r.nextResultListeners=new Set,r.cancel=function(i){r.reject(i),r.sources=[],r.handlers.complete()},r.promise.catch(function(i){}),typeof n=="function"&&(n=[new Re(n)]),Um(n)?n.then(function(i){return r.start(i)},r.handlers.error):r.start(n),r}return t.prototype.start=function(n){this.sub===void 0&&(this.sources=Array.from(n),this.handlers.complete())},t.prototype.deliverLastMessage=function(n){if(this.latest){var r=this.latest[0],i=n[r];i&&i.call(n,this.latest[1]),this.sub===null&&r==="next"&&n.complete&&n.complete()}},t.prototype.addObserver=function(n){this.observers.has(n)||(this.deliverLastMessage(n),this.observers.add(n))},t.prototype.removeObserver=function(n){this.observers.delete(n)&&this.observers.size<1&&this.handlers.complete()},t.prototype.notify=function(n,r){var i=this.nextResultListeners;i.size&&(this.nextResultListeners=new Set,i.forEach(function(o){return o(n,r)}))},t.prototype.beforeNext=function(n){var r=!1;this.nextResultListeners.add(function(i,o){r||(r=!0,n(i,o))})},t}(Re);s1(Ti);function Gi(e){return"incremental"in e}function Bb(e){return"hasNext"in e&&"data"in e}function Qb(e){return Gi(e)||Bb(e)}function $b(e){return Qe(e)&&"payload"in e}function l1(e,t){var n=e,r=new Dr;return Gi(t)&&en(t.incremental)&&t.incremental.forEach(function(i){for(var o=i.data,a=i.path,s=a.length-1;s>=0;--s){var l=a[s],u=!isNaN(+l),c=u?[]:{};c[l]=o,o=c}n=r.merge(n,o)}),n}function $s(e){var t=Of(e);return en(t)}function Of(e){var t=en(e.errors)?e.errors.slice(0):[];return Gi(e)&&en(e.incremental)&&e.incremental.forEach(function(n){n.errors&&t.push.apply(t,n.errors)}),t}function ci(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Object.create(null);return e.forEach(function(r){r&&Object.keys(r).forEach(function(i){var o=r[i];o!==void 0&&(n[i]=o)})}),n}function ti(e,t){return ci(e,t,t.variables&&{variables:ci(b(b({},e&&e.variables),t.variables))})}function Zu(e){return new Re(function(t){t.error(e)})}var u1=function(e,t,n){var r=new Error(n);throw r.name="ServerError",r.response=e,r.statusCode=e.status,r.result=t,r};function qb(e){for(var t=["query","operationName","variables","extensions","context"],n=0,r=Object.keys(e);n<r.length;n++){var i=r[n];if(t.indexOf(i)<0)throw _t(43,i)}return e}function Hb(e,t){var n=b({},e),r=function(o){typeof o=="function"?n=b(b({},n),o(n)):n=b(b({},n),o)},i=function(){return b({},n)};return Object.defineProperty(t,"setContext",{enumerable:!1,value:r}),Object.defineProperty(t,"getContext",{enumerable:!1,value:i}),t}function Wb(e){var t={variables:e.variables||{},extensions:e.extensions||{},operationName:e.operationName,query:e.query};return t.operationName||(t.operationName=typeof t.query!="string"?xf(t.query)||void 0:""),t}function Gb(e,t){var n=b({},e),r=new Set(Object.keys(e));return Wn(t,{Variable:function(i,o,a){a&&a.kind!=="VariableDefinition"&&r.delete(i.name.value)}}),r.forEach(function(i){delete n[i]}),n}function Vm(e,t){return t?t(e):Re.of()}function Po(e){return typeof e=="function"?new Wa(e):e}function Es(e){return e.request.length<=1}var Wa=function(){function e(t){t&&(this.request=t)}return e.empty=function(){return new e(function(){return Re.of()})},e.from=function(t){return t.length===0?e.empty():t.map(Po).reduce(function(n,r){return n.concat(r)})},e.split=function(t,n,r){var i=Po(n),o=Po(r||new e(Vm)),a;return Es(i)&&Es(o)?a=new e(function(s){return t(s)?i.request(s)||Re.of():o.request(s)||Re.of()}):a=new e(function(s,l){return t(s)?i.request(s,l)||Re.of():o.request(s,l)||Re.of()}),Object.assign(a,{left:i,right:o})},e.execute=function(t,n){return t.request(Hb(n.context,Wb(qb(n))))||Re.of()},e.concat=function(t,n){var r=Po(t);if(Es(r))return globalThis.__DEV__!==!1&&X.warn(35,r),r;var i=Po(n),o;return Es(i)?o=new e(function(a){return r.request(a,function(s){return i.request(s)||Re.of()})||Re.of()}):o=new e(function(a,s){return r.request(a,function(l){return i.request(l,s)||Re.of()})||Re.of()}),Object.assign(o,{left:r,right:i})},e.prototype.split=function(t,n,r){return this.concat(e.split(t,n,r||new e(Vm)))},e.prototype.concat=function(t){return e.concat(this,t)},e.prototype.request=function(t,n){throw _t(36)},e.prototype.onError=function(t,n){if(n&&n.error)return n.error(t),!1;throw t},e.prototype.setOnError=function(t){return this.onError=t,this},e}(),Nf=Wa.execute;function Yb(e){var t,n=e[Symbol.asyncIterator]();return t={next:function(){return n.next()}},t[Symbol.asyncIterator]=function(){return this},t}function Xb(e){var t=null,n=null,r=!1,i=[],o=[];function a(f){if(!n){if(o.length){var d=o.shift();if(Array.isArray(d)&&d[0])return d[0]({value:f,done:!1})}i.push(f)}}function s(f){n=f;var d=o.slice();d.forEach(function(y){y[1](f)}),!t||t()}function l(){r=!0;var f=o.slice();f.forEach(function(d){d[0]({value:void 0,done:!0})}),!t||t()}t=function(){t=null,e.removeListener("data",a),e.removeListener("error",s),e.removeListener("end",l),e.removeListener("finish",l),e.removeListener("close",l)},e.on("data",a),e.on("error",s),e.on("end",l),e.on("finish",l),e.on("close",l);function u(){return new Promise(function(f,d){if(n)return d(n);if(i.length)return f({value:i.shift(),done:!1});if(r)return f({value:void 0,done:!0});o.push([f,d])})}var c={next:function(){return u()}};return Yl&&(c[Symbol.asyncIterator]=function(){return this}),c}function Kb(e){var t=!1,n={next:function(){return t?Promise.resolve({value:void 0,done:!0}):(t=!0,new Promise(function(r,i){e.then(function(o){r({value:o,done:!1})}).catch(i)}))}};return Yl&&(n[Symbol.asyncIterator]=function(){return this}),n}function Bm(e){var t={next:function(){return e.read()}};return Yl&&(t[Symbol.asyncIterator]=function(){return this}),t}function Jb(e){return!!e.body}function Zb(e){return!!e.getReader}function ek(e){return!!(Yl&&e[Symbol.asyncIterator])}function tk(e){return!!e.stream}function nk(e){return!!e.arrayBuffer}function rk(e){return!!e.pipe}function ik(e){var t=e;if(Jb(e)&&(t=e.body),ek(t))return Yb(t);if(Zb(t))return Bm(t.getReader());if(tk(t))return Bm(t.stream().getReader());if(nk(t))return Kb(t.arrayBuffer());if(rk(t))return Xb(t);throw new Error("Unknown body type for responseIterator. Please pass a streamable response.")}var yh=Symbol();function ok(e){return e.extensions?Array.isArray(e.extensions[yh]):!1}function ak(e){return e.hasOwnProperty("graphQLErrors")}var sk=function(e){var t=vn(vn(vn([],e.graphQLErrors,!0),e.clientErrors,!0),e.protocolErrors,!0);return e.networkError&&t.push(e.networkError),t.map(function(n){return Qe(n)&&n.message||"Error message not found."}).join(`
`)},jn=function(e){Nn(t,e);function t(n){var r=n.graphQLErrors,i=n.protocolErrors,o=n.clientErrors,a=n.networkError,s=n.errorMessage,l=n.extraInfo,u=e.call(this,s)||this;return u.name="ApolloError",u.graphQLErrors=r||[],u.protocolErrors=i||[],u.clientErrors=o||[],u.networkError=a||null,u.message=s||sk(u),u.extraInfo=l,u.__proto__=t.prototype,u}return t}(Error),Qm=Object.prototype.hasOwnProperty;function lk(e,t){return ur(this,void 0,void 0,function(){var n,r,i,o,a,s,l,u,c,f,d,y,g,v,E,h,m,p,S,T,O,w,k,I;return cr(this,function(P){switch(P.label){case 0:if(TextDecoder===void 0)throw new Error("TextDecoder must be defined in the environment: please import a polyfill.");n=new TextDecoder("utf-8"),r=(I=e.headers)===null||I===void 0?void 0:I.get("content-type"),i="boundary=",o=r!=null&&r.includes(i)?r==null?void 0:r.substring((r==null?void 0:r.indexOf(i))+i.length).replace(/['"]/g,"").replace(/\;(.*)/gm,"").trim():"-",a=`\r
--`.concat(o),s="",l=ik(e),u=!0,P.label=1;case 1:return u?[4,l.next()]:[3,3];case 2:for(c=P.sent(),f=c.value,d=c.done,y=typeof f=="string"?f:n.decode(f),g=s.length-a.length+1,u=!d,s+=y,v=s.indexOf(a,g);v>-1;){if(E=void 0,w=[s.slice(0,v),s.slice(v+a.length)],E=w[0],s=w[1],h=E.indexOf(`\r
\r
`),m=uk(E.slice(0,h)),p=m["content-type"],p&&p.toLowerCase().indexOf("application/json")===-1)throw new Error("Unsupported patch content type: application/json is required.");if(S=E.slice(h),S){if(T=c1(e,S),Object.keys(T).length>1||"data"in T||"incremental"in T||"errors"in T||"payload"in T)if($b(T)){if(O={},"payload"in T){if(Object.keys(T).length===1&&T.payload===null)return[2];O=b({},T.payload)}"errors"in T&&(O=b(b({},O),{extensions:b(b({},"extensions"in O?O.extensions:null),(k={},k[yh]=T.errors,k))})),t(O)}else t(T);else if(Object.keys(T).length===1&&"hasNext"in T&&!T.hasNext)return[2]}v=s.indexOf(a)}return[3,1];case 3:return[2]}})})}function uk(e){var t={};return e.split(`
`).forEach(function(n){var r=n.indexOf(":");if(r>-1){var i=n.slice(0,r).trim().toLowerCase(),o=n.slice(r+1).trim();t[i]=o}}),t}function c1(e,t){if(e.status>=300){var n=function(){try{return JSON.parse(t)}catch{return t}};u1(e,n(),"Response not successful: Received status code ".concat(e.status))}try{return JSON.parse(t)}catch(i){var r=i;throw r.name="ServerParseError",r.response=e,r.statusCode=e.status,r.bodyText=t,r}}function ck(e,t){e.result&&e.result.errors&&e.result.data&&t.next(e.result),t.error(e)}function fk(e){return function(t){return t.text().then(function(n){return c1(t,n)}).then(function(n){return!Array.isArray(n)&&!Qm.call(n,"data")&&!Qm.call(n,"errors")&&u1(t,n,"Server response was missing for query '".concat(Array.isArray(e)?e.map(function(r){return r.operationName}):e.operationName,"'.")),n})}}var Rf=function(e,t){var n;try{n=JSON.stringify(e)}catch(i){var r=_t(39,t,i.message);throw r.parseError=i,r}return n},dk={includeQuery:!0,includeExtensions:!1,preserveHeaderCase:!1},hk={accept:"*/*","content-type":"application/json"},pk={method:"POST"},mk={http:dk,headers:hk,options:pk},vk=function(e,t){return t(e)};function yk(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={},o={};n.forEach(function(f){i=b(b(b({},i),f.options),{headers:b(b({},i.headers),f.headers)}),f.credentials&&(i.credentials=f.credentials),o=b(b({},o),f.http)}),i.headers&&(i.headers=gk(i.headers,o.preserveHeaderCase));var a=e.operationName,s=e.extensions,l=e.variables,u=e.query,c={operationName:a,variables:l};return o.includeExtensions&&(c.extensions=s),o.includeQuery&&(c.query=t(u,nu)),{options:i,body:c}}function gk(e,t){if(!t){var n=Object.create(null);return Object.keys(Object(e)).forEach(function(o){n[o.toLowerCase()]=e[o]}),n}var r=Object.create(null);Object.keys(Object(e)).forEach(function(o){r[o.toLowerCase()]={originalName:o,value:e[o]}});var i=Object.create(null);return Object.keys(r).forEach(function(o){i[r[o].originalName]=r[o].value}),i}var Ek=function(e){if(!e&&typeof fetch>"u")throw _t(37)},wk=function(e,t){var n=e.getContext(),r=n.uri;return r||(typeof t=="function"?t(e):t||"/graphql")};function Sk(e,t){var n=[],r=function(f,d){n.push("".concat(f,"=").concat(encodeURIComponent(d)))};if("query"in t&&r("query",t.query),t.operationName&&r("operationName",t.operationName),t.variables){var i=void 0;try{i=Rf(t.variables,"Variables map")}catch(f){return{parseError:f}}r("variables",i)}if(t.extensions){var o=void 0;try{o=Rf(t.extensions,"Extensions map")}catch(f){return{parseError:f}}r("extensions",o)}var a="",s=e,l=e.indexOf("#");l!==-1&&(a=e.substr(l),s=e.substr(0,l));var u=s.indexOf("?")===-1?"?":"&",c=s+u+n.join("&")+a;return{newURI:c}}var $m=hn(function(){return fetch}),_k=function(e){e===void 0&&(e={});var t=e.uri,n=t===void 0?"/graphql":t,r=e.fetch,i=e.print,o=i===void 0?vk:i,a=e.includeExtensions,s=e.preserveHeaderCase,l=e.useGETForQueries,u=e.includeUnusedVariables,c=u===void 0?!1:u,f=kn(e,["uri","fetch","print","includeExtensions","preserveHeaderCase","useGETForQueries","includeUnusedVariables"]);globalThis.__DEV__!==!1&&Ek(r||$m);var d={http:{includeExtensions:a,preserveHeaderCase:s},options:f.fetchOptions,credentials:f.credentials,headers:f.headers};return new Wa(function(y){var g=wk(y,n),v=y.getContext(),E={};if(v.clientAwareness){var h=v.clientAwareness,m=h.name,p=h.version;m&&(E["apollographql-client-name"]=m),p&&(E["apollographql-client-version"]=p)}var S=b(b({},E),v.headers),T={http:v.http,options:v.fetchOptions,credentials:v.credentials,headers:S};if(Oa(["client"],y.query)){var O=n1(y.query);if(!O)return Zu(new Error("HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`."));y.query=O}var w=yk(y,o,mk,d,T),k=w.options,I=w.body;I.variables&&!c&&(I.variables=Gb(I.variables,y.query));var P;!k.signal&&typeof AbortController<"u"&&(P=new AbortController,k.signal=P.signal);var K=function(V){return V.kind==="OperationDefinition"&&V.operation==="mutation"},se=function(V){return V.kind==="OperationDefinition"&&V.operation==="subscription"},ee=se(Ha(y.query)),le=Oa(["defer"],y.query);if(l&&!y.query.definitions.some(K)&&(k.method="GET"),le||ee){k.headers=k.headers||{};var Ae="multipart/mixed;";ee&&le&&globalThis.__DEV__!==!1&&X.warn(38),ee?Ae+="boundary=graphql;subscriptionSpec=1.0,application/json":le&&(Ae+="deferSpec=20220824,application/json"),k.headers.accept=Ae}if(k.method==="GET"){var rt=Sk(g,I),Fe=rt.newURI,M=rt.parseError;if(M)return Zu(M);g=Fe}else try{k.body=Rf(I,"Payload")}catch(V){return Zu(V)}return new Re(function(V){var U=r||hn(function(){return fetch})||$m,ae=V.next.bind(V);return U(g,k).then(function(q){var Ee;y.setContext({response:q});var te=(Ee=q.headers)===null||Ee===void 0?void 0:Ee.get("content-type");return te!==null&&/^multipart\/mixed/i.test(te)?lk(q,ae):fk(y)(q).then(ae)}).then(function(){P=void 0,V.complete()}).catch(function(q){P=void 0,ck(q,V)}),function(){P&&P.abort()}})})},Tk=function(e){Nn(t,e);function t(n){n===void 0&&(n={});var r=e.call(this,_k(n).request)||this;return r.options=n,r}return t}(Wa);const{toString:qm,hasOwnProperty:xk}=Object.prototype,Hm=Function.prototype.toString,Df=new Map;function we(e,t){try{return If(e,t)}finally{Df.clear()}}function If(e,t){if(e===t)return!0;const n=qm.call(e),r=qm.call(t);if(n!==r)return!1;switch(n){case"[object Array]":if(e.length!==t.length)return!1;case"[object Object]":{if(Gm(e,t))return!0;const i=Wm(e),o=Wm(t),a=i.length;if(a!==o.length)return!1;for(let s=0;s<a;++s)if(!xk.call(t,i[s]))return!1;for(let s=0;s<a;++s){const l=i[s];if(!If(e[l],t[l]))return!1}return!0}case"[object Error]":return e.name===t.name&&e.message===t.message;case"[object Number]":if(e!==e)return t!==t;case"[object Boolean]":case"[object Date]":return+e==+t;case"[object RegExp]":case"[object String]":return e==`${t}`;case"[object Map]":case"[object Set]":{if(e.size!==t.size)return!1;if(Gm(e,t))return!0;const i=e.entries(),o=n==="[object Map]";for(;;){const a=i.next();if(a.done)break;const[s,l]=a.value;if(!t.has(s)||o&&!If(l,t.get(s)))return!1}return!0}case"[object Uint16Array]":case"[object Uint8Array]":case"[object Uint32Array]":case"[object Int32Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object ArrayBuffer]":e=new Uint8Array(e),t=new Uint8Array(t);case"[object DataView]":{let i=e.byteLength;if(i===t.byteLength)for(;i--&&e[i]===t[i];);return i===-1}case"[object AsyncFunction]":case"[object GeneratorFunction]":case"[object AsyncGeneratorFunction]":case"[object Function]":{const i=Hm.call(e);return i!==Hm.call(t)?!1:!Ck(i,kk)}}return!1}function Wm(e){return Object.keys(e).filter(bk,e)}function bk(e){return this[e]!==void 0}const kk="{ [native code] }";function Ck(e,t){const n=e.length-t.length;return n>=0&&e.indexOf(t,n)===n}function Gm(e,t){let n=Df.get(e);if(n){if(n.has(t))return!0}else Df.set(e,n=new Set);return n.add(t),!1}var f1=function(){function e(){this.assumeImmutableResults=!1,this.getFragmentDoc=Ra(Ix,{max:Cn["cache.fragmentQueryDocuments"]||1e3,cache:_l})}return e.prototype.batch=function(t){var n=this,r=typeof t.optimistic=="string"?t.optimistic:t.optimistic===!1?null:void 0,i;return this.performTransaction(function(){return i=t.update(n)},r),i},e.prototype.recordOptimisticTransaction=function(t,n){this.performTransaction(t,n)},e.prototype.transformDocument=function(t){return t},e.prototype.transformForLink=function(t){return t},e.prototype.identify=function(t){},e.prototype.gc=function(){return[]},e.prototype.modify=function(t){return!1},e.prototype.readQuery=function(t,n){return n===void 0&&(n=!!t.optimistic),this.read(b(b({},t),{rootId:t.id||"ROOT_QUERY",optimistic:n}))},e.prototype.watchFragment=function(t){var n=this,r=t.fragment,i=t.fragmentName,o=t.from,a=t.optimistic,s=a===void 0?!0:a,l={returnPartialData:!0,id:typeof o=="string"?o:this.identify(o),query:this.getFragmentDoc(r,i),optimistic:s},u;return new Re(function(c){return n.watch(b(b({},l),{immediate:!0,query:n.getFragmentDoc(r,i),callback:function(f){if(!we(f,u)){var d={data:f.result,complete:!!f.complete};f.missing&&(d.missing=ru(f.missing.map(function(y){return y.missing}))),u=f,c.next(d)}}}))})},e.prototype.readFragment=function(t,n){return n===void 0&&(n=!!t.optimistic),this.read(b(b({},t),{query:this.getFragmentDoc(t.fragment,t.fragmentName),rootId:t.id,optimistic:n}))},e.prototype.writeQuery=function(t){var n=t.id,r=t.data,i=kn(t,["id","data"]);return this.write(Object.assign(i,{dataId:n||"ROOT_QUERY",result:r}))},e.prototype.writeFragment=function(t){var n=t.id,r=t.data,i=t.fragment,o=t.fragmentName,a=kn(t,["id","data","fragment","fragmentName"]);return this.write(Object.assign(a,{query:this.getFragmentDoc(i,o),dataId:n,result:r}))},e.prototype.updateQuery=function(t,n){return this.batch({update:function(r){var i=r.readQuery(t),o=n(i);return o==null?i:(r.writeQuery(b(b({},t),{data:o})),o)}})},e.prototype.updateFragment=function(t,n){return this.batch({update:function(r){var i=r.readFragment(t),o=n(i);return o==null?i:(r.writeFragment(b(b({},t),{data:o})),o)}})},e}();globalThis.__DEV__!==!1&&(f1.prototype.getMemoryInternals=Qx);var d1=function(e){Nn(t,e);function t(n,r,i,o){var a,s=e.call(this,n)||this;if(s.message=n,s.path=r,s.query=i,s.variables=o,Array.isArray(s.path)){s.missing=s.message;for(var l=s.path.length-1;l>=0;--l)s.missing=(a={},a[s.path[l]]=s.missing,a)}else s.missing=s.path;return s.__proto__=t.prototype,s}return t}(Error),it=Object.prototype.hasOwnProperty;function Ao(e){return e==null}function h1(e,t){var n=e.__typename,r=e.id,i=e._id;if(typeof n=="string"&&(t&&(t.keyObject=Ao(r)?Ao(i)?void 0:{_id:i}:{id:r}),Ao(r)&&!Ao(i)&&(r=i),!Ao(r)))return"".concat(n,":").concat(typeof r=="number"||typeof r=="string"?r:JSON.stringify(r))}var p1={dataIdFromObject:h1,addTypename:!0,resultCaching:!0,canonizeResults:!1};function Ok(e){return ci(p1,e)}function m1(e){var t=e.canonizeResults;return t===void 0?p1.canonizeResults:t}function Nk(e,t){return _e(t)?e.get(t.__ref,"__typename"):t&&t.__typename}var v1=/^[_a-z][_0-9a-z]*/i;function Ir(e){var t=e.match(v1);return t?t[0]:e}function Pf(e,t,n){return Qe(t)?$e(t)?t.every(function(r){return Pf(e,r,n)}):e.selections.every(function(r){if(Nr(r)&&Qa(r,n)){var i=Or(r);return it.call(t,i)&&(!r.selectionSet||Pf(r.selectionSet,t[i],n))}return!0}):!1}function zi(e){return Qe(e)&&!_e(e)&&!$e(e)}function Rk(){return new Dr}function y1(e,t){var n=Xl(Zl(e));return{fragmentMap:n,lookupFragment:function(r){var i=n[r];return!i&&t&&(i=t.lookup(r)),i||null}}}var qs=Object.create(null),ec=function(){return qs},Ym=Object.create(null),Ia=function(){function e(t,n){var r=this;this.policies=t,this.group=n,this.data=Object.create(null),this.rootIds=Object.create(null),this.refs=Object.create(null),this.getFieldValue=function(i,o){return Da(_e(i)?r.get(i.__ref,o):i&&i[o])},this.canRead=function(i){return _e(i)?r.has(i.__ref):typeof i=="object"},this.toReference=function(i,o){if(typeof i=="string")return Wi(i);if(_e(i))return i;var a=r.policies.identify(i)[0];if(a){var s=Wi(a);return o&&r.merge(a,i),s}}}return e.prototype.toObject=function(){return b({},this.data)},e.prototype.has=function(t){return this.lookup(t,!0)!==void 0},e.prototype.get=function(t,n){if(this.group.depend(t,n),it.call(this.data,t)){var r=this.data[t];if(r&&it.call(r,n))return r[n]}if(n==="__typename"&&it.call(this.policies.rootTypenamesById,t))return this.policies.rootTypenamesById[t];if(this instanceof ar)return this.parent.get(t,n)},e.prototype.lookup=function(t,n){if(n&&this.group.depend(t,"__exists"),it.call(this.data,t))return this.data[t];if(this instanceof ar)return this.parent.lookup(t,n);if(this.policies.rootTypenamesById[t])return Object.create(null)},e.prototype.merge=function(t,n){var r=this,i;_e(t)&&(t=t.__ref),_e(n)&&(n=n.__ref);var o=typeof t=="string"?this.lookup(i=t):t,a=typeof n=="string"?this.lookup(i=n):n;if(a){X(typeof i=="string",1);var s=new Dr(Ik).merge(o,a);if(this.data[i]=s,s!==o&&(delete this.refs[i],this.group.caching)){var l=Object.create(null);o||(l.__exists=1),Object.keys(a).forEach(function(u){if(!o||o[u]!==s[u]){l[u]=1;var c=Ir(u);c!==u&&!r.policies.hasKeyArgs(s.__typename,c)&&(l[c]=1),s[u]===void 0&&!(r instanceof ar)&&delete s[u]}}),l.__typename&&!(o&&o.__typename)&&this.policies.rootTypenamesById[i]===s.__typename&&delete l.__typename,Object.keys(l).forEach(function(u){return r.group.dirty(i,u)})}}},e.prototype.modify=function(t,n){var r=this,i=this.lookup(t);if(i){var o=Object.create(null),a=!1,s=!0,l={DELETE:qs,INVALIDATE:Ym,isReference:_e,toReference:this.toReference,canRead:this.canRead,readField:function(u,c){return r.policies.readField(typeof u=="string"?{fieldName:u,from:c||Wi(t)}:u,{store:r})}};if(Object.keys(i).forEach(function(u){var c=Ir(u),f=i[u];if(f!==void 0){var d=typeof n=="function"?n:n[u]||n[c];if(d){var y=d===ec?qs:d(Da(f),b(b({},l),{fieldName:c,storeFieldName:u,storage:r.getStorage(t,u)}));if(y===Ym)r.group.dirty(t,u);else if(y===qs&&(y=void 0),y!==f&&(o[u]=y,a=!0,f=y,globalThis.__DEV__!==!1)){var g=function(T){if(r.lookup(T.__ref)===void 0)return globalThis.__DEV__!==!1&&X.warn(2,T),!0};if(_e(y))g(y);else if(Array.isArray(y))for(var v=!1,E=void 0,h=0,m=y;h<m.length;h++){var p=m[h];if(_e(p)){if(v=!0,g(p))break}else if(typeof p=="object"&&p){var S=r.policies.identify(p)[0];S&&(E=p)}if(v&&E!==void 0){globalThis.__DEV__!==!1&&X.warn(3,E);break}}}}f!==void 0&&(s=!1)}}),a)return this.merge(t,o),s&&(this instanceof ar?this.data[t]=void 0:delete this.data[t],this.group.dirty(t,"__exists")),!0}return!1},e.prototype.delete=function(t,n,r){var i,o=this.lookup(t);if(o){var a=this.getFieldValue(o,"__typename"),s=n&&r?this.policies.getStoreFieldName({typename:a,fieldName:n,args:r}):n;return this.modify(t,s?(i={},i[s]=ec,i):ec)}return!1},e.prototype.evict=function(t,n){var r=!1;return t.id&&(it.call(this.data,t.id)&&(r=this.delete(t.id,t.fieldName,t.args)),this instanceof ar&&this!==n&&(r=this.parent.evict(t,n)||r),(t.fieldName||r)&&this.group.dirty(t.id,t.fieldName||"__exists")),r},e.prototype.clear=function(){this.replace(null)},e.prototype.extract=function(){var t=this,n=this.toObject(),r=[];return this.getRootIdSet().forEach(function(i){it.call(t.policies.rootTypenamesById,i)||r.push(i)}),r.length&&(n.__META={extraRootIds:r.sort()}),n},e.prototype.replace=function(t){var n=this;if(Object.keys(this.data).forEach(function(o){t&&it.call(t,o)||n.delete(o)}),t){var r=t.__META,i=kn(t,["__META"]);Object.keys(i).forEach(function(o){n.merge(o,i[o])}),r&&r.extraRootIds.forEach(this.retain,this)}},e.prototype.retain=function(t){return this.rootIds[t]=(this.rootIds[t]||0)+1},e.prototype.release=function(t){if(this.rootIds[t]>0){var n=--this.rootIds[t];return n||delete this.rootIds[t],n}return 0},e.prototype.getRootIdSet=function(t){return t===void 0&&(t=new Set),Object.keys(this.rootIds).forEach(t.add,t),this instanceof ar?this.parent.getRootIdSet(t):Object.keys(this.policies.rootTypenamesById).forEach(t.add,t),t},e.prototype.gc=function(){var t=this,n=this.getRootIdSet(),r=this.toObject();n.forEach(function(a){it.call(r,a)&&(Object.keys(t.findChildRefIds(a)).forEach(n.add,n),delete r[a])});var i=Object.keys(r);if(i.length){for(var o=this;o instanceof ar;)o=o.parent;i.forEach(function(a){return o.delete(a)})}return i},e.prototype.findChildRefIds=function(t){if(!it.call(this.refs,t)){var n=this.refs[t]=Object.create(null),r=this.data[t];if(!r)return n;var i=new Set([r]);i.forEach(function(o){_e(o)&&(n[o.__ref]=!0),Qe(o)&&Object.keys(o).forEach(function(a){var s=o[a];Qe(s)&&i.add(s)})})}return this.refs[t]},e.prototype.makeCacheKey=function(){return this.group.keyMaker.lookupArray(arguments)},e}(),g1=function(){function e(t,n){n===void 0&&(n=null),this.caching=t,this.parent=n,this.d=null,this.resetCaching()}return e.prototype.resetCaching=function(){this.d=this.caching?J0():null,this.keyMaker=new po(pi)},e.prototype.depend=function(t,n){if(this.d){this.d(tc(t,n));var r=Ir(n);r!==n&&this.d(tc(t,r)),this.parent&&this.parent.depend(t,n)}},e.prototype.dirty=function(t,n){this.d&&this.d.dirty(tc(t,n),n==="__exists"?"forget":"setDirty")},e}();function tc(e,t){return t+"#"+e}function Xm(e,t){ra(e)&&e.group.depend(t,"__exists")}(function(e){var t=function(n){Nn(r,n);function r(i){var o=i.policies,a=i.resultCaching,s=a===void 0?!0:a,l=i.seed,u=n.call(this,o,new g1(s))||this;return u.stump=new Dk(u),u.storageTrie=new po(pi),l&&u.replace(l),u}return r.prototype.addLayer=function(i,o){return this.stump.addLayer(i,o)},r.prototype.removeLayer=function(){return this},r.prototype.getStorage=function(){return this.storageTrie.lookupArray(arguments)},r}(e);e.Root=t})(Ia||(Ia={}));var ar=function(e){Nn(t,e);function t(n,r,i,o){var a=e.call(this,r.policies,o)||this;return a.id=n,a.parent=r,a.replay=i,a.group=o,i(a),a}return t.prototype.addLayer=function(n,r){return new t(n,this,r,this.group)},t.prototype.removeLayer=function(n){var r=this,i=this.parent.removeLayer(n);return n===this.id?(this.group.caching&&Object.keys(this.data).forEach(function(o){var a=r.data[o],s=i.lookup(o);s?a?a!==s&&Object.keys(a).forEach(function(l){we(a[l],s[l])||r.group.dirty(o,l)}):(r.group.dirty(o,"__exists"),Object.keys(s).forEach(function(l){r.group.dirty(o,l)})):r.delete(o)}),i):i===this.parent?this:i.addLayer(this.id,this.replay)},t.prototype.toObject=function(){return b(b({},this.parent.toObject()),this.data)},t.prototype.findChildRefIds=function(n){var r=this.parent.findChildRefIds(n);return it.call(this.data,n)?b(b({},r),e.prototype.findChildRefIds.call(this,n)):r},t.prototype.getStorage=function(){for(var n=this.parent;n.parent;)n=n.parent;return n.getStorage.apply(n,arguments)},t}(Ia),Dk=function(e){Nn(t,e);function t(n){return e.call(this,"EntityStore.Stump",n,function(){},new g1(n.group.caching,n.group))||this}return t.prototype.removeLayer=function(){return this},t.prototype.merge=function(n,r){return this.parent.merge(n,r)},t}(ar);function Ik(e,t,n){var r=e[n],i=t[n];return we(r,i)?r:i}function ra(e){return!!(e instanceof Ia&&e.group.caching)}function Pk(e){return Qe(e)?$e(e)?e.slice(0):b({__proto__:Object.getPrototypeOf(e)},e):e}var Km=function(){function e(){this.known=new(A0?WeakSet:Set),this.pool=new po(pi),this.passes=new WeakMap,this.keysByJSON=new Map,this.empty=this.admit({})}return e.prototype.isKnown=function(t){return Qe(t)&&this.known.has(t)},e.prototype.pass=function(t){if(Qe(t)){var n=Pk(t);return this.passes.set(n,t),n}return t},e.prototype.admit=function(t){var n=this;if(Qe(t)){var r=this.passes.get(t);if(r)return r;var i=Object.getPrototypeOf(t);switch(i){case Array.prototype:{if(this.known.has(t))return t;var o=t.map(this.admit,this),a=this.pool.lookupArray(o);return a.array||(this.known.add(a.array=o),globalThis.__DEV__!==!1&&Object.freeze(o)),a.array}case null:case Object.prototype:{if(this.known.has(t))return t;var s=Object.getPrototypeOf(t),l=[s],u=this.sortedKeys(t);l.push(u.json);var c=l.length;u.sorted.forEach(function(y){l.push(n.admit(t[y]))});var a=this.pool.lookupArray(l);if(!a.object){var f=a.object=Object.create(s);this.known.add(f),u.sorted.forEach(function(y,g){f[y]=l[c+g]}),globalThis.__DEV__!==!1&&Object.freeze(f)}return a.object}}}return t},e.prototype.sortedKeys=function(t){var n=Object.keys(t),r=this.pool.lookupArray(n);if(!r.keys){n.sort();var i=JSON.stringify(n);(r.keys=this.keysByJSON.get(i))||this.keysByJSON.set(i,r.keys={sorted:n,json:i})}return r.keys},e}();function Jm(e){return[e.selectionSet,e.objectOrReference,e.context,e.context.canonizeResults]}var Ak=function(){function e(t){var n=this;this.knownResults=new(pi?WeakMap:Map),this.config=ci(t,{addTypename:t.addTypename!==!1,canonizeResults:m1(t)}),this.canon=t.canon||new Km,this.executeSelectionSet=Ra(function(r){var i,o=r.context.canonizeResults,a=Jm(r);a[3]=!o;var s=(i=n.executeSelectionSet).peek.apply(i,a);return s?o?b(b({},s),{result:n.canon.admit(s.result)}):s:(Xm(r.context.store,r.enclosingRef.__ref),n.execSelectionSetImpl(r))},{max:this.config.resultCacheMaxSize||Cn["inMemoryCache.executeSelectionSet"]||5e4,keyArgs:Jm,makeCacheKey:function(r,i,o,a){if(ra(o.store))return o.store.makeCacheKey(r,_e(i)?i.__ref:i,o.varString,a)}}),this.executeSubSelectedArray=Ra(function(r){return Xm(r.context.store,r.enclosingRef.__ref),n.execSubSelectedArrayImpl(r)},{max:this.config.resultCacheMaxSize||Cn["inMemoryCache.executeSubSelectedArray"]||1e4,makeCacheKey:function(r){var i=r.field,o=r.array,a=r.context;if(ra(a.store))return a.store.makeCacheKey(i,o,a.varString)}})}return e.prototype.resetCanon=function(){this.canon=new Km},e.prototype.diffQueryAgainstStore=function(t){var n=t.store,r=t.query,i=t.rootId,o=i===void 0?"ROOT_QUERY":i,a=t.variables,s=t.returnPartialData,l=s===void 0?!0:s,u=t.canonizeResults,c=u===void 0?this.config.canonizeResults:u,f=this.config.cache.policies;a=b(b({},sh(U0(r))),a);var d=Wi(o),y=this.executeSelectionSet({selectionSet:Ha(r).selectionSet,objectOrReference:d,enclosingRef:d,context:b({store:n,query:r,policies:f,variables:a,varString:br(a),canonizeResults:c},y1(r,this.config.fragments))}),g;if(y.missing&&(g=[new d1(Lk(y.missing),y.missing,r,a)],!l))throw g[0];return{result:y.result,complete:!g,missing:g}},e.prototype.isFresh=function(t,n,r,i){if(ra(i.store)&&this.knownResults.get(t)===r){var o=this.executeSelectionSet.peek(r,n,i,this.canon.isKnown(t));if(o&&t===o.result)return!0}return!1},e.prototype.execSelectionSetImpl=function(t){var n=this,r=t.selectionSet,i=t.objectOrReference,o=t.enclosingRef,a=t.context;if(_e(i)&&!a.policies.rootTypenamesById[i.__ref]&&!a.store.has(i.__ref))return{result:this.canon.empty,missing:"Dangling reference to missing ".concat(i.__ref," object")};var s=a.variables,l=a.policies,u=a.store,c=u.getFieldValue(i,"__typename"),f=[],d,y=new Dr;this.config.addTypename&&typeof c=="string"&&!l.rootIdsByTypename[c]&&f.push({__typename:c});function g(p,S){var T;return p.missing&&(d=y.merge(d,(T={},T[S]=p.missing,T))),p.result}var v=new Set(r.selections);v.forEach(function(p){var S,T;if(Qa(p,s))if(Nr(p)){var O=l.readField({fieldName:p.name.value,field:p,variables:a.variables,from:i},a),w=Or(p);O===void 0?dh.added(p)||(d=y.merge(d,(S={},S[w]="Can't find field '".concat(p.name.value,"' on ").concat(_e(i)?i.__ref+" object":"object "+JSON.stringify(i,null,2)),S))):$e(O)?O.length>0&&(O=g(n.executeSubSelectedArray({field:p,array:O,enclosingRef:o,context:a}),w)):p.selectionSet?O!=null&&(O=g(n.executeSelectionSet({selectionSet:p.selectionSet,objectOrReference:O,enclosingRef:_e(O)?O:o,context:a}),w)):a.canonizeResults&&(O=n.canon.pass(O)),O!==void 0&&f.push((T={},T[w]=O,T))}else{var k=Kl(p,a.lookupFragment);if(!k&&p.kind===G.FRAGMENT_SPREAD)throw _t(9,p.name.value);k&&l.fragmentMatches(k,c)&&k.selectionSet.selections.forEach(v.add,v)}});var E=ru(f),h={result:E,missing:d},m=a.canonizeResults?this.canon.admit(h):Da(h);return m.result&&this.knownResults.set(m.result,r),m},e.prototype.execSubSelectedArrayImpl=function(t){var n=this,r=t.field,i=t.array,o=t.enclosingRef,a=t.context,s,l=new Dr;function u(c,f){var d;return c.missing&&(s=l.merge(s,(d={},d[f]=c.missing,d))),c.result}return r.selectionSet&&(i=i.filter(a.store.canRead)),i=i.map(function(c,f){return c===null?null:$e(c)?u(n.executeSubSelectedArray({field:r,array:c,enclosingRef:o,context:a}),f):r.selectionSet?u(n.executeSelectionSet({selectionSet:r.selectionSet,objectOrReference:c,enclosingRef:_e(c)?c:o,context:a}),f):(globalThis.__DEV__!==!1&&Mk(a.store,r,c),c)}),{result:a.canonizeResults?this.canon.admit(i):i,missing:s}},e}();function Lk(e){try{JSON.stringify(e,function(t,n){if(typeof n=="string")throw n;return n})}catch(t){return t}}function Mk(e,t,n){if(!t.selectionSet){var r=new Set([n]);r.forEach(function(i){Qe(i)&&(X(!_e(i),10,Nk(e,i),t.name.value),Object.values(i).forEach(r.add,r))})}}var gh=new V0,Zm=new WeakMap;function ia(e){var t=Zm.get(e);return t||Zm.set(e,t={vars:new Set,dep:J0()}),t}function ev(e){ia(e).vars.forEach(function(t){return t.forgetCache(e)})}function Fk(e){ia(e).vars.forEach(function(t){return t.attachCache(e)})}function jk(e){var t=new Set,n=new Set,r=function(o){if(arguments.length>0){if(e!==o){e=o,t.forEach(function(l){ia(l).dep.dirty(r),zk(l)});var a=Array.from(n);n.clear(),a.forEach(function(l){return l(e)})}}else{var s=gh.getValue();s&&(i(s),ia(s).dep(r))}return e};r.onNextChange=function(o){return n.add(o),function(){n.delete(o)}};var i=r.attachCache=function(o){return t.add(o),ia(o).vars.add(r),r};return r.forgetCache=function(o){return t.delete(o)},r}function zk(e){e.broadcastWatches&&e.broadcastWatches()}var tv=Object.create(null);function Eh(e){var t=JSON.stringify(e);return tv[t]||(tv[t]=Object.create(null))}function nv(e){var t=Eh(e);return t.keyFieldsFn||(t.keyFieldsFn=function(n,r){var i=function(a,s){return r.readField(s,a)},o=r.keyObject=wh(e,function(a){var s=Yi(r.storeObject,a,i);return s===void 0&&n!==r.storeObject&&it.call(n,a[0])&&(s=Yi(n,a,w1)),X(s!==void 0,4,a.join("."),n),s});return"".concat(r.typename,":").concat(JSON.stringify(o))})}function rv(e){var t=Eh(e);return t.keyArgsFn||(t.keyArgsFn=function(n,r){var i=r.field,o=r.variables,a=r.fieldName,s=wh(e,function(u){var c=u[0],f=c.charAt(0);if(f==="@"){if(i&&en(i.directives)){var d=c.slice(1),y=i.directives.find(function(h){return h.name.value===d}),g=y&&Jl(y,o);return g&&Yi(g,u.slice(1))}return}if(f==="$"){var v=c.slice(1);if(o&&it.call(o,v)){var E=u.slice(0);return E[0]=v,Yi(o,E)}return}if(n)return Yi(n,u)}),l=JSON.stringify(s);return(n||l!=="{}")&&(a+=":"+l),a})}function wh(e,t){var n=new Dr;return E1(e).reduce(function(r,i){var o,a=t(i);if(a!==void 0){for(var s=i.length-1;s>=0;--s)a=(o={},o[i[s]]=a,o);r=n.merge(r,a)}return r},Object.create(null))}function E1(e){var t=Eh(e);if(!t.paths){var n=t.paths=[],r=[];e.forEach(function(i,o){$e(i)?(E1(i).forEach(function(a){return n.push(r.concat(a))}),r.length=0):(r.push(i),$e(e[o+1])||(n.push(r.slice(0)),r.length=0))})}return t.paths}function w1(e,t){return e[t]}function Yi(e,t,n){return n=n||w1,S1(t.reduce(function r(i,o){return $e(i)?i.map(function(a){return r(a,o)}):i&&n(i,o)},e))}function S1(e){return Qe(e)?$e(e)?e.map(S1):wh(Object.keys(e).sort(),function(t){return Yi(e,t)}):e}function Af(e){return e.args!==void 0?e.args:e.field?Jl(e.field,e.variables):null}var Uk=function(){},iv=function(e,t){return t.fieldName},ov=function(e,t,n){var r=n.mergeObjects;return r(e,t)},av=function(e,t){return t},Vk=function(){function e(t){this.config=t,this.typePolicies=Object.create(null),this.toBeAdded=Object.create(null),this.supertypeMap=new Map,this.fuzzySubtypes=new Map,this.rootIdsByTypename=Object.create(null),this.rootTypenamesById=Object.create(null),this.usingPossibleTypes=!1,this.config=b({dataIdFromObject:h1},t),this.cache=this.config.cache,this.setRootTypename("Query"),this.setRootTypename("Mutation"),this.setRootTypename("Subscription"),t.possibleTypes&&this.addPossibleTypes(t.possibleTypes),t.typePolicies&&this.addTypePolicies(t.typePolicies)}return e.prototype.identify=function(t,n){var r,i=this,o=n&&(n.typename||((r=n.storeObject)===null||r===void 0?void 0:r.__typename))||t.__typename;if(o===this.rootTypenamesById.ROOT_QUERY)return["ROOT_QUERY"];for(var a=n&&n.storeObject||t,s=b(b({},n),{typename:o,storeObject:a,readField:n&&n.readField||function(){var d=Sh(arguments,a);return i.readField(d,{store:i.cache.data,variables:d.variables})}}),l,u=o&&this.getTypePolicy(o),c=u&&u.keyFn||this.config.dataIdFromObject;c;){var f=c(b(b({},t),a),s);if($e(f))c=nv(f);else{l=f;break}}return l=l?String(l):void 0,s.keyObject?[l,s.keyObject]:[l]},e.prototype.addTypePolicies=function(t){var n=this;Object.keys(t).forEach(function(r){var i=t[r],o=i.queryType,a=i.mutationType,s=i.subscriptionType,l=kn(i,["queryType","mutationType","subscriptionType"]);o&&n.setRootTypename("Query",r),a&&n.setRootTypename("Mutation",r),s&&n.setRootTypename("Subscription",r),it.call(n.toBeAdded,r)?n.toBeAdded[r].push(l):n.toBeAdded[r]=[l]})},e.prototype.updateTypePolicy=function(t,n){var r=this,i=this.getTypePolicy(t),o=n.keyFields,a=n.fields;function s(l,u){l.merge=typeof u=="function"?u:u===!0?ov:u===!1?av:l.merge}s(i,n.merge),i.keyFn=o===!1?Uk:$e(o)?nv(o):typeof o=="function"?o:i.keyFn,a&&Object.keys(a).forEach(function(l){var u=r.getFieldPolicy(t,l,!0),c=a[l];if(typeof c=="function")u.read=c;else{var f=c.keyArgs,d=c.read,y=c.merge;u.keyFn=f===!1?iv:$e(f)?rv(f):typeof f=="function"?f:u.keyFn,typeof d=="function"&&(u.read=d),s(u,y)}u.read&&u.merge&&(u.keyFn=u.keyFn||iv)})},e.prototype.setRootTypename=function(t,n){n===void 0&&(n=t);var r="ROOT_"+t.toUpperCase(),i=this.rootTypenamesById[r];n!==i&&(X(!i||i===t,5,t),i&&delete this.rootIdsByTypename[i],this.rootIdsByTypename[n]=r,this.rootTypenamesById[r]=n)},e.prototype.addPossibleTypes=function(t){var n=this;this.usingPossibleTypes=!0,Object.keys(t).forEach(function(r){n.getSupertypeSet(r,!0),t[r].forEach(function(i){n.getSupertypeSet(i,!0).add(r);var o=i.match(v1);(!o||o[0]!==i)&&n.fuzzySubtypes.set(i,new RegExp(i))})})},e.prototype.getTypePolicy=function(t){var n=this;if(!it.call(this.typePolicies,t)){var r=this.typePolicies[t]=Object.create(null);r.fields=Object.create(null);var i=this.supertypeMap.get(t);!i&&this.fuzzySubtypes.size&&(i=this.getSupertypeSet(t,!0),this.fuzzySubtypes.forEach(function(a,s){if(a.test(t)){var l=n.supertypeMap.get(s);l&&l.forEach(function(u){return i.add(u)})}})),i&&i.size&&i.forEach(function(a){var s=n.getTypePolicy(a),l=s.fields,u=kn(s,["fields"]);Object.assign(r,u),Object.assign(r.fields,l)})}var o=this.toBeAdded[t];return o&&o.length&&o.splice(0).forEach(function(a){n.updateTypePolicy(t,a)}),this.typePolicies[t]},e.prototype.getFieldPolicy=function(t,n,r){if(t){var i=this.getTypePolicy(t).fields;return i[n]||r&&(i[n]=Object.create(null))}},e.prototype.getSupertypeSet=function(t,n){var r=this.supertypeMap.get(t);return!r&&n&&this.supertypeMap.set(t,r=new Set),r},e.prototype.fragmentMatches=function(t,n,r,i){var o=this;if(!t.typeCondition)return!0;if(!n)return!1;var a=t.typeCondition.name.value;if(n===a)return!0;if(this.usingPossibleTypes&&this.supertypeMap.has(a))for(var s=this.getSupertypeSet(n,!0),l=[s],u=function(g){var v=o.getSupertypeSet(g,!1);v&&v.size&&l.indexOf(v)<0&&l.push(v)},c=!!(r&&this.fuzzySubtypes.size),f=!1,d=0;d<l.length;++d){var y=l[d];if(y.has(a))return s.has(a)||(f&&globalThis.__DEV__!==!1&&X.warn(6,n,a),s.add(a)),!0;y.forEach(u),c&&d===l.length-1&&Pf(t.selectionSet,r,i)&&(c=!1,f=!0,this.fuzzySubtypes.forEach(function(g,v){var E=n.match(g);E&&E[0]===n&&u(v)}))}return!1},e.prototype.hasKeyArgs=function(t,n){var r=this.getFieldPolicy(t,n,!1);return!!(r&&r.keyFn)},e.prototype.getStoreFieldName=function(t){var n=t.typename,r=t.fieldName,i=this.getFieldPolicy(n,r,!1),o,a=i&&i.keyFn;if(a&&n)for(var s={typename:n,fieldName:r,field:t.field||null,variables:t.variables},l=Af(t);a;){var u=a(l,s);if($e(u))a=rv(u);else{o=u||r;break}}return o===void 0&&(o=t.field?ab(t.field,t.variables):z0(r,Af(t))),o===!1?r:r===Ir(o)?o:r+":"+o},e.prototype.readField=function(t,n){var r=t.from;if(r){var i=t.field||t.fieldName;if(i){if(t.typename===void 0){var o=n.store.getFieldValue(r,"__typename");o&&(t.typename=o)}var a=this.getStoreFieldName(t),s=Ir(a),l=n.store.getFieldValue(r,a),u=this.getFieldPolicy(t.typename,s,!1),c=u&&u.read;if(c){var f=sv(this,r,t,n,n.store.getStorage(_e(r)?r.__ref:r,a));return gh.withValue(this.cache,c,[l,f])}return l}}},e.prototype.getReadFunction=function(t,n){var r=this.getFieldPolicy(t,n,!1);return r&&r.read},e.prototype.getMergeFunction=function(t,n,r){var i=this.getFieldPolicy(t,n,!1),o=i&&i.merge;return!o&&r&&(i=this.getTypePolicy(r),o=i&&i.merge),o},e.prototype.runMergeFunction=function(t,n,r,i,o){var a=r.field,s=r.typename,l=r.merge;return l===ov?_1(i.store)(t,n):l===av?n:(i.overwrite&&(t=void 0),l(t,n,sv(this,void 0,{typename:s,fieldName:a.name.value,field:a,variables:i.variables},i,o||Object.create(null))))},e}();function sv(e,t,n,r,i){var o=e.getStoreFieldName(n),a=Ir(o),s=n.variables||r.variables,l=r.store,u=l.toReference,c=l.canRead;return{args:Af(n),field:n.field||null,fieldName:a,storeFieldName:o,variables:s,isReference:_e,toReference:u,storage:i,cache:e.cache,canRead:c,readField:function(){return e.readField(Sh(arguments,t,s),r)},mergeObjects:_1(r.store)}}function Sh(e,t,n){var r=e[0],i=e[1],o=e.length,a;return typeof r=="string"?a={fieldName:r,from:o>1?i:t}:(a=b({},r),it.call(a,"from")||(a.from=t)),globalThis.__DEV__!==!1&&a.from===void 0&&globalThis.__DEV__!==!1&&X.warn(7,S0(Array.from(e))),a.variables===void 0&&(a.variables=n),a}function _1(e){return function(n,r){if($e(n)||$e(r))throw _t(8);if(Qe(n)&&Qe(r)){var i=e.getFieldValue(n,"__typename"),o=e.getFieldValue(r,"__typename"),a=i&&o&&i!==o;if(a)return r;if(_e(n)&&zi(r))return e.merge(n.__ref,r),n;if(zi(n)&&_e(r))return e.merge(n,r.__ref),r;if(zi(n)&&zi(r))return b(b({},n),r)}return r}}function nc(e,t,n){var r="".concat(t).concat(n),i=e.flavors.get(r);return i||e.flavors.set(r,i=e.clientOnly===t&&e.deferred===n?e:b(b({},e),{clientOnly:t,deferred:n})),i}var Bk=function(){function e(t,n,r){this.cache=t,this.reader=n,this.fragments=r}return e.prototype.writeToStore=function(t,n){var r=this,i=n.query,o=n.result,a=n.dataId,s=n.variables,l=n.overwrite,u=qa(i),c=Rk();s=b(b({},sh(u)),s);var f=b(b({store:t,written:Object.create(null),merge:function(y,g){return c.merge(y,g)},variables:s,varString:br(s)},y1(i,this.fragments)),{overwrite:!!l,incomingById:new Map,clientOnly:!1,deferred:!1,flavors:new Map}),d=this.processSelectionSet({result:o||Object.create(null),dataId:a,selectionSet:u.selectionSet,mergeTree:{map:new Map},context:f});if(!_e(d))throw _t(11,o);return f.incomingById.forEach(function(y,g){var v=y.storeObject,E=y.mergeTree,h=y.fieldNodeSet,m=Wi(g);if(E&&E.map.size){var p=r.applyMerges(E,m,v,f);if(_e(p))return;v=p}if(globalThis.__DEV__!==!1&&!f.overwrite){var S=Object.create(null);h.forEach(function(w){w.selectionSet&&(S[w.name.value]=!0)});var T=function(w){return S[Ir(w)]===!0},O=function(w){var k=E&&E.map.get(w);return!!(k&&k.info&&k.info.merge)};Object.keys(v).forEach(function(w){T(w)&&!O(w)&&Qk(m,v,w,f.store)})}t.merge(g,v)}),t.retain(d.__ref),d},e.prototype.processSelectionSet=function(t){var n=this,r=t.dataId,i=t.result,o=t.selectionSet,a=t.context,s=t.mergeTree,l=this.cache.policies,u=Object.create(null),c=r&&l.rootTypenamesById[r]||Tf(i,o,a.fragmentMap)||r&&a.store.get(r,"__typename");typeof c=="string"&&(u.__typename=c);var f=function(){var p=Sh(arguments,u,a.variables);if(_e(p.from)){var S=a.incomingById.get(p.from.__ref);if(S){var T=l.readField(b(b({},p),{from:S.storeObject}),a);if(T!==void 0)return T}}return l.readField(p,a)},d=new Set;this.flattenFields(o,i,a,c).forEach(function(p,S){var T,O=Or(S),w=i[O];if(d.add(S),w!==void 0){var k=l.getStoreFieldName({typename:c,fieldName:S.name.value,field:S,variables:p.variables}),I=lv(s,k),P=n.processFieldValue(w,S,S.selectionSet?nc(p,!1,!1):p,I),K=void 0;S.selectionSet&&(_e(P)||zi(P))&&(K=f("__typename",P));var se=l.getMergeFunction(c,S.name.value,K);se?I.info={field:S,typename:c,merge:se}:uv(s,k),u=p.merge(u,(T={},T[k]=P,T))}else globalThis.__DEV__!==!1&&!p.clientOnly&&!p.deferred&&!dh.added(S)&&!l.getReadFunction(c,S.name.value)&&globalThis.__DEV__!==!1&&X.error(12,Or(S),i)});try{var y=l.identify(i,{typename:c,selectionSet:o,fragmentMap:a.fragmentMap,storeObject:u,readField:f}),g=y[0],v=y[1];r=r||g,v&&(u=a.merge(u,v))}catch(p){if(!r)throw p}if(typeof r=="string"){var E=Wi(r),h=a.written[r]||(a.written[r]=[]);if(h.indexOf(o)>=0||(h.push(o),this.reader&&this.reader.isFresh(i,E,o,a)))return E;var m=a.incomingById.get(r);return m?(m.storeObject=a.merge(m.storeObject,u),m.mergeTree=Lf(m.mergeTree,s),d.forEach(function(p){return m.fieldNodeSet.add(p)})):a.incomingById.set(r,{storeObject:u,mergeTree:xl(s)?void 0:s,fieldNodeSet:d}),E}return u},e.prototype.processFieldValue=function(t,n,r,i){var o=this;return!n.selectionSet||t===null?globalThis.__DEV__!==!1?a1(t):t:$e(t)?t.map(function(a,s){var l=o.processFieldValue(a,n,r,lv(i,s));return uv(i,s),l}):this.processSelectionSet({result:t,selectionSet:n.selectionSet,context:r,mergeTree:i})},e.prototype.flattenFields=function(t,n,r,i){i===void 0&&(i=Tf(n,t,r.fragmentMap));var o=new Map,a=this.cache.policies,s=new po(!1);return function l(u,c){var f=s.lookup(u,c.clientOnly,c.deferred);f.visited||(f.visited=!0,u.selections.forEach(function(d){if(Qa(d,r.variables)){var y=c.clientOnly,g=c.deferred;if(!(y&&g)&&en(d.directives)&&d.directives.forEach(function(h){var m=h.name.value;if(m==="client"&&(y=!0),m==="defer"){var p=Jl(h,r.variables);(!p||p.if!==!1)&&(g=!0)}}),Nr(d)){var v=o.get(d);v&&(y=y&&v.clientOnly,g=g&&v.deferred),o.set(d,nc(r,y,g))}else{var E=Kl(d,r.lookupFragment);if(!E&&d.kind===G.FRAGMENT_SPREAD)throw _t(13,d.name.value);E&&a.fragmentMatches(E,i,n,r.variables)&&l(E.selectionSet,nc(r,y,g))}}}))}(t,r),o},e.prototype.applyMerges=function(t,n,r,i,o){var a,s=this;if(t.map.size&&!_e(r)){var l=!$e(r)&&(_e(n)||zi(n))?n:void 0,u=r;l&&!o&&(o=[_e(l)?l.__ref:l]);var c,f=function(d,y){return $e(d)?typeof y=="number"?d[y]:void 0:i.store.getFieldValue(d,String(y))};t.map.forEach(function(d,y){var g=f(l,y),v=f(u,y);if(v!==void 0){o&&o.push(y);var E=s.applyMerges(d,g,v,i,o);E!==v&&(c=c||new Map,c.set(y,E)),o&&X(o.pop()===y)}}),c&&(r=$e(u)?u.slice(0):b({},u),c.forEach(function(d,y){r[y]=d}))}return t.info?this.cache.policies.runMergeFunction(n,r,t.info,i,o&&(a=i.store).getStorage.apply(a,o)):r},e}(),T1=[];function lv(e,t){var n=e.map;return n.has(t)||n.set(t,T1.pop()||{map:new Map}),n.get(t)}function Lf(e,t){if(e===t||!t||xl(t))return e;if(!e||xl(e))return t;var n=e.info&&t.info?b(b({},e.info),t.info):e.info||t.info,r=e.map.size&&t.map.size,i=r?new Map:e.map.size?e.map:t.map,o={info:n,map:i};if(r){var a=new Set(t.map.keys());e.map.forEach(function(s,l){o.map.set(l,Lf(s,t.map.get(l))),a.delete(l)}),a.forEach(function(s){o.map.set(s,Lf(t.map.get(s),e.map.get(s)))})}return o}function xl(e){return!e||!(e.info||e.map.size)}function uv(e,t){var n=e.map,r=n.get(t);r&&xl(r)&&(T1.push(r),n.delete(t))}var cv=new Set;function Qk(e,t,n,r){var i=function(f){var d=r.getFieldValue(f,n);return typeof d=="object"&&d},o=i(e);if(o){var a=i(t);if(a&&!_e(o)&&!we(o,a)&&!Object.keys(o).every(function(f){return r.getFieldValue(a,f)!==void 0})){var s=r.getFieldValue(e,"__typename")||r.getFieldValue(t,"__typename"),l=Ir(n),u="".concat(s,".").concat(l);if(!cv.has(u)){cv.add(u);var c=[];!$e(o)&&!$e(a)&&[o,a].forEach(function(f){var d=r.getFieldValue(f,"__typename");typeof d=="string"&&!c.includes(d)&&c.push(d)}),globalThis.__DEV__!==!1&&X.warn(14,l,s,c.length?"either ensure all objects of type "+c.join(" and ")+" have an ID or a custom merge function, or ":"",u,o,a)}}}}var x1=function(e){Nn(t,e);function t(n){n===void 0&&(n={});var r=e.call(this)||this;return r.watches=new Set,r.addTypenameTransform=new Z0(dh),r.assumeImmutableResults=!0,r.makeVar=jk,r.txCount=0,r.config=Ok(n),r.addTypename=!!r.config.addTypename,r.policies=new Vk({cache:r,dataIdFromObject:r.config.dataIdFromObject,possibleTypes:r.config.possibleTypes,typePolicies:r.config.typePolicies}),r.init(),r}return t.prototype.init=function(){var n=this.data=new Ia.Root({policies:this.policies,resultCaching:this.config.resultCaching});this.optimisticData=n.stump,this.resetResultCache()},t.prototype.resetResultCache=function(n){var r=this,i=this.storeReader,o=this.config.fragments;this.storeWriter=new Bk(this,this.storeReader=new Ak({cache:this,addTypename:this.addTypename,resultCacheMaxSize:this.config.resultCacheMaxSize,canonizeResults:m1(this.config),canon:n?void 0:i&&i.canon,fragments:o}),o),this.maybeBroadcastWatch=Ra(function(a,s){return r.broadcastWatch(a,s)},{max:this.config.resultCacheMaxSize||Cn["inMemoryCache.maybeBroadcastWatch"]||5e3,makeCacheKey:function(a){var s=a.optimistic?r.optimisticData:r.data;if(ra(s)){var l=a.optimistic,u=a.id,c=a.variables;return s.makeCacheKey(a.query,a.callback,br({optimistic:l,id:u,variables:c}))}}}),new Set([this.data.group,this.optimisticData.group]).forEach(function(a){return a.resetCaching()})},t.prototype.restore=function(n){return this.init(),n&&this.data.replace(n),this},t.prototype.extract=function(n){return n===void 0&&(n=!1),(n?this.optimisticData:this.data).extract()},t.prototype.read=function(n){var r=n.returnPartialData,i=r===void 0?!1:r;try{return this.storeReader.diffQueryAgainstStore(b(b({},n),{store:n.optimistic?this.optimisticData:this.data,config:this.config,returnPartialData:i})).result||null}catch(o){if(o instanceof d1)return null;throw o}},t.prototype.write=function(n){try{return++this.txCount,this.storeWriter.writeToStore(this.data,n)}finally{!--this.txCount&&n.broadcast!==!1&&this.broadcastWatches()}},t.prototype.modify=function(n){if(it.call(n,"id")&&!n.id)return!1;var r=n.optimistic?this.optimisticData:this.data;try{return++this.txCount,r.modify(n.id||"ROOT_QUERY",n.fields)}finally{!--this.txCount&&n.broadcast!==!1&&this.broadcastWatches()}},t.prototype.diff=function(n){return this.storeReader.diffQueryAgainstStore(b(b({},n),{store:n.optimistic?this.optimisticData:this.data,rootId:n.id||"ROOT_QUERY",config:this.config}))},t.prototype.watch=function(n){var r=this;return this.watches.size||Fk(this),this.watches.add(n),n.immediate&&this.maybeBroadcastWatch(n),function(){r.watches.delete(n)&&!r.watches.size&&ev(r),r.maybeBroadcastWatch.forget(n)}},t.prototype.gc=function(n){var r;br.reset(),nu.reset(),this.addTypenameTransform.resetCache(),(r=this.config.fragments)===null||r===void 0||r.resetCaches();var i=this.optimisticData.gc();return n&&!this.txCount&&(n.resetResultCache?this.resetResultCache(n.resetResultIdentities):n.resetResultIdentities&&this.storeReader.resetCanon()),i},t.prototype.retain=function(n,r){return(r?this.optimisticData:this.data).retain(n)},t.prototype.release=function(n,r){return(r?this.optimisticData:this.data).release(n)},t.prototype.identify=function(n){if(_e(n))return n.__ref;try{return this.policies.identify(n)[0]}catch(r){globalThis.__DEV__!==!1&&X.warn(r)}},t.prototype.evict=function(n){if(!n.id){if(it.call(n,"id"))return!1;n=b(b({},n),{id:"ROOT_QUERY"})}try{return++this.txCount,this.optimisticData.evict(n,this.data)}finally{!--this.txCount&&n.broadcast!==!1&&this.broadcastWatches()}},t.prototype.reset=function(n){var r=this;return this.init(),br.reset(),n&&n.discardWatches?(this.watches.forEach(function(i){return r.maybeBroadcastWatch.forget(i)}),this.watches.clear(),ev(this)):this.broadcastWatches(),Promise.resolve()},t.prototype.removeOptimistic=function(n){var r=this.optimisticData.removeLayer(n);r!==this.optimisticData&&(this.optimisticData=r,this.broadcastWatches())},t.prototype.batch=function(n){var r=this,i=n.update,o=n.optimistic,a=o===void 0?!0:o,s=n.removeOptimistic,l=n.onWatchUpdated,u,c=function(d){var y=r,g=y.data,v=y.optimisticData;++r.txCount,d&&(r.data=r.optimisticData=d);try{return u=i(r)}finally{--r.txCount,r.data=g,r.optimisticData=v}},f=new Set;return l&&!this.txCount&&this.broadcastWatches(b(b({},n),{onWatchUpdated:function(d){return f.add(d),!1}})),typeof a=="string"?this.optimisticData=this.optimisticData.addLayer(a,c):a===!1?c(this.data):c(),typeof s=="string"&&(this.optimisticData=this.optimisticData.removeLayer(s)),l&&f.size?(this.broadcastWatches(b(b({},n),{onWatchUpdated:function(d,y){var g=l.call(this,d,y);return g!==!1&&f.delete(d),g}})),f.size&&f.forEach(function(d){return r.maybeBroadcastWatch.dirty(d)})):this.broadcastWatches(n),u},t.prototype.performTransaction=function(n,r){return this.batch({update:n,optimistic:r||r!==null})},t.prototype.transformDocument=function(n){return this.addTypenameToDocument(this.addFragmentsToDocument(n))},t.prototype.broadcastWatches=function(n){var r=this;this.txCount||this.watches.forEach(function(i){return r.maybeBroadcastWatch(i,n)})},t.prototype.addFragmentsToDocument=function(n){var r=this.config.fragments;return r?r.transform(n):n},t.prototype.addTypenameToDocument=function(n){return this.addTypename?this.addTypenameTransform.transformDocument(n):n},t.prototype.broadcastWatch=function(n,r){var i=n.lastDiff,o=this.diff(n);r&&(n.optimistic&&typeof r.optimistic=="string"&&(o.fromOptimisticTransaction=!0),r.onWatchUpdated&&r.onWatchUpdated.call(this,n,o,i)===!1)||(!i||!we(i.result,o.result))&&n.callback(n.lastDiff=o,i)},t}(f1);globalThis.__DEV__!==!1&&(x1.prototype.getMemoryInternals=Bx);var me;(function(e){e[e.loading=1]="loading",e[e.setVariables=2]="setVariables",e[e.fetchMore=3]="fetchMore",e[e.refetch=4]="refetch",e[e.poll=6]="poll",e[e.ready=7]="ready",e[e.error=8]="error"})(me||(me={}));function Pa(e){return e?e<7:!1}function $k(e,t,n,r){var i=t.data,o=kn(t,["data"]),a=n.data,s=kn(n,["data"]);return we(o,s)&&Hs(Ha(e).selectionSet,i,a,{fragmentMap:Xl(Zl(e)),variables:r})}function Hs(e,t,n,r){if(t===n)return!0;var i=new Set;return e.selections.every(function(o){if(i.has(o)||(i.add(o),!Qa(o,r.variables))||fv(o))return!0;if(Nr(o)){var a=Or(o),s=t&&t[a],l=n&&n[a],u=o.selectionSet;if(!u)return we(s,l);var c=Array.isArray(s),f=Array.isArray(l);if(c!==f)return!1;if(c&&f){var d=s.length;if(l.length!==d)return!1;for(var y=0;y<d;++y)if(!Hs(u,s[y],l[y],r))return!1;return!0}return Hs(u,s,l,r)}else{var g=Kl(o,r.fragmentMap);if(g)return fv(g)?!0:Hs(g.selectionSet,t,n,r)}})}function fv(e){return!!e.directives&&e.directives.some(qk)}function qk(e){return e.name.value==="nonreactive"}var dv=Object.assign,Hk=Object.hasOwnProperty,Mf=function(e){Nn(t,e);function t(n){var r=n.queryManager,i=n.queryInfo,o=n.options,a=e.call(this,function(E){try{var h=E._subscription._observer;h&&!h.error&&(h.error=Wk)}catch{}var m=!a.observers.size;a.observers.add(E);var p=a.last;return p&&p.error?E.error&&E.error(p.error):p&&p.result&&E.next&&E.next(p.result),m&&a.reobserve().catch(function(){}),function(){a.observers.delete(E)&&!a.observers.size&&a.tearDownQuery()}})||this;a.observers=new Set,a.subscriptions=new Set,a.queryInfo=i,a.queryManager=r,a.waitForOwnResult=rc(o.fetchPolicy),a.isTornDown=!1;var s=r.defaultOptions.watchQuery,l=s===void 0?{}:s,u=l.fetchPolicy,c=u===void 0?"cache-first":u,f=o.fetchPolicy,d=f===void 0?c:f,y=o.initialFetchPolicy,g=y===void 0?d==="standby"?c:d:y;a.options=b(b({},o),{initialFetchPolicy:g,fetchPolicy:d}),a.queryId=i.queryId||r.generateQueryId();var v=qa(a.query);return a.queryName=v&&v.name&&v.name.value,a}return Object.defineProperty(t.prototype,"query",{get:function(){return this.lastQuery||this.options.query},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"variables",{get:function(){return this.options.variables},enumerable:!1,configurable:!0}),t.prototype.result=function(){var n=this;return new Promise(function(r,i){var o={next:function(s){r(s),n.observers.delete(o),n.observers.size||n.queryManager.removeQuery(n.queryId),setTimeout(function(){a.unsubscribe()},0)},error:i},a=n.subscribe(o)})},t.prototype.resetDiff=function(){this.queryInfo.resetDiff()},t.prototype.getCurrentResult=function(n){n===void 0&&(n=!0);var r=this.getLastResult(!0),i=this.queryInfo.networkStatus||r&&r.networkStatus||me.ready,o=b(b({},r),{loading:Pa(i),networkStatus:i}),a=this.options.fetchPolicy,s=a===void 0?"cache-first":a;if(!(rc(s)||this.queryManager.getDocumentInfo(this.query).hasForcedResolvers))if(this.waitForOwnResult)this.queryInfo.updateWatch();else{var l=this.queryInfo.getDiff();(l.complete||this.options.returnPartialData)&&(o.data=l.result),we(o.data,{})&&(o.data=void 0),l.complete?(delete o.partial,l.complete&&o.networkStatus===me.loading&&(s==="cache-first"||s==="cache-only")&&(o.networkStatus=me.ready,o.loading=!1)):o.partial=!0,globalThis.__DEV__!==!1&&!l.complete&&!this.options.partialRefetch&&!o.loading&&!o.data&&!o.error&&k1(l.missing)}return n&&this.updateLastResult(o),o},t.prototype.isDifferentFromLastResult=function(n,r){if(!this.last)return!0;var i=this.queryManager.getDocumentInfo(this.query).hasNonreactiveDirective?!$k(this.query,this.last.result,n,this.variables):!we(this.last.result,n);return i||r&&!we(this.last.variables,r)},t.prototype.getLast=function(n,r){var i=this.last;if(i&&i[n]&&(!r||we(i.variables,this.variables)))return i[n]},t.prototype.getLastResult=function(n){return this.getLast("result",n)},t.prototype.getLastError=function(n){return this.getLast("error",n)},t.prototype.resetLastResults=function(){delete this.last,this.isTornDown=!1},t.prototype.resetQueryStoreErrors=function(){this.queryManager.resetErrors(this.queryId)},t.prototype.refetch=function(n){var r,i={pollInterval:0},o=this.options.fetchPolicy;if(o==="cache-and-network"?i.fetchPolicy=o:o==="no-cache"?i.fetchPolicy="no-cache":i.fetchPolicy="network-only",globalThis.__DEV__!==!1&&n&&Hk.call(n,"variables")){var a=U0(this.query),s=a.variableDefinitions;(!s||!s.some(function(l){return l.variable.name.value==="variables"}))&&globalThis.__DEV__!==!1&&X.warn(20,n,((r=a.name)===null||r===void 0?void 0:r.value)||a)}return n&&!we(this.options.variables,n)&&(i.variables=this.options.variables=b(b({},this.options.variables),n)),this.queryInfo.resetLastWrite(),this.reobserve(i,me.refetch)},t.prototype.fetchMore=function(n){var r=this,i=b(b({},n.query?n:b(b(b(b({},this.options),{query:this.options.query}),n),{variables:b(b({},this.options.variables),n.variables)})),{fetchPolicy:"no-cache"});i.query=this.transformDocument(i.query);var o=this.queryManager.generateQueryId();this.lastQuery=n.query?this.transformDocument(this.options.query):i.query;var a=this.queryInfo,s=a.networkStatus;a.networkStatus=me.fetchMore,i.notifyOnNetworkStatusChange&&this.observe();var l=new Set;return this.queryManager.fetchQuery(o,i,me.fetchMore).then(function(u){return r.queryManager.removeQuery(o),a.networkStatus===me.fetchMore&&(a.networkStatus=s),r.queryManager.cache.batch({update:function(c){var f=n.updateQuery;f?c.updateQuery({query:r.query,variables:r.variables,returnPartialData:!0,optimistic:!1},function(d){return f(d,{fetchMoreResult:u.data,variables:i.variables})}):c.writeQuery({query:i.query,variables:i.variables,data:u.data})},onWatchUpdated:function(c){l.add(c.query)}}),u}).finally(function(){l.has(r.query)||b1(r)})},t.prototype.subscribeToMore=function(n){var r=this,i=this.queryManager.startGraphQLSubscription({query:n.document,variables:n.variables,context:n.context}).subscribe({next:function(o){var a=n.updateQuery;a&&r.updateQuery(function(s,l){var u=l.variables;return a(s,{subscriptionData:o,variables:u})})},error:function(o){if(n.onError){n.onError(o);return}globalThis.__DEV__!==!1&&X.error(21,o)}});return this.subscriptions.add(i),function(){r.subscriptions.delete(i)&&i.unsubscribe()}},t.prototype.setOptions=function(n){return this.reobserve(n)},t.prototype.silentSetOptions=function(n){var r=ci(this.options,n||{});dv(this.options,r)},t.prototype.setVariables=function(n){return we(this.variables,n)?this.observers.size?this.result():Promise.resolve():(this.options.variables=n,this.observers.size?this.reobserve({fetchPolicy:this.options.initialFetchPolicy,variables:n},me.setVariables):Promise.resolve())},t.prototype.updateQuery=function(n){var r=this.queryManager,i=r.cache.diff({query:this.options.query,variables:this.variables,returnPartialData:!0,optimistic:!1}).result,o=n(i,{variables:this.variables});o&&(r.cache.writeQuery({query:this.options.query,data:o,variables:this.variables}),r.broadcastQueries())},t.prototype.startPolling=function(n){this.options.pollInterval=n,this.updatePolling()},t.prototype.stopPolling=function(){this.options.pollInterval=0,this.updatePolling()},t.prototype.applyNextFetchPolicy=function(n,r){if(r.nextFetchPolicy){var i=r.fetchPolicy,o=i===void 0?"cache-first":i,a=r.initialFetchPolicy,s=a===void 0?o:a;o==="standby"||(typeof r.nextFetchPolicy=="function"?r.fetchPolicy=r.nextFetchPolicy(o,{reason:n,options:r,observable:this,initialFetchPolicy:s}):n==="variables-changed"?r.fetchPolicy=s:r.fetchPolicy=r.nextFetchPolicy)}return r.fetchPolicy},t.prototype.fetch=function(n,r,i){return this.queryManager.setObservableQuery(this),this.queryManager.fetchConcastWithInfo(this.queryId,n,r,i)},t.prototype.updatePolling=function(){var n=this;if(!this.queryManager.ssrMode){var r=this,i=r.pollingInfo,o=r.options.pollInterval;if(!o){i&&(clearTimeout(i.timeout),delete this.pollingInfo);return}if(!(i&&i.interval===o)){X(o,22);var a=i||(this.pollingInfo={});a.interval=o;var s=function(){var u,c;n.pollingInfo&&(!Pa(n.queryInfo.networkStatus)&&!(!((c=(u=n.options).skipPollAttempt)===null||c===void 0)&&c.call(u))?n.reobserve({fetchPolicy:n.options.initialFetchPolicy==="no-cache"?"no-cache":"network-only"},me.poll).then(l,l):l())},l=function(){var u=n.pollingInfo;u&&(clearTimeout(u.timeout),u.timeout=setTimeout(s,u.interval))};l()}}},t.prototype.updateLastResult=function(n,r){r===void 0&&(r=this.variables);var i=this.getLastError();return i&&this.last&&!we(r,this.last.variables)&&(i=void 0),this.last=b({result:this.queryManager.assumeImmutableResults?n:a1(n),variables:r},i?{error:i}:null)},t.prototype.reobserveAsConcast=function(n,r){var i=this;this.isTornDown=!1;var o=r===me.refetch||r===me.fetchMore||r===me.poll,a=this.options.variables,s=this.options.fetchPolicy,l=ci(this.options,n||{}),u=o?l:dv(this.options,l),c=this.transformDocument(u.query);this.lastQuery=c,o||(this.updatePolling(),n&&n.variables&&!we(n.variables,a)&&u.fetchPolicy!=="standby"&&u.fetchPolicy===s&&(this.applyNextFetchPolicy("variables-changed",u),r===void 0&&(r=me.setVariables))),this.waitForOwnResult&&(this.waitForOwnResult=rc(u.fetchPolicy));var f=function(){i.concast===g&&(i.waitForOwnResult=!1)},d=u.variables&&b({},u.variables),y=this.fetch(u,r,c),g=y.concast,v=y.fromLink,E={next:function(h){we(i.variables,d)&&(f(),i.reportResult(h,d))},error:function(h){we(i.variables,d)&&(f(),i.reportError(h,d))}};return!o&&(v||!this.concast)&&(this.concast&&this.observer&&this.concast.removeObserver(this.observer),this.concast=g,this.observer=E),g.addObserver(E),g},t.prototype.reobserve=function(n,r){return this.reobserveAsConcast(n,r).promise},t.prototype.resubscribeAfterError=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=this.last;this.resetLastResults();var o=this.subscribe.apply(this,n);return this.last=i,o},t.prototype.observe=function(){this.reportResult(this.getCurrentResult(!1),this.variables)},t.prototype.reportResult=function(n,r){var i=this.getLastError(),o=this.isDifferentFromLastResult(n,r);(i||!n.partial||this.options.returnPartialData)&&this.updateLastResult(n,r),(i||o)&&na(this.observers,"next",n)},t.prototype.reportError=function(n,r){var i=b(b({},this.getLastResult()),{error:n,errors:n.graphQLErrors,networkStatus:me.error,loading:!1});this.updateLastResult(i,r),na(this.observers,"error",this.last.error=n)},t.prototype.hasObservers=function(){return this.observers.size>0},t.prototype.tearDownQuery=function(){this.isTornDown||(this.concast&&this.observer&&(this.concast.removeObserver(this.observer),delete this.concast,delete this.observer),this.stopPolling(),this.subscriptions.forEach(function(n){return n.unsubscribe()}),this.subscriptions.clear(),this.queryManager.stopQuery(this.queryId),this.observers.clear(),this.isTornDown=!0)},t.prototype.transformDocument=function(n){return this.queryManager.transform(n)},t}(Re);s1(Mf);function b1(e){var t=e.options,n=t.fetchPolicy,r=t.nextFetchPolicy;return n==="cache-and-network"||n==="network-only"?e.reobserve({fetchPolicy:"cache-first",nextFetchPolicy:function(i,o){return this.nextFetchPolicy=r,typeof this.nextFetchPolicy=="function"?this.nextFetchPolicy(i,o):n}}):e.reobserve()}function Wk(e){globalThis.__DEV__!==!1&&X.error(23,e.message,e.stack)}function k1(e){globalThis.__DEV__!==!1&&e&&globalThis.__DEV__!==!1&&X.debug(24,e)}function rc(e){return e==="network-only"||e==="no-cache"||e==="standby"}var C1=function(){function e(t){var n=t.cache,r=t.client,i=t.resolvers,o=t.fragmentMatcher;this.selectionsToResolveCache=new WeakMap,this.cache=n,r&&(this.client=r),i&&this.addResolvers(i),o&&this.setFragmentMatcher(o)}return e.prototype.addResolvers=function(t){var n=this;this.resolvers=this.resolvers||{},Array.isArray(t)?t.forEach(function(r){n.resolvers=Lm(n.resolvers,r)}):this.resolvers=Lm(this.resolvers,t)},e.prototype.setResolvers=function(t){this.resolvers={},this.addResolvers(t)},e.prototype.getResolvers=function(){return this.resolvers||{}},e.prototype.runResolvers=function(t){return ur(this,arguments,void 0,function(n){var r=n.document,i=n.remoteResult,o=n.context,a=n.variables,s=n.onlyRunForcedResolvers,l=s===void 0?!1:s;return cr(this,function(u){return r?[2,this.resolveDocument(r,i.data,o,a,this.fragmentMatcher,l).then(function(c){return b(b({},i),{data:c.result})})]:[2,i]})})},e.prototype.setFragmentMatcher=function(t){this.fragmentMatcher=t},e.prototype.getFragmentMatcher=function(){return this.fragmentMatcher},e.prototype.clientQuery=function(t){return Oa(["client"],t)&&this.resolvers?t:null},e.prototype.serverQuery=function(t){return n1(t)},e.prototype.prepareContext=function(t){var n=this.cache;return b(b({},t),{cache:n,getCacheKey:function(r){return n.identify(r)}})},e.prototype.addExportedVariables=function(t){return ur(this,arguments,void 0,function(n,r,i){return r===void 0&&(r={}),i===void 0&&(i={}),cr(this,function(o){return n?[2,this.resolveDocument(n,this.buildRootValueFromCache(n,r)||{},this.prepareContext(i),r).then(function(a){return b(b({},r),a.exportedVariables)})]:[2,b({},r)]})})},e.prototype.shouldForceResolvers=function(t){var n=!1;return Wn(t,{Directive:{enter:function(r){if(r.name.value==="client"&&r.arguments&&(n=r.arguments.some(function(i){return i.name.value==="always"&&i.value.kind==="BooleanValue"&&i.value.value===!0}),n))return rh}}}),n},e.prototype.buildRootValueFromCache=function(t,n){return this.cache.diff({query:Ob(t),variables:n,returnPartialData:!0,optimistic:!1}).result},e.prototype.resolveDocument=function(t,n){return ur(this,arguments,void 0,function(r,i,o,a,s,l){var u,c,f,d,y,g,v,E,h,m,p;return o===void 0&&(o={}),a===void 0&&(a={}),s===void 0&&(s=function(){return!0}),l===void 0&&(l=!1),cr(this,function(S){return u=Ha(r),c=Zl(r),f=Xl(c),d=this.collectSelectionsToResolve(u,f),y=u.operation,g=y?y.charAt(0).toUpperCase()+y.slice(1):"Query",v=this,E=v.cache,h=v.client,m={fragmentMap:f,context:b(b({},o),{cache:E,client:h}),variables:a,fragmentMatcher:s,defaultOperationType:g,exportedVariables:{},selectionsToResolve:d,onlyRunForcedResolvers:l},p=!1,[2,this.resolveSelectionSet(u.selectionSet,p,i,m).then(function(T){return{result:T,exportedVariables:m.exportedVariables}})]})})},e.prototype.resolveSelectionSet=function(t,n,r,i){return ur(this,void 0,void 0,function(){var o,a,s,l,u,c=this;return cr(this,function(f){return o=i.fragmentMap,a=i.context,s=i.variables,l=[r],u=function(d){return ur(c,void 0,void 0,function(){var y,g;return cr(this,function(v){return!n&&!i.selectionsToResolve.has(d)?[2]:Qa(d,s)?Nr(d)?[2,this.resolveField(d,n,r,i).then(function(E){var h;typeof E<"u"&&l.push((h={},h[Or(d)]=E,h))})]:(lb(d)?y=d:(y=o[d.name.value],X(y,18,d.name.value)),y&&y.typeCondition&&(g=y.typeCondition.name.value,i.fragmentMatcher(r,g,a))?[2,this.resolveSelectionSet(y.selectionSet,n,r,i).then(function(E){l.push(E)})]:[2]):[2]})})},[2,Promise.all(t.selections.map(u)).then(function(){return ru(l)})]})})},e.prototype.resolveField=function(t,n,r,i){return ur(this,void 0,void 0,function(){var o,a,s,l,u,c,f,d,y,g=this;return cr(this,function(v){return r?(o=i.variables,a=t.name.value,s=Or(t),l=a!==s,u=r[s]||r[a],c=Promise.resolve(u),(!i.onlyRunForcedResolvers||this.shouldForceResolvers(t))&&(f=r.__typename||i.defaultOperationType,d=this.resolvers&&this.resolvers[f],d&&(y=d[l?a:s],y&&(c=Promise.resolve(gh.withValue(this.cache,y,[r,Jl(t,o),i.context,{field:t,fragmentMap:i.fragmentMap}]))))),[2,c.then(function(E){var h,m;if(E===void 0&&(E=u),t.directives&&t.directives.forEach(function(S){S.name.value==="export"&&S.arguments&&S.arguments.forEach(function(T){T.name.value==="as"&&T.value.kind==="StringValue"&&(i.exportedVariables[T.value.value]=E)})}),!t.selectionSet||E==null)return E;var p=(m=(h=t.directives)===null||h===void 0?void 0:h.some(function(S){return S.name.value==="client"}))!==null&&m!==void 0?m:!1;if(Array.isArray(E))return g.resolveSubSelectedArray(t,n||p,E,i);if(t.selectionSet)return g.resolveSelectionSet(t.selectionSet,n||p,E,i)})]):[2,null]})})},e.prototype.resolveSubSelectedArray=function(t,n,r,i){var o=this;return Promise.all(r.map(function(a){if(a===null)return null;if(Array.isArray(a))return o.resolveSubSelectedArray(t,n,a,i);if(t.selectionSet)return o.resolveSelectionSet(t.selectionSet,n,a,i)}))},e.prototype.collectSelectionsToResolve=function(t,n){var r=function(a){return!Array.isArray(a)},i=this.selectionsToResolveCache;function o(a){if(!i.has(a)){var s=new Set;i.set(a,s),Wn(a,{Directive:function(l,u,c,f,d){l.name.value==="client"&&d.forEach(function(y){r(y)&&bm(y)&&s.add(y)})},FragmentSpread:function(l,u,c,f,d){var y=n[l.name.value];X(y,19,l.name.value);var g=o(y);g.size>0&&(d.forEach(function(v){r(v)&&bm(v)&&s.add(v)}),s.add(l),g.forEach(function(v){s.add(v)}))}})}return i.get(a)}return o(t)},e}(),Ui=new(pi?WeakMap:Map);function ic(e,t){var n=e[t];typeof n=="function"&&(e[t]=function(){return Ui.set(e,(Ui.get(e)+1)%1e15),n.apply(this,arguments)})}function hv(e){e.notifyTimeout&&(clearTimeout(e.notifyTimeout),e.notifyTimeout=void 0)}var oc=function(){function e(t,n){n===void 0&&(n=t.generateQueryId()),this.queryId=n,this.listeners=new Set,this.document=null,this.lastRequestId=1,this.stopped=!1,this.dirty=!1,this.observableQuery=null;var r=this.cache=t.cache;Ui.has(r)||(Ui.set(r,0),ic(r,"evict"),ic(r,"modify"),ic(r,"reset"))}return e.prototype.init=function(t){var n=t.networkStatus||me.loading;return this.variables&&this.networkStatus!==me.loading&&!we(this.variables,t.variables)&&(n=me.setVariables),we(t.variables,this.variables)||(this.lastDiff=void 0),Object.assign(this,{document:t.document,variables:t.variables,networkError:null,graphQLErrors:this.graphQLErrors||[],networkStatus:n}),t.observableQuery&&this.setObservableQuery(t.observableQuery),t.lastRequestId&&(this.lastRequestId=t.lastRequestId),this},e.prototype.reset=function(){hv(this),this.dirty=!1},e.prototype.resetDiff=function(){this.lastDiff=void 0},e.prototype.getDiff=function(){var t=this.getDiffOptions();if(this.lastDiff&&we(t,this.lastDiff.options))return this.lastDiff.diff;this.updateWatch(this.variables);var n=this.observableQuery;if(n&&n.options.fetchPolicy==="no-cache")return{complete:!1};var r=this.cache.diff(t);return this.updateLastDiff(r,t),r},e.prototype.updateLastDiff=function(t,n){this.lastDiff=t?{diff:t,options:n||this.getDiffOptions()}:void 0},e.prototype.getDiffOptions=function(t){var n;return t===void 0&&(t=this.variables),{query:this.document,variables:t,returnPartialData:!0,optimistic:!0,canonizeResults:(n=this.observableQuery)===null||n===void 0?void 0:n.options.canonizeResults}},e.prototype.setDiff=function(t){var n=this,r,i=this.lastDiff&&this.lastDiff.diff;t&&!t.complete&&!(!((r=this.observableQuery)===null||r===void 0)&&r.options.returnPartialData)&&!(i&&i.complete)||(this.updateLastDiff(t),!this.dirty&&!we(i&&i.result,t&&t.result)&&(this.dirty=!0,this.notifyTimeout||(this.notifyTimeout=setTimeout(function(){return n.notify()},0))))},e.prototype.setObservableQuery=function(t){var n=this;t!==this.observableQuery&&(this.oqListener&&this.listeners.delete(this.oqListener),this.observableQuery=t,t?(t.queryInfo=this,this.listeners.add(this.oqListener=function(){var r=n.getDiff();r.fromOptimisticTransaction?t.observe():b1(t)})):delete this.oqListener)},e.prototype.notify=function(){var t=this;hv(this),this.shouldNotify()&&this.listeners.forEach(function(n){return n(t)}),this.dirty=!1},e.prototype.shouldNotify=function(){if(!this.dirty||!this.listeners.size)return!1;if(Pa(this.networkStatus)&&this.observableQuery){var t=this.observableQuery.options.fetchPolicy;if(t!=="cache-only"&&t!=="cache-and-network")return!1}return!0},e.prototype.stop=function(){if(!this.stopped){this.stopped=!0,this.reset(),this.cancel(),this.cancel=e.prototype.cancel;var t=this.observableQuery;t&&t.stopPolling()}},e.prototype.cancel=function(){},e.prototype.updateWatch=function(t){var n=this;t===void 0&&(t=this.variables);var r=this.observableQuery;if(!(r&&r.options.fetchPolicy==="no-cache")){var i=b(b({},this.getDiffOptions(t)),{watcher:this,callback:function(o){return n.setDiff(o)}});(!this.lastWatch||!we(i,this.lastWatch))&&(this.cancel(),this.cancel=this.cache.watch(this.lastWatch=i))}},e.prototype.resetLastWrite=function(){this.lastWrite=void 0},e.prototype.shouldWrite=function(t,n){var r=this.lastWrite;return!(r&&r.dmCount===Ui.get(this.cache)&&we(n,r.variables)&&we(t.data,r.result.data))},e.prototype.markResult=function(t,n,r,i){var o=this,a=new Dr,s=en(t.errors)?t.errors.slice(0):[];if(this.reset(),"incremental"in t&&en(t.incremental)){var l=l1(this.getDiff().result,t);t.data=l}else if("hasNext"in t&&t.hasNext){var u=this.getDiff();t.data=a.merge(u.result,t.data)}this.graphQLErrors=s,r.fetchPolicy==="no-cache"?this.updateLastDiff({result:t.data,complete:!0},this.getDiffOptions(r.variables)):i!==0&&(Ff(t,r.errorPolicy)?this.cache.performTransaction(function(c){if(o.shouldWrite(t,r.variables))c.writeQuery({query:n,data:t.data,variables:r.variables,overwrite:i===1}),o.lastWrite={result:t,variables:r.variables,dmCount:Ui.get(o.cache)};else if(o.lastDiff&&o.lastDiff.diff.complete){t.data=o.lastDiff.diff.result;return}var f=o.getDiffOptions(r.variables),d=c.diff(f);!o.stopped&&we(o.variables,r.variables)&&o.updateWatch(r.variables),o.updateLastDiff(d,f),d.complete&&(t.data=d.result)}):this.lastWrite=void 0)},e.prototype.markReady=function(){return this.networkError=null,this.networkStatus=me.ready},e.prototype.markError=function(t){return this.networkStatus=me.error,this.lastWrite=void 0,this.reset(),t.graphQLErrors&&(this.graphQLErrors=t.graphQLErrors),t.networkError&&(this.networkError=t.networkError),t},e}();function Ff(e,t){t===void 0&&(t="none");var n=t==="ignore"||t==="all",r=!$s(e);return!r&&n&&e.data&&(r=!0),r}var Gk=Object.prototype.hasOwnProperty,pv=Object.create(null),Yk=function(){function e(t){var n=t.cache,r=t.link,i=t.defaultOptions,o=t.documentTransform,a=t.queryDeduplication,s=a===void 0?!1:a,l=t.onBroadcast,u=t.ssrMode,c=u===void 0?!1:u,f=t.clientAwareness,d=f===void 0?{}:f,y=t.localState,g=t.assumeImmutableResults,v=g===void 0?!!n.assumeImmutableResults:g,E=t.defaultContext,h=this;this.clientAwareness={},this.queries=new Map,this.fetchCancelFns=new Map,this.transformCache=new oh(Cn["queryManager.getDocumentInfo"]||2e3),this.queryIdCounter=1,this.requestIdCounter=1,this.mutationIdCounter=1,this.inFlightLinkObservables=new po(!1);var m=new Z0(function(p){return h.cache.transformDocument(p)},{cache:!1});this.cache=n,this.link=r,this.defaultOptions=i||Object.create(null),this.queryDeduplication=s,this.clientAwareness=d,this.localState=y||new C1({cache:n}),this.ssrMode=c,this.assumeImmutableResults=v,this.documentTransform=o?m.concat(o).concat(m):m,this.defaultContext=E||Object.create(null),(this.onBroadcast=l)&&(this.mutationStore=Object.create(null))}return e.prototype.stop=function(){var t=this;this.queries.forEach(function(n,r){t.stopQueryNoBroadcast(r)}),this.cancelPendingFetches(_t(25))},e.prototype.cancelPendingFetches=function(t){this.fetchCancelFns.forEach(function(n){return n(t)}),this.fetchCancelFns.clear()},e.prototype.mutate=function(t){return ur(this,arguments,void 0,function(n){var r,i,o,a,s,l,u,c=n.mutation,f=n.variables,d=n.optimisticResponse,y=n.updateQueries,g=n.refetchQueries,v=g===void 0?[]:g,E=n.awaitRefetchQueries,h=E===void 0?!1:E,m=n.update,p=n.onQueryUpdated,S=n.fetchPolicy,T=S===void 0?((l=this.defaultOptions.mutate)===null||l===void 0?void 0:l.fetchPolicy)||"network-only":S,O=n.errorPolicy,w=O===void 0?((u=this.defaultOptions.mutate)===null||u===void 0?void 0:u.errorPolicy)||"none":O,k=n.keepRootFields,I=n.context;return cr(this,function(P){switch(P.label){case 0:return X(c,26),X(T==="network-only"||T==="no-cache",27),r=this.generateMutationId(),c=this.cache.transformForLink(this.transform(c)),i=this.getDocumentInfo(c).hasClientExports,f=this.getVariables(c,f),i?[4,this.localState.addExportedVariables(c,f,I)]:[3,2];case 1:f=P.sent(),P.label=2;case 2:return o=this.mutationStore&&(this.mutationStore[r]={mutation:c,variables:f,loading:!0,error:null}),a=d&&this.markMutationOptimistic(d,{mutationId:r,document:c,variables:f,fetchPolicy:T,errorPolicy:w,context:I,updateQueries:y,update:m,keepRootFields:k}),this.broadcastQueries(),s=this,[2,new Promise(function(K,se){return Ju(s.getObservableFromLink(c,b(b({},I),{optimisticResponse:a?d:void 0}),f,!1),function(ee){if($s(ee)&&w==="none")throw new jn({graphQLErrors:Of(ee)});o&&(o.loading=!1,o.error=null);var le=b({},ee);return typeof v=="function"&&(v=v(le)),w==="ignore"&&$s(le)&&delete le.errors,s.markMutationResult({mutationId:r,result:le,document:c,variables:f,fetchPolicy:T,errorPolicy:w,context:I,update:m,updateQueries:y,awaitRefetchQueries:h,refetchQueries:v,removeOptimistic:a?r:void 0,onQueryUpdated:p,keepRootFields:k})}).subscribe({next:function(ee){s.broadcastQueries(),(!("hasNext"in ee)||ee.hasNext===!1)&&K(ee)},error:function(ee){o&&(o.loading=!1,o.error=ee),a&&s.cache.removeOptimistic(r),s.broadcastQueries(),se(ee instanceof jn?ee:new jn({networkError:ee}))}})})]}})})},e.prototype.markMutationResult=function(t,n){var r=this;n===void 0&&(n=this.cache);var i=t.result,o=[],a=t.fetchPolicy==="no-cache";if(!a&&Ff(i,t.errorPolicy)){if(Gi(i)||o.push({result:i.data,dataId:"ROOT_MUTATION",query:t.document,variables:t.variables}),Gi(i)&&en(i.incremental)){var s=n.diff({id:"ROOT_MUTATION",query:this.getDocumentInfo(t.document).asQuery,variables:t.variables,optimistic:!1,returnPartialData:!0}),l=void 0;s.result&&(l=l1(s.result,i)),typeof l<"u"&&(i.data=l,o.push({result:l,dataId:"ROOT_MUTATION",query:t.document,variables:t.variables}))}var u=t.updateQueries;u&&this.queries.forEach(function(f,d){var y=f.observableQuery,g=y&&y.queryName;if(!(!g||!Gk.call(u,g))){var v=u[g],E=r.queries.get(d),h=E.document,m=E.variables,p=n.diff({query:h,variables:m,returnPartialData:!0,optimistic:!1}),S=p.result,T=p.complete;if(T&&S){var O=v(S,{mutationResult:i,queryName:h&&xf(h)||void 0,queryVariables:m});O&&o.push({result:O,dataId:"ROOT_QUERY",query:h,variables:m})}}})}if(o.length>0||(t.refetchQueries||"").length>0||t.update||t.onQueryUpdated||t.removeOptimistic){var c=[];if(this.refetchQueries({updateCache:function(f){a||o.forEach(function(v){return f.write(v)});var d=t.update,y=!Qb(i)||Gi(i)&&!i.hasNext;if(d){if(!a){var g=f.diff({id:"ROOT_MUTATION",query:r.getDocumentInfo(t.document).asQuery,variables:t.variables,optimistic:!1,returnPartialData:!0});g.complete&&(i=b(b({},i),{data:g.result}),"incremental"in i&&delete i.incremental,"hasNext"in i&&delete i.hasNext)}y&&d(f,i,{context:t.context,variables:t.variables})}!a&&!t.keepRootFields&&y&&f.modify({id:"ROOT_MUTATION",fields:function(v,E){var h=E.fieldName,m=E.DELETE;return h==="__typename"?v:m}})},include:t.refetchQueries,optimistic:!1,removeOptimistic:t.removeOptimistic,onQueryUpdated:t.onQueryUpdated||null}).forEach(function(f){return c.push(f)}),t.awaitRefetchQueries||t.onQueryUpdated)return Promise.all(c).then(function(){return i})}return Promise.resolve(i)},e.prototype.markMutationOptimistic=function(t,n){var r=this,i=typeof t=="function"?t(n.variables,{IGNORE:pv}):t;return i===pv?!1:(this.cache.recordOptimisticTransaction(function(o){try{r.markMutationResult(b(b({},n),{result:{data:i}}),o)}catch(a){globalThis.__DEV__!==!1&&X.error(a)}},n.mutationId),!0)},e.prototype.fetchQuery=function(t,n,r){return this.fetchConcastWithInfo(t,n,r).concast.promise},e.prototype.getQueryStore=function(){var t=Object.create(null);return this.queries.forEach(function(n,r){t[r]={variables:n.variables,networkStatus:n.networkStatus,networkError:n.networkError,graphQLErrors:n.graphQLErrors}}),t},e.prototype.resetErrors=function(t){var n=this.queries.get(t);n&&(n.networkError=void 0,n.graphQLErrors=[])},e.prototype.transform=function(t){return this.documentTransform.transformDocument(t)},e.prototype.getDocumentInfo=function(t){var n=this.transformCache;if(!n.has(t)){var r={hasClientExports:_x(t),hasForcedResolvers:this.localState.shouldForceResolvers(t),hasNonreactiveDirective:Oa(["nonreactive"],t),clientQuery:this.localState.clientQuery(t),serverQuery:t1([{name:"client",remove:!0},{name:"connection"},{name:"nonreactive"}],t),defaultVars:sh(qa(t)),asQuery:b(b({},t),{definitions:t.definitions.map(function(i){return i.kind==="OperationDefinition"&&i.operation!=="query"?b(b({},i),{operation:"query"}):i})})};n.set(t,r)}return n.get(t)},e.prototype.getVariables=function(t,n){return b(b({},this.getDocumentInfo(t).defaultVars),n)},e.prototype.watchQuery=function(t){var n=this.transform(t.query);t=b(b({},t),{variables:this.getVariables(n,t.variables)}),typeof t.notifyOnNetworkStatusChange>"u"&&(t.notifyOnNetworkStatusChange=!1);var r=new oc(this),i=new Mf({queryManager:this,queryInfo:r,options:t});return i.lastQuery=n,this.queries.set(i.queryId,r),r.init({document:n,observableQuery:i,variables:i.variables}),i},e.prototype.query=function(t,n){var r=this;return n===void 0&&(n=this.generateQueryId()),X(t.query,28),X(t.query.kind==="Document",29),X(!t.returnPartialData,30),X(!t.pollInterval,31),this.fetchQuery(n,b(b({},t),{query:this.transform(t.query)})).finally(function(){return r.stopQuery(n)})},e.prototype.generateQueryId=function(){return String(this.queryIdCounter++)},e.prototype.generateRequestId=function(){return this.requestIdCounter++},e.prototype.generateMutationId=function(){return String(this.mutationIdCounter++)},e.prototype.stopQueryInStore=function(t){this.stopQueryInStoreNoBroadcast(t),this.broadcastQueries()},e.prototype.stopQueryInStoreNoBroadcast=function(t){var n=this.queries.get(t);n&&n.stop()},e.prototype.clearStore=function(t){return t===void 0&&(t={discardWatches:!0}),this.cancelPendingFetches(_t(32)),this.queries.forEach(function(n){n.observableQuery?n.networkStatus=me.loading:n.stop()}),this.mutationStore&&(this.mutationStore=Object.create(null)),this.cache.reset(t)},e.prototype.getObservableQueries=function(t){var n=this;t===void 0&&(t="active");var r=new Map,i=new Map,o=new Set;return Array.isArray(t)&&t.forEach(function(a){typeof a=="string"?i.set(a,!1):Xx(a)?i.set(n.transform(a),!1):Qe(a)&&a.query&&o.add(a)}),this.queries.forEach(function(a,s){var l=a.observableQuery,u=a.document;if(l){if(t==="all"){r.set(s,l);return}var c=l.queryName,f=l.options.fetchPolicy;if(f==="standby"||t==="active"&&!l.hasObservers())return;(t==="active"||c&&i.has(c)||u&&i.has(u))&&(r.set(s,l),c&&i.set(c,!0),u&&i.set(u,!0))}}),o.size&&o.forEach(function(a){var s=mf("legacyOneTimeQuery"),l=n.getQuery(s).init({document:a.query,variables:a.variables}),u=new Mf({queryManager:n,queryInfo:l,options:b(b({},a),{fetchPolicy:"network-only"})});X(u.queryId===s),l.setObservableQuery(u),r.set(s,u)}),globalThis.__DEV__!==!1&&i.size&&i.forEach(function(a,s){a||globalThis.__DEV__!==!1&&X.warn(typeof s=="string"?33:34,s)}),r},e.prototype.reFetchObservableQueries=function(t){var n=this;t===void 0&&(t=!1);var r=[];return this.getObservableQueries(t?"all":"active").forEach(function(i,o){var a=i.options.fetchPolicy;i.resetLastResults(),(t||a!=="standby"&&a!=="cache-only")&&r.push(i.refetch()),n.getQuery(o).setDiff(null)}),this.broadcastQueries(),Promise.all(r)},e.prototype.setObservableQuery=function(t){this.getQuery(t.queryId).setObservableQuery(t)},e.prototype.startGraphQLSubscription=function(t){var n=this,r=t.query,i=t.fetchPolicy,o=t.errorPolicy,a=o===void 0?"none":o,s=t.variables,l=t.context,u=l===void 0?{}:l;r=this.transform(r),s=this.getVariables(r,s);var c=function(d){return n.getObservableFromLink(r,u,d).map(function(y){i!=="no-cache"&&(Ff(y,a)&&n.cache.write({query:r,result:y.data,dataId:"ROOT_SUBSCRIPTION",variables:d}),n.broadcastQueries());var g=$s(y),v=ok(y);if(g||v){var E={};if(g&&(E.graphQLErrors=y.errors),v&&(E.protocolErrors=y.extensions[yh]),a==="none"||v)throw new jn(E)}return a==="ignore"&&delete y.errors,y})};if(this.getDocumentInfo(r).hasClientExports){var f=this.localState.addExportedVariables(r,s,u).then(c);return new Re(function(d){var y=null;return f.then(function(g){return y=g.subscribe(d)},d.error),function(){return y&&y.unsubscribe()}})}return c(s)},e.prototype.stopQuery=function(t){this.stopQueryNoBroadcast(t),this.broadcastQueries()},e.prototype.stopQueryNoBroadcast=function(t){this.stopQueryInStoreNoBroadcast(t),this.removeQuery(t)},e.prototype.removeQuery=function(t){this.fetchCancelFns.delete(t),this.queries.has(t)&&(this.getQuery(t).stop(),this.queries.delete(t))},e.prototype.broadcastQueries=function(){this.onBroadcast&&this.onBroadcast(),this.queries.forEach(function(t){return t.notify()})},e.prototype.getLocalState=function(){return this.localState},e.prototype.getObservableFromLink=function(t,n,r,i){var o=this,a;i===void 0&&(i=(a=n==null?void 0:n.queryDeduplication)!==null&&a!==void 0?a:this.queryDeduplication);var s,l=this.getDocumentInfo(t),u=l.serverQuery,c=l.clientQuery;if(u){var f=this,d=f.inFlightLinkObservables,y=f.link,g={query:u,variables:r,operationName:xf(u)||void 0,context:this.prepareContext(b(b({},n),{forceFetch:!i}))};if(n=g.context,i){var v=nu(u),E=br(r),h=d.lookup(v,E);if(s=h.observable,!s){var m=new Ti([Nf(y,g)]);s=h.observable=m,m.beforeNext(function(){d.remove(v,E)})}}else s=new Ti([Nf(y,g)])}else s=new Ti([Re.of({data:{}})]),n=this.prepareContext(n);return c&&(s=Ju(s,function(p){return o.localState.runResolvers({document:c,remoteResult:p,context:n,variables:r})})),s},e.prototype.getResultsFromLink=function(t,n,r){var i=t.lastRequestId=this.generateRequestId(),o=this.cache.transformForLink(r.query);return Ju(this.getObservableFromLink(o,r.context,r.variables),function(a){var s=Of(a),l=s.length>0;if(i>=t.lastRequestId){if(l&&r.errorPolicy==="none")throw t.markError(new jn({graphQLErrors:s}));t.markResult(a,o,r,n),t.markReady()}var u={data:a.data,loading:!1,networkStatus:me.ready};return l&&r.errorPolicy!=="ignore"&&(u.errors=s,u.networkStatus=me.error),u},function(a){var s=ak(a)?a:new jn({networkError:a});throw i>=t.lastRequestId&&t.markError(s),s})},e.prototype.fetchConcastWithInfo=function(t,n,r,i){var o=this;r===void 0&&(r=me.loading),i===void 0&&(i=n.query);var a=this.getVariables(i,n.variables),s=this.getQuery(t),l=this.defaultOptions.watchQuery,u=n.fetchPolicy,c=u===void 0?l&&l.fetchPolicy||"cache-first":u,f=n.errorPolicy,d=f===void 0?l&&l.errorPolicy||"none":f,y=n.returnPartialData,g=y===void 0?!1:y,v=n.notifyOnNetworkStatusChange,E=v===void 0?!1:v,h=n.context,m=h===void 0?{}:h,p=Object.assign({},n,{query:i,variables:a,fetchPolicy:c,errorPolicy:d,returnPartialData:g,notifyOnNetworkStatusChange:E,context:m}),S=function(I){p.variables=I;var P=o.fetchQueryByPolicy(s,p,r);return p.fetchPolicy!=="standby"&&P.sources.length>0&&s.observableQuery&&s.observableQuery.applyNextFetchPolicy("after-fetch",n),P},T=function(){return o.fetchCancelFns.delete(t)};this.fetchCancelFns.set(t,function(I){T(),setTimeout(function(){return O.cancel(I)})});var O,w;if(this.getDocumentInfo(p.query).hasClientExports)O=new Ti(this.localState.addExportedVariables(p.query,p.variables,p.context).then(S).then(function(I){return I.sources})),w=!0;else{var k=S(p.variables);w=k.fromLink,O=new Ti(k.sources)}return O.promise.then(T,T),{concast:O,fromLink:w}},e.prototype.refetchQueries=function(t){var n=this,r=t.updateCache,i=t.include,o=t.optimistic,a=o===void 0?!1:o,s=t.removeOptimistic,l=s===void 0?a?mf("refetchQueries"):void 0:s,u=t.onQueryUpdated,c=new Map;i&&this.getObservableQueries(i).forEach(function(d,y){c.set(y,{oq:d,lastDiff:n.getQuery(y).getDiff()})});var f=new Map;return r&&this.cache.batch({update:r,optimistic:a&&l||!1,removeOptimistic:l,onWatchUpdated:function(d,y,g){var v=d.watcher instanceof oc&&d.watcher.observableQuery;if(v){if(u){c.delete(v.queryId);var E=u(v,y,g);return E===!0&&(E=v.refetch()),E!==!1&&f.set(v,E),E}u!==null&&c.set(v.queryId,{oq:v,lastDiff:g,diff:y})}}}),c.size&&c.forEach(function(d,y){var g=d.oq,v=d.lastDiff,E=d.diff,h;if(u){if(!E){var m=g.queryInfo;m.reset(),E=m.getDiff()}h=u(g,E,v)}(!u||h===!0)&&(h=g.refetch()),h!==!1&&f.set(g,h),y.indexOf("legacyOneTimeQuery")>=0&&n.stopQueryNoBroadcast(y)}),l&&this.cache.removeOptimistic(l),f},e.prototype.fetchQueryByPolicy=function(t,n,r){var i=this,o=n.query,a=n.variables,s=n.fetchPolicy,l=n.refetchWritePolicy,u=n.errorPolicy,c=n.returnPartialData,f=n.context,d=n.notifyOnNetworkStatusChange,y=t.networkStatus;t.init({document:o,variables:a,networkStatus:r});var g=function(){return t.getDiff()},v=function(S,T){T===void 0&&(T=t.networkStatus||me.loading);var O=S.result;globalThis.__DEV__!==!1&&!c&&!we(O,{})&&k1(S.missing);var w=function(k){return Re.of(b({data:k,loading:Pa(T),networkStatus:T},S.complete?null:{partial:!0}))};return O&&i.getDocumentInfo(o).hasForcedResolvers?i.localState.runResolvers({document:o,remoteResult:{data:O},context:f,variables:a,onlyRunForcedResolvers:!0}).then(function(k){return w(k.data||void 0)}):u==="none"&&T===me.refetch&&Array.isArray(S.missing)?w(void 0):w(O)},E=s==="no-cache"?0:r===me.refetch&&l!=="merge"?1:2,h=function(){return i.getResultsFromLink(t,E,{query:o,variables:a,context:f,fetchPolicy:s,errorPolicy:u})},m=d&&typeof y=="number"&&y!==r&&Pa(r);switch(s){default:case"cache-first":{var p=g();return p.complete?{fromLink:!1,sources:[v(p,t.markReady())]}:c||m?{fromLink:!0,sources:[v(p),h()]}:{fromLink:!0,sources:[h()]}}case"cache-and-network":{var p=g();return p.complete||c||m?{fromLink:!0,sources:[v(p),h()]}:{fromLink:!0,sources:[h()]}}case"cache-only":return{fromLink:!1,sources:[v(g(),t.markReady())]};case"network-only":return m?{fromLink:!0,sources:[v(g()),h()]}:{fromLink:!0,sources:[h()]};case"no-cache":return m?{fromLink:!0,sources:[v(t.getDiff()),h()]}:{fromLink:!0,sources:[h()]};case"standby":return{fromLink:!1,sources:[]}}},e.prototype.getQuery=function(t){return t&&!this.queries.has(t)&&this.queries.set(t,new oc(this,t)),this.queries.get(t)},e.prototype.prepareContext=function(t){t===void 0&&(t={});var n=this.localState.prepareContext(t);return b(b(b({},this.defaultContext),n),{clientAwareness:this.clientAwareness})},e}(),mv=!1,O1=function(){function e(t){var n=this;if(this.resetStoreCallbacks=[],this.clearStoreCallbacks=[],!t.cache)throw _t(15);var r=t.uri,i=t.credentials,o=t.headers,a=t.cache,s=t.documentTransform,l=t.ssrMode,u=l===void 0?!1:l,c=t.ssrForceFetchDelay,f=c===void 0?0:c,d=t.connectToDevTools,y=d===void 0?typeof window=="object"&&!window.__APOLLO_CLIENT__&&globalThis.__DEV__!==!1:d,g=t.queryDeduplication,v=g===void 0?!0:g,E=t.defaultOptions,h=t.defaultContext,m=t.assumeImmutableResults,p=m===void 0?a.assumeImmutableResults:m,S=t.resolvers,T=t.typeDefs,O=t.fragmentMatcher,w=t.name,k=t.version,I=t.link;I||(I=r?new Tk({uri:r,credentials:i,headers:o}):Wa.empty()),this.link=I,this.cache=a,this.disableNetworkFetches=u||f>0,this.queryDeduplication=v,this.defaultOptions=E||Object.create(null),this.typeDefs=T,f&&setTimeout(function(){return n.disableNetworkFetches=!1},f),this.watchQuery=this.watchQuery.bind(this),this.query=this.query.bind(this),this.mutate=this.mutate.bind(this),this.watchFragment=this.watchFragment.bind(this),this.resetStore=this.resetStore.bind(this),this.reFetchObservableQueries=this.reFetchObservableQueries.bind(this),this.version=Jd,this.localState=new C1({cache:a,client:this,resolvers:S,fragmentMatcher:O}),this.queryManager=new Yk({cache:this.cache,link:this.link,defaultOptions:this.defaultOptions,defaultContext:h,documentTransform:s,queryDeduplication:v,ssrMode:u,clientAwareness:{name:w,version:k},localState:this.localState,assumeImmutableResults:p,onBroadcast:y?function(){n.devToolsHookCb&&n.devToolsHookCb({action:{},state:{queries:n.queryManager.getQueryStore(),mutations:n.queryManager.mutationStore||{}},dataWithOptimisticResults:n.cache.extract(!0)})}:void 0}),y&&this.connectToDevTools()}return e.prototype.connectToDevTools=function(){if(typeof window=="object"){var t=window,n=Symbol.for("apollo.devtools");(t[n]=t[n]||[]).push(this),t.__APOLLO_CLIENT__=this}!mv&&globalThis.__DEV__!==!1&&(mv=!0,setTimeout(function(){if(typeof window<"u"&&window.document&&window.top===window.self&&!window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__){var r=window.navigator,i=r&&r.userAgent,o=void 0;typeof i=="string"&&(i.indexOf("Chrome/")>-1?o="https://chrome.google.com/webstore/detail/apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm":i.indexOf("Firefox/")>-1&&(o="https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/")),o&&globalThis.__DEV__!==!1&&X.log("Download the Apollo DevTools for a better development experience: %s",o)}},1e4))},Object.defineProperty(e.prototype,"documentTransform",{get:function(){return this.queryManager.documentTransform},enumerable:!1,configurable:!0}),e.prototype.stop=function(){this.queryManager.stop()},e.prototype.watchQuery=function(t){return this.defaultOptions.watchQuery&&(t=ti(this.defaultOptions.watchQuery,t)),this.disableNetworkFetches&&(t.fetchPolicy==="network-only"||t.fetchPolicy==="cache-and-network")&&(t=b(b({},t),{fetchPolicy:"cache-first"})),this.queryManager.watchQuery(t)},e.prototype.query=function(t){return this.defaultOptions.query&&(t=ti(this.defaultOptions.query,t)),X(t.fetchPolicy!=="cache-and-network",16),this.disableNetworkFetches&&t.fetchPolicy==="network-only"&&(t=b(b({},t),{fetchPolicy:"cache-first"})),this.queryManager.query(t)},e.prototype.mutate=function(t){return this.defaultOptions.mutate&&(t=ti(this.defaultOptions.mutate,t)),this.queryManager.mutate(t)},e.prototype.subscribe=function(t){return this.queryManager.startGraphQLSubscription(t)},e.prototype.readQuery=function(t,n){return n===void 0&&(n=!1),this.cache.readQuery(t,n)},e.prototype.watchFragment=function(t){return this.cache.watchFragment(t)},e.prototype.readFragment=function(t,n){return n===void 0&&(n=!1),this.cache.readFragment(t,n)},e.prototype.writeQuery=function(t){var n=this.cache.writeQuery(t);return t.broadcast!==!1&&this.queryManager.broadcastQueries(),n},e.prototype.writeFragment=function(t){var n=this.cache.writeFragment(t);return t.broadcast!==!1&&this.queryManager.broadcastQueries(),n},e.prototype.__actionHookForDevTools=function(t){this.devToolsHookCb=t},e.prototype.__requestRaw=function(t){return Nf(this.link,t)},e.prototype.resetStore=function(){var t=this;return Promise.resolve().then(function(){return t.queryManager.clearStore({discardWatches:!1})}).then(function(){return Promise.all(t.resetStoreCallbacks.map(function(n){return n()}))}).then(function(){return t.reFetchObservableQueries()})},e.prototype.clearStore=function(){var t=this;return Promise.resolve().then(function(){return t.queryManager.clearStore({discardWatches:!0})}).then(function(){return Promise.all(t.clearStoreCallbacks.map(function(n){return n()}))})},e.prototype.onResetStore=function(t){var n=this;return this.resetStoreCallbacks.push(t),function(){n.resetStoreCallbacks=n.resetStoreCallbacks.filter(function(r){return r!==t})}},e.prototype.onClearStore=function(t){var n=this;return this.clearStoreCallbacks.push(t),function(){n.clearStoreCallbacks=n.clearStoreCallbacks.filter(function(r){return r!==t})}},e.prototype.reFetchObservableQueries=function(t){return this.queryManager.reFetchObservableQueries(t)},e.prototype.refetchQueries=function(t){var n=this.queryManager.refetchQueries(t),r=[],i=[];n.forEach(function(a,s){r.push(s),i.push(a)});var o=Promise.all(i);return o.queries=r,o.results=i,o.catch(function(a){globalThis.__DEV__!==!1&&X.debug(17,a)}),o},e.prototype.getObservableQueries=function(t){return t===void 0&&(t="active"),this.queryManager.getObservableQueries(t)},e.prototype.extract=function(t){return this.cache.extract(t)},e.prototype.restore=function(t){return this.cache.restore(t)},e.prototype.addResolvers=function(t){this.localState.addResolvers(t)},e.prototype.setResolvers=function(t){this.localState.setResolvers(t)},e.prototype.getResolvers=function(){return this.localState.getResolvers()},e.prototype.setLocalStateFragmentMatcher=function(t){this.localState.setFragmentMatcher(t)},e.prototype.setLink=function(t){this.link=this.queryManager.link=t},Object.defineProperty(e.prototype,"defaultContext",{get:function(){return this.queryManager.defaultContext},enumerable:!1,configurable:!0}),e}();globalThis.__DEV__!==!1&&(O1.prototype.getMemoryInternals=Vx);var Ws=new Map,jf=new Map,N1=!0,bl=!1;function R1(e){return e.replace(/[\s,]+/g," ").trim()}function Xk(e){return R1(e.source.body.substring(e.start,e.end))}function Kk(e){var t=new Set,n=[];return e.definitions.forEach(function(r){if(r.kind==="FragmentDefinition"){var i=r.name.value,o=Xk(r.loc),a=jf.get(i);a&&!a.has(o)?N1&&console.warn("Warning: fragment with name "+i+` already exists.
graphql-tag enforces all fragment names across your application to be unique; read more about
this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names`):a||jf.set(i,a=new Set),a.add(o),t.has(o)||(t.add(o),n.push(r))}else n.push(r)}),b(b({},e),{definitions:n})}function Jk(e){var t=new Set(e.definitions);t.forEach(function(r){r.loc&&delete r.loc,Object.keys(r).forEach(function(i){var o=r[i];o&&typeof o=="object"&&t.add(o)})});var n=e.loc;return n&&(delete n.startToken,delete n.endToken),e}function Zk(e){var t=R1(e);if(!Ws.has(t)){var n=dx(e,{experimentalFragmentVariables:bl,allowLegacyFragmentVariables:bl});if(!n||n.kind!=="Document")throw new Error("Not a valid GraphQL document.");Ws.set(t,Jk(Kk(n)))}return Ws.get(t)}function On(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];typeof e=="string"&&(e=[e]);var r=e[0];return t.forEach(function(i,o){i&&i.kind==="Document"?r+=i.loc.source.body:r+=i,r+=e[o+1]}),Zk(r)}function eC(){Ws.clear(),jf.clear()}function tC(){N1=!1}function nC(){bl=!0}function rC(){bl=!1}var Lo={gql:On,resetCaches:eC,disableFragmentWarnings:tC,enableExperimentalFragmentVariables:nC,disableExperimentalFragmentVariables:rC};(function(e){e.gql=Lo.gql,e.resetCaches=Lo.resetCaches,e.disableFragmentWarnings=Lo.disableFragmentWarnings,e.enableExperimentalFragmentVariables=Lo.enableExperimentalFragmentVariables,e.disableExperimentalFragmentVariables=Lo.disableExperimentalFragmentVariables})(On||(On={}));On.default=On;var D1={exports:{}};(function(e){e.exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=void 0,e.exports.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=void 0,e.exports.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=void 0,Object.assign(e.exports,N)})(D1);var Ne=D1.exports;const iC=Ma(Ne),I1=Yf({__proto__:null,default:iC},[Ne]);var vv=ih?Symbol.for("__APOLLO_CONTEXT__"):"__APOLLO_CONTEXT__";function _h(){X("createContext"in I1,45);var e=Ne.createContext[vv];return e||(Object.defineProperty(Ne.createContext,vv,{value:e=Ne.createContext({}),enumerable:!1,writable:!1,configurable:!0}),e.displayName="ApolloContext"),e}var oC=function(e){var t=e.client,n=e.children,r=_h(),i=Ne.useContext(r),o=Ne.useMemo(function(){return b(b({},i),{client:t||i.client})},[i,t]);return X(o.client,46),Ne.createElement(r.Provider,{value:o},n)};function iu(e){var t=Ne.useContext(_h()),n=e||t.client;return X(!!n,49),n}var yv=!1,aC="useSyncExternalStore",sC=I1[aC],lC=sC||function(e,t,n){var r=t();globalThis.__DEV__!==!1&&!yv&&r!==t()&&(yv=!0,globalThis.__DEV__!==!1&&X.error(58));var i=Ne.useState({inst:{value:r,getSnapshot:t}}),o=i[0].inst,a=i[1];return Dx?Ne.useLayoutEffect(function(){Object.assign(o,{value:r,getSnapshot:t}),ac(o)&&a({inst:o})},[e,r,t]):Object.assign(o,{value:r,getSnapshot:t}),Ne.useEffect(function(){return ac(o)&&a({inst:o}),e(function(){ac(o)&&a({inst:o})})},[e]),r};function ac(e){var t=e.value,n=e.getSnapshot;try{return t!==n()}catch{return!0}}var bn;(function(e){e[e.Query=0]="Query",e[e.Mutation=1]="Mutation",e[e.Subscription=2]="Subscription"})(bn||(bn={}));var Jr;function gv(e){var t;switch(e){case bn.Query:t="Query";break;case bn.Mutation:t="Mutation";break;case bn.Subscription:t="Subscription";break}return t}function P1(e){Jr||(Jr=new oh(Cn.parser||1e3));var t=Jr.get(e);if(t)return t;var n,r,i;X(!!e&&!!e.kind,59,e);for(var o=[],a=[],s=[],l=[],u=0,c=e.definitions;u<c.length;u++){var f=c[u];if(f.kind==="FragmentDefinition"){o.push(f);continue}if(f.kind==="OperationDefinition")switch(f.operation){case"query":a.push(f);break;case"mutation":s.push(f);break;case"subscription":l.push(f);break}}X(!o.length||a.length||s.length||l.length,60),X(a.length+s.length+l.length<=1,61,e,a.length,l.length,s.length),r=a.length?bn.Query:bn.Mutation,!a.length&&!s.length&&(r=bn.Subscription);var d=a.length?a:s.length?s:l;X(d.length===1,62,e,d.length);var y=d[0];n=y.variableDefinitions||[],y.name&&y.name.kind==="Name"?i=y.name.value:i="data";var g={name:i,type:r,variables:n};return Jr.set(e,g),g}P1.resetCache=function(){Jr=void 0};globalThis.__DEV__!==!1&&ah("parser",function(){return Jr?Jr.size:0});function A1(e,t){var n=P1(e),r=gv(t),i=gv(n.type);X(n.type===t,63,r,r,i)}var uC=Symbol.for("apollo.hook.wrappers");function cC(e,t,n){var r=n.queryManager,i=r&&r[uC],o=i&&i[e];return o?o(t):t}var fC=Object.prototype.hasOwnProperty;function dC(e,t){return t===void 0&&(t=Object.create(null)),cC("useQuery",hC,iu(t&&t.client))(e,t)}function hC(e,t){return L1(iu(t.client),e).useQuery(t)}function L1(e,t){var n=Ne.useRef();(!n.current||e!==n.current.client||t!==n.current.query)&&(n.current=new pC(e,t,n.current));var r=n.current;return r.forceUpdateState=Ne.useReducer(function(i){return i+1},0)[1],r}var pC=function(){function e(t,n,r){var i=this;this.client=t,this.query=n,this.forceUpdate=function(){return i.forceUpdateState()},this.ssrDisabledResult=Da({loading:!0,data:void 0,error:void 0,networkStatus:me.loading}),this.skipStandbyResult=Da({loading:!1,data:void 0,error:void 0,networkStatus:me.ready}),this.toQueryResultCache=new(pi?WeakMap:Map),A1(n,bn.Query);var o=r&&r.result,a=o&&o.data;a&&(this.previousData=a)}return e.prototype.forceUpdateState=function(){globalThis.__DEV__!==!1&&X.warn(51)},e.prototype.executeQuery=function(t){var n=this,r;t.query&&Object.assign(this,{query:t.query}),this.watchQueryOptions=this.createWatchQueryOptions(this.queryHookOptions=t);var i=this.observable.reobserveAsConcast(this.getObsQueryOptions());return this.previousData=((r=this.result)===null||r===void 0?void 0:r.data)||this.previousData,this.result=void 0,this.forceUpdate(),new Promise(function(o){var a;i.subscribe({next:function(s){a=s},error:function(){o(n.toQueryResult(n.observable.getCurrentResult()))},complete:function(){o(n.toQueryResult(a))}})})},e.prototype.useQuery=function(t){var n=this;this.renderPromises=Ne.useContext(_h()).renderPromises,this.useOptions(t);var r=this.useObservableQuery(),i=lC(Ne.useCallback(function(o){if(n.renderPromises)return function(){};n.forceUpdate=o;var a=function(){var u=n.result,c=r.getCurrentResult();u&&u.loading===c.loading&&u.networkStatus===c.networkStatus&&we(u.data,c.data)||n.setResult(c)},s=function(u){if(l.unsubscribe(),l=r.resubscribeAfterError(a,s),!fC.call(u,"graphQLErrors"))throw u;var c=n.result;(!c||c&&c.loading||!we(u,c.error))&&n.setResult({data:c&&c.data,error:u,loading:!1,networkStatus:me.error})},l=r.subscribe(a,s);return function(){setTimeout(function(){return l.unsubscribe()}),n.forceUpdate=function(){return n.forceUpdateState()}}},[r,this.renderPromises,this.client.disableNetworkFetches]),function(){return n.getCurrentResult()},function(){return n.getCurrentResult()});return this.unsafeHandlePartialRefetch(i),this.toQueryResult(i)},e.prototype.useOptions=function(t){var n,r=this.createWatchQueryOptions(this.queryHookOptions=t),i=this.watchQueryOptions;we(r,i)||(this.watchQueryOptions=r,i&&this.observable&&(this.observable.reobserve(this.getObsQueryOptions()),this.previousData=((n=this.result)===null||n===void 0?void 0:n.data)||this.previousData,this.result=void 0)),this.onCompleted=t.onCompleted||e.prototype.onCompleted,this.onError=t.onError||e.prototype.onError,(this.renderPromises||this.client.disableNetworkFetches)&&this.queryHookOptions.ssr===!1&&!this.queryHookOptions.skip?this.result=this.ssrDisabledResult:this.queryHookOptions.skip||this.watchQueryOptions.fetchPolicy==="standby"?this.result=this.skipStandbyResult:(this.result===this.ssrDisabledResult||this.result===this.skipStandbyResult)&&(this.result=void 0)},e.prototype.getObsQueryOptions=function(){var t=[],n=this.client.defaultOptions.watchQuery;return n&&t.push(n),this.queryHookOptions.defaultOptions&&t.push(this.queryHookOptions.defaultOptions),t.push(ci(this.observable&&this.observable.options,this.watchQueryOptions)),t.reduce(ti)},e.prototype.createWatchQueryOptions=function(t){var n;t===void 0&&(t={});var r=t.skip;t.ssr,t.onCompleted,t.onError,t.defaultOptions;var i=kn(t,["skip","ssr","onCompleted","onError","defaultOptions"]),o=Object.assign(i,{query:this.query});if(this.renderPromises&&(o.fetchPolicy==="network-only"||o.fetchPolicy==="cache-and-network")&&(o.fetchPolicy="cache-first"),o.variables||(o.variables={}),r){var a=o.fetchPolicy,s=a===void 0?this.getDefaultFetchPolicy():a,l=o.initialFetchPolicy,u=l===void 0?s:l;Object.assign(o,{initialFetchPolicy:u,fetchPolicy:"standby"})}else o.fetchPolicy||(o.fetchPolicy=((n=this.observable)===null||n===void 0?void 0:n.options.initialFetchPolicy)||this.getDefaultFetchPolicy());return o},e.prototype.getDefaultFetchPolicy=function(){var t,n;return((t=this.queryHookOptions.defaultOptions)===null||t===void 0?void 0:t.fetchPolicy)||((n=this.client.defaultOptions.watchQuery)===null||n===void 0?void 0:n.fetchPolicy)||"cache-first"},e.prototype.onCompleted=function(t){},e.prototype.onError=function(t){},e.prototype.useObservableQuery=function(){var t=this.observable=this.renderPromises&&this.renderPromises.getSSRObservable(this.watchQueryOptions)||this.observable||this.client.watchQuery(this.getObsQueryOptions());this.obsQueryFields=Ne.useMemo(function(){return{refetch:t.refetch.bind(t),reobserve:t.reobserve.bind(t),fetchMore:t.fetchMore.bind(t),updateQuery:t.updateQuery.bind(t),startPolling:t.startPolling.bind(t),stopPolling:t.stopPolling.bind(t),subscribeToMore:t.subscribeToMore.bind(t)}},[t]);var n=!(this.queryHookOptions.ssr===!1||this.queryHookOptions.skip);return this.renderPromises&&n&&(this.renderPromises.registerSSRObservable(t),t.getCurrentResult().loading&&this.renderPromises.addObservableQueryPromise(t)),t},e.prototype.setResult=function(t){var n=this.result;n&&n.data&&(this.previousData=n.data),this.result=t,this.forceUpdate(),this.handleErrorOrCompleted(t,n)},e.prototype.handleErrorOrCompleted=function(t,n){var r=this;if(!t.loading){var i=this.toApolloError(t);Promise.resolve().then(function(){i?r.onError(i):t.data&&(n==null?void 0:n.networkStatus)!==t.networkStatus&&t.networkStatus===me.ready&&r.onCompleted(t.data)}).catch(function(o){globalThis.__DEV__!==!1&&X.warn(o)})}},e.prototype.toApolloError=function(t){return en(t.errors)?new jn({graphQLErrors:t.errors}):t.error},e.prototype.getCurrentResult=function(){return this.result||this.handleErrorOrCompleted(this.result=this.observable.getCurrentResult()),this.result},e.prototype.toQueryResult=function(t){var n=this.toQueryResultCache.get(t);if(n)return n;var r=t.data;t.partial;var i=kn(t,["data","partial"]);return this.toQueryResultCache.set(t,n=b(b(b({data:r},i),this.obsQueryFields),{client:this.client,observable:this.observable,variables:this.observable.variables,called:!this.queryHookOptions.skip,previousData:this.previousData})),!n.error&&en(t.errors)&&(n.error=new jn({graphQLErrors:t.errors})),n},e.prototype.unsafeHandlePartialRefetch=function(t){t.partial&&this.queryHookOptions.partialRefetch&&!t.loading&&(!t.data||Object.keys(t.data).length===0)&&this.observable.options.fetchPolicy!=="cache-only"&&(Object.assign(t,{loading:!0,networkStatus:me.refetch}),this.observable.refetch())},e}(),mC=["refetch","reobserve","fetchMore","updateQuery","startPolling","subscribeToMore"];function M1(e,t){var n,r=Ne.useRef(),i=Ne.useRef(),o=Ne.useRef(),a=ti(t,r.current||{}),s=(n=a==null?void 0:a.query)!==null&&n!==void 0?n:e;i.current=t,o.current=s;var l=L1(iu(t&&t.client),s),u=l.useQuery(b(b({},a),{skip:!r.current})),c=u.observable.options.initialFetchPolicy||l.getDefaultFetchPolicy(),f=Object.assign(u,{called:!!r.current}),d=Ne.useMemo(function(){for(var g={},v=function(p){var S=f[p];g[p]=function(){return r.current||(r.current=Object.create(null),l.forceUpdateState()),S.apply(this,arguments)}},E=0,h=mC;E<h.length;E++){var m=h[E];v(m)}return g},[]);Object.assign(f,d);var y=Ne.useCallback(function(g){r.current=g?b(b({},g),{fetchPolicy:g.fetchPolicy||c}):{fetchPolicy:c};var v=ti(i.current,b({query:o.current},r.current)),E=l.executeQuery(b(b({},v),{skip:!1})).then(function(h){return Object.assign(h,d)});return E.catch(function(){}),E},[]);return[y,f]}function vC(e,t){var n=iu(void 0);A1(e,bn.Mutation);var r=Ne.useState({called:!1,loading:!1,client:n}),i=r[0],o=r[1],a=Ne.useRef({result:i,mutationId:0,isMounted:!0,client:n,mutation:e,options:t});Object.assign(a.current,{client:n,options:t,mutation:e});var s=Ne.useCallback(function(u){u===void 0&&(u={});var c=a.current,f=c.options,d=c.mutation,y=b(b({},f),{mutation:d}),g=u.client||a.current.client;!a.current.result.loading&&!y.ignoreResults&&a.current.isMounted&&o(a.current.result={loading:!0,error:void 0,data:void 0,called:!0,client:g});var v=++a.current.mutationId,E=ti(y,u);return g.mutate(E).then(function(h){var m,p,S=h.data,T=h.errors,O=T&&T.length>0?new jn({graphQLErrors:T}):void 0,w=u.onError||((m=a.current.options)===null||m===void 0?void 0:m.onError);if(O&&w&&w(O,E),v===a.current.mutationId&&!E.ignoreResults){var k={called:!0,loading:!1,data:S,error:O,client:g};a.current.isMounted&&!we(a.current.result,k)&&o(a.current.result=k)}var I=u.onCompleted||((p=a.current.options)===null||p===void 0?void 0:p.onCompleted);return O||I==null||I(h.data,E),h}).catch(function(h){var m;if(v===a.current.mutationId&&a.current.isMounted){var p={loading:!1,error:h,data:void 0,called:!0,client:g};we(a.current.result,p)||o(a.current.result=p)}var S=u.onError||((m=a.current.options)===null||m===void 0?void 0:m.onError);if(S)return S(h,E),{data:void 0,errors:h};throw h})},[]),l=Ne.useCallback(function(){if(a.current.isMounted){var u={called:!1,loading:!1,client:n};Object.assign(a.current,{mutationId:0,result:u}),o(u)}},[]);return Ne.useEffect(function(){return a.current.isMounted=!0,function(){a.current.isMounted=!1}},[]),[s,b({reset:l},i)]}class yC extends N.Component{render(){return R.jsxs("svg",{width:"41",height:"41",viewBox:"0 0 41 41",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[R.jsxs("g",{clipPath:"url(#clip0_150_1230)",children:[R.jsx("path",{d:"M34.0222 28.6646C34.0494 28.983 33.8009 29.2566 33.4846 29.2566H7.46924C7.15373 29.2566 6.90553 28.9843 6.93156 28.6665L8.7959 5.91227C8.8191 5.62962 9.05287 5.41211 9.33372 5.41211H31.5426C31.8226 5.41211 32.0561 5.62853 32.0801 5.91036L34.0222 28.6646Z",fill:"#1DCF65"}),R.jsx("path",{d:"M36.0988 34.6014C36.1313 34.9985 35.8211 35.339 35.4268 35.339H5.59438C5.2009 35.339 4.89092 35.0002 4.92208 34.6037L7.06376 7.34718C7.09168 6.9927 7.38426 6.71973 7.73606 6.71973H33.1958C33.5468 6.71973 33.8391 6.99161 33.868 7.34499L36.0988 34.6014Z",fill:"url(#paint0_linear_150_1230)"}),R.jsx("path",{d:"M19.9232 26.6953C16.0402 26.6953 12.8813 22.8631 12.8813 18.1528C12.8813 17.9075 13.0782 17.7085 13.3211 17.7085C13.564 17.7085 13.7608 17.9073 13.7608 18.1528C13.7608 22.3732 16.5253 25.8067 19.9234 25.8067C23.3214 25.8067 26.0859 22.3732 26.0859 18.1528C26.0859 17.9075 26.2827 17.7085 26.5257 17.7085C26.7686 17.7085 26.9654 17.9073 26.9654 18.1528C26.9653 22.8631 23.8062 26.6953 19.9232 26.6953Z",fill:"white"}),R.jsx("path",{d:"M24.2581 18.0337C24.1456 18.0337 24.0331 17.9904 23.9471 17.9036C23.7754 17.7301 23.7754 17.4488 23.9471 17.2753L26.226 14.9729C26.3084 14.8897 26.4203 14.8428 26.5369 14.8428C26.6536 14.8428 26.7654 14.8895 26.8479 14.9729L29.1045 17.2529C29.2762 17.4264 29.2762 17.7077 29.1045 17.8812C28.9327 18.0546 28.6543 18.0547 28.4826 17.8812L26.5368 15.9155L24.569 17.9036C24.4831 17.9904 24.3706 18.0337 24.2581 18.0337Z",fill:"white"})]}),R.jsxs("defs",{children:[R.jsxs("linearGradient",{id:"paint0_linear_150_1230",x1:"29.8733",y1:"31.3337",x2:"11.5132",y2:"9.9008",gradientUnits:"userSpaceOnUse",children:[R.jsx("stop",{stopColor:"#52D67A"}),R.jsx("stop",{offset:"1",stopColor:"#5AEE87"})]}),R.jsx("clipPath",{id:"clip0_150_1230",children:R.jsx("rect",{width:"31.16",height:"30.176",fill:"white",transform:"translate(4.91992 5.41211)"})})]})]})}}var F1={exports:{}},gC="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",EC=gC,wC=EC;function j1(){}function z1(){}z1.resetWarningCache=j1;var SC=function(){function e(r,i,o,a,s,l){if(l!==wC){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:z1,resetWarningCache:j1};return n.PropTypes=n,n};F1.exports=SC();var _C=F1.exports;const ne=Ma(_C);class Th extends N.Component{render(){const{className:t,color:n="hsl(216, 8%, 12%)",size:r=24}=this.props;return R.jsxs("svg",{width:r,height:r,viewBox:"0 0 24 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[R.jsx("path",{d:"M23.4736 4.8484C23.0186 4.29247 22.3109 3.95457 21.5785 3.95457H6.19066L5.71097 2.16691C5.43262 1.12772 4.47323 0.402832 3.36082 0.402832H0.783719C0.354361 0.402832 0 0.740725 0 1.15227C0 1.56284 0.353351 1.9017 0.783719 1.9017H3.36082C3.73985 1.9017 4.06854 2.14333 4.1692 2.50577L7.25167 14.2494C7.53003 15.2886 8.48941 16.0135 9.60182 16.0135H19.6833C20.7947 16.0135 21.7808 15.2886 22.0335 14.2494L23.9286 6.80699C24.1053 6.1293 23.9543 5.40442 23.4736 4.84848L23.4736 4.8484ZM22.3879 6.46712L20.4928 13.9095C20.3921 14.272 20.0634 14.5136 19.6844 14.5136H9.60185C9.22282 14.5136 8.89413 14.272 8.79347 13.9095L6.59533 5.47717H21.5796C21.8323 5.47717 22.085 5.59798 22.237 5.79148C22.388 5.98403 22.463 6.22566 22.388 6.46729L22.3879 6.46712Z",fill:n}),R.jsx("path",{d:"M10.1332 16.9778C8.69316 16.9778 7.50586 18.1132 7.50586 19.4902C7.50586 20.8672 8.69326 22.0027 10.1332 22.0027C11.5733 22.0036 12.7606 20.8682 12.7606 19.491C12.7606 18.1137 11.5732 16.9775 10.1332 16.9775V16.9778ZM10.1332 20.4814C9.55188 20.4814 9.09685 20.0463 9.09685 19.4903C9.09685 18.9344 9.55188 18.4993 10.1332 18.4993C10.7146 18.4993 11.1696 18.9344 11.1696 19.4903C11.1687 20.0227 10.689 20.4814 10.1332 20.4814Z",fill:n}),R.jsx("path",{d:"M18.8251 16.978C17.3851 16.978 16.1978 18.1135 16.1978 19.4905C16.1978 20.8675 17.3852 22.0029 18.8251 22.0029C20.2651 22.0029 21.4525 20.8675 21.4525 19.4905C21.4279 18.1143 20.2651 16.978 18.8251 16.978ZM18.8251 20.4816C18.2438 20.4816 17.7887 20.0465 17.7887 19.4906C17.7887 18.9346 18.2438 18.4995 18.8251 18.4995C19.4065 18.4995 19.8615 18.9346 19.8615 19.4906C19.8615 20.0229 19.3809 20.4816 18.8251 20.4816Z",fill:n})]})}}Th.propTypes={className:ne.string,color:ne.string,size:ne.number};class zf extends N.Component{render(){const{direction:t="right"}=this.props,n={up:"-rotate-90",down:"rotate-90",left:"rotate-180",right:"rotate-0"};return R.jsx("svg",{className:`w-6 h-6 ${n[t]}`,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:R.jsx("path",{fillRule:"evenodd",d:"M7.707 15.707a1 1 0 01-1.414-1.414L10.586 10 6.293 5.707a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5z",clipRule:"evenodd"})})}}zf.propTypes={direction:ne.oneOf(["up","down","left","right"])};class xh extends N.Component{render(){const{className:t}=this.props;return R.jsxs("svg",{"aria-hidden":"true",className:`w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-primary${t?" "+t:""}`,viewBox:"0 0 100 101",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[R.jsx("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),R.jsx("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]})}}xh.propTypes={className:ne.string};function U1(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=U1(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function vr(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=U1(e))&&(r&&(r+=" "),r+=t);return r}const Aa=e=>typeof e=="number"&&!isNaN(e),ni=e=>typeof e=="string",zt=e=>typeof e=="function",Gs=e=>ni(e)||zt(e)?e:null,Uf=e=>N.isValidElement(e)||ni(e)||zt(e)||Aa(e);function TC(e,t,n){n===void 0&&(n=300);const{scrollHeight:r,style:i}=e;requestAnimationFrame(()=>{i.minHeight="initial",i.height=r+"px",i.transition=`all ${n}ms`,requestAnimationFrame(()=>{i.height="0",i.padding="0",i.margin="0",setTimeout(t,n)})})}function ou(e){let{enter:t,exit:n,appendPosition:r=!1,collapse:i=!0,collapseDuration:o=300}=e;return function(a){let{children:s,position:l,preventExitTransition:u,done:c,nodeRef:f,isIn:d,playToast:y}=a;const g=r?`${t}--${l}`:t,v=r?`${n}--${l}`:n,E=N.useRef(0);return N.useLayoutEffect(()=>{const h=f.current,m=g.split(" "),p=S=>{S.target===f.current&&(y(),h.removeEventListener("animationend",p),h.removeEventListener("animationcancel",p),E.current===0&&S.type!=="animationcancel"&&h.classList.remove(...m))};h.classList.add(...m),h.addEventListener("animationend",p),h.addEventListener("animationcancel",p)},[]),N.useEffect(()=>{const h=f.current,m=()=>{h.removeEventListener("animationend",m),i?TC(h,c,o):c()};d||(u?m():(E.current=1,h.className+=` ${v}`,h.addEventListener("animationend",m)))},[d]),xe.createElement(xe.Fragment,null,s)}}function Ev(e,t){return e!=null?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}const wt=new Map;let La=[];const Vf=new Set,xC=e=>Vf.forEach(t=>t(e)),V1=()=>wt.size>0;function B1(e,t){var n;if(t)return!((n=wt.get(t))==null||!n.isToastActive(e));let r=!1;return wt.forEach(i=>{i.isToastActive(e)&&(r=!0)}),r}function Q1(e,t){Uf(e)&&(V1()||La.push({content:e,options:t}),wt.forEach(n=>{n.buildToast(e,t)}))}function wv(e,t){wt.forEach(n=>{t!=null&&t!=null&&t.containerId?(t==null?void 0:t.containerId)===n.id&&n.toggle(e,t==null?void 0:t.id):n.toggle(e,t==null?void 0:t.id)})}function bC(e){const{subscribe:t,getSnapshot:n,setProps:r}=N.useRef(function(o){const a=o.containerId||1;return{subscribe(s){const l=function(c,f,d){let y=1,g=0,v=[],E=[],h=[],m=f;const p=new Map,S=new Set,T=()=>{h=Array.from(p.values()),S.forEach(k=>k())},O=k=>{E=k==null?[]:E.filter(I=>I!==k),T()},w=k=>{const{toastId:I,onOpen:P,updateId:K,children:se}=k.props,ee=K==null;k.staleId&&p.delete(k.staleId),p.set(I,k),E=[...E,k.props.toastId].filter(le=>le!==k.staleId),T(),d(Ev(k,ee?"added":"updated")),ee&&zt(P)&&P(N.isValidElement(se)&&se.props)};return{id:c,props:m,observe:k=>(S.add(k),()=>S.delete(k)),toggle:(k,I)=>{p.forEach(P=>{I!=null&&I!==P.props.toastId||zt(P.toggle)&&P.toggle(k)})},removeToast:O,toasts:p,clearQueue:()=>{g-=v.length,v=[]},buildToast:(k,I)=>{if((q=>{let{containerId:Ee,toastId:te,updateId:Se}=q;const Ce=Ee?Ee!==c:c!==1,qe=p.has(te)&&Se==null;return Ce||qe})(I))return;const{toastId:P,updateId:K,data:se,staleId:ee,delay:le}=I,Ae=()=>{O(P)},rt=K==null;rt&&g++;const Fe={...m,style:m.toastStyle,key:y++,...Object.fromEntries(Object.entries(I).filter(q=>{let[Ee,te]=q;return te!=null})),toastId:P,updateId:K,data:se,closeToast:Ae,isIn:!1,className:Gs(I.className||m.toastClassName),bodyClassName:Gs(I.bodyClassName||m.bodyClassName),progressClassName:Gs(I.progressClassName||m.progressClassName),autoClose:!I.isLoading&&(M=I.autoClose,V=m.autoClose,M===!1||Aa(M)&&M>0?M:V),deleteToast(){const q=p.get(P),{onClose:Ee,children:te}=q.props;zt(Ee)&&Ee(N.isValidElement(te)&&te.props),d(Ev(q,"removed")),p.delete(P),g--,g<0&&(g=0),v.length>0?w(v.shift()):T()}};var M,V;Fe.closeButton=m.closeButton,I.closeButton===!1||Uf(I.closeButton)?Fe.closeButton=I.closeButton:I.closeButton===!0&&(Fe.closeButton=!Uf(m.closeButton)||m.closeButton);let U=k;N.isValidElement(k)&&!ni(k.type)?U=N.cloneElement(k,{closeToast:Ae,toastProps:Fe,data:se}):zt(k)&&(U=k({closeToast:Ae,toastProps:Fe,data:se}));const ae={content:U,props:Fe,staleId:ee};m.limit&&m.limit>0&&g>m.limit&&rt?v.push(ae):Aa(le)?setTimeout(()=>{w(ae)},le):w(ae)},setProps(k){m=k},setToggle:(k,I)=>{p.get(k).toggle=I},isToastActive:k=>E.some(I=>I===k),getSnapshot:()=>m.newestOnTop?h.reverse():h}}(a,o,xC);wt.set(a,l);const u=l.observe(s);return La.forEach(c=>Q1(c.content,c.options)),La=[],()=>{u(),wt.delete(a)}},setProps(s){var l;(l=wt.get(a))==null||l.setProps(s)},getSnapshot(){var s;return(s=wt.get(a))==null?void 0:s.getSnapshot()}}}(e)).current;r(e);const i=N.useSyncExternalStore(t,n,n);return{getToastToRender:function(o){if(!i)return[];const a=new Map;return i.forEach(s=>{const{position:l}=s.props;a.has(l)||a.set(l,[]),a.get(l).push(s)}),Array.from(a,s=>o(s[0],s[1]))},isToastActive:B1,count:i==null?void 0:i.length}}function kC(e){const[t,n]=N.useState(!1),[r,i]=N.useState(!1),o=N.useRef(null),a=N.useRef({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:s,pauseOnHover:l,closeToast:u,onClick:c,closeOnClick:f}=e;var d,y;function g(){n(!0)}function v(){n(!1)}function E(p){const S=o.current;a.canDrag&&S&&(a.didMove=!0,t&&v(),a.delta=e.draggableDirection==="x"?p.clientX-a.start:p.clientY-a.start,a.start!==p.clientX&&(a.canCloseOnClick=!1),S.style.transform=`translate3d(${e.draggableDirection==="x"?`${a.delta}px, var(--y)`:`0, calc(${a.delta}px + var(--y))`},0)`,S.style.opacity=""+(1-Math.abs(a.delta/a.removalDistance)))}function h(){document.removeEventListener("pointermove",E),document.removeEventListener("pointerup",h);const p=o.current;if(a.canDrag&&a.didMove&&p){if(a.canDrag=!1,Math.abs(a.delta)>a.removalDistance)return i(!0),e.closeToast(),void e.collapseAll();p.style.transition="transform 0.2s, opacity 0.2s",p.style.removeProperty("transform"),p.style.removeProperty("opacity")}}(y=wt.get((d={id:e.toastId,containerId:e.containerId,fn:n}).containerId||1))==null||y.setToggle(d.id,d.fn),N.useEffect(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||v(),window.addEventListener("focus",g),window.addEventListener("blur",v),()=>{window.removeEventListener("focus",g),window.removeEventListener("blur",v)}},[e.pauseOnFocusLoss]);const m={onPointerDown:function(p){if(e.draggable===!0||e.draggable===p.pointerType){a.didMove=!1,document.addEventListener("pointermove",E),document.addEventListener("pointerup",h);const S=o.current;a.canCloseOnClick=!0,a.canDrag=!0,S.style.transition="none",e.draggableDirection==="x"?(a.start=p.clientX,a.removalDistance=S.offsetWidth*(e.draggablePercent/100)):(a.start=p.clientY,a.removalDistance=S.offsetHeight*(e.draggablePercent===80?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(p){const{top:S,bottom:T,left:O,right:w}=o.current.getBoundingClientRect();p.nativeEvent.type!=="touchend"&&e.pauseOnHover&&p.clientX>=O&&p.clientX<=w&&p.clientY>=S&&p.clientY<=T?v():g()}};return s&&l&&(m.onMouseEnter=v,e.stacked||(m.onMouseLeave=g)),f&&(m.onClick=p=>{c&&c(p),a.canCloseOnClick&&u()}),{playToast:g,pauseToast:v,isRunning:t,preventExitTransition:r,toastRef:o,eventHandlers:m}}function CC(e){let{delay:t,isRunning:n,closeToast:r,type:i="default",hide:o,className:a,style:s,controlledProgress:l,progress:u,rtl:c,isIn:f,theme:d}=e;const y=o||l&&u===0,g={...s,animationDuration:`${t}ms`,animationPlayState:n?"running":"paused"};l&&(g.transform=`scaleX(${u})`);const v=vr("Toastify__progress-bar",l?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${d}`,`Toastify__progress-bar--${i}`,{"Toastify__progress-bar--rtl":c}),E=zt(a)?a({rtl:c,type:i,defaultClassName:v}):vr(v,a),h={[l&&u>=1?"onTransitionEnd":"onAnimationEnd"]:l&&u<1?null:()=>{f&&r()}};return xe.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":y},xe.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${d} Toastify__progress-bar--${i}`}),xe.createElement("div",{role:"progressbar","aria-hidden":y?"true":"false","aria-label":"notification timer",className:E,style:g,...h}))}let OC=1;const $1=()=>""+OC++;function NC(e){return e&&(ni(e.toastId)||Aa(e.toastId))?e.toastId:$1()}function oa(e,t){return Q1(e,t),t.toastId}function kl(e,t){return{...t,type:t&&t.type||e,toastId:NC(t)}}function ws(e){return(t,n)=>oa(t,kl(e,n))}function pe(e,t){return oa(e,kl("default",t))}pe.loading=(e,t)=>oa(e,kl("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),pe.promise=function(e,t,n){let r,{pending:i,error:o,success:a}=t;i&&(r=ni(i)?pe.loading(i,n):pe.loading(i.render,{...n,...i}));const s={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},l=(c,f,d)=>{if(f==null)return void pe.dismiss(r);const y={type:c,...s,...n,data:d},g=ni(f)?{render:f}:f;return r?pe.update(r,{...y,...g}):pe(g.render,{...y,...g}),d},u=zt(e)?e():e;return u.then(c=>l("success",a,c)).catch(c=>l("error",o,c)),u},pe.success=ws("success"),pe.info=ws("info"),pe.error=ws("error"),pe.warning=ws("warning"),pe.warn=pe.warning,pe.dark=(e,t)=>oa(e,kl("default",{theme:"dark",...t})),pe.dismiss=function(e){(function(t){var n;if(V1()){if(t==null||ni(n=t)||Aa(n))wt.forEach(r=>{r.removeToast(t)});else if(t&&("containerId"in t||"id"in t)){const r=wt.get(t.containerId);r?r.removeToast(t.id):wt.forEach(i=>{i.removeToast(t.id)})}}else La=La.filter(r=>t!=null&&r.options.toastId!==t)})(e)},pe.clearWaitingQueue=function(e){e===void 0&&(e={}),wt.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},pe.isActive=B1,pe.update=function(e,t){t===void 0&&(t={});const n=((r,i)=>{var o;let{containerId:a}=i;return(o=wt.get(a||1))==null?void 0:o.toasts.get(r)})(e,t);if(n){const{props:r,content:i}=n,o={delay:100,...r,...t,toastId:t.toastId||e,updateId:$1()};o.toastId!==e&&(o.staleId=e);const a=o.render||i;delete o.render,oa(a,o)}},pe.done=e=>{pe.update(e,{progress:1})},pe.onChange=function(e){return Vf.add(e),()=>{Vf.delete(e)}},pe.play=e=>wv(!0,e),pe.pause=e=>wv(!1,e);const RC=typeof window<"u"?N.useLayoutEffect:N.useEffect,Ss=e=>{let{theme:t,type:n,isLoading:r,...i}=e;return xe.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:t==="colored"?"currentColor":`var(--toastify-icon-color-${n})`,...i})},sc={info:function(e){return xe.createElement(Ss,{...e},xe.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return xe.createElement(Ss,{...e},xe.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return xe.createElement(Ss,{...e},xe.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return xe.createElement(Ss,{...e},xe.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return xe.createElement("div",{className:"Toastify__spinner"})}},DC=e=>{const{isRunning:t,preventExitTransition:n,toastRef:r,eventHandlers:i,playToast:o}=kC(e),{closeButton:a,children:s,autoClose:l,onClick:u,type:c,hideProgressBar:f,closeToast:d,transition:y,position:g,className:v,style:E,bodyClassName:h,bodyStyle:m,progressClassName:p,progressStyle:S,updateId:T,role:O,progress:w,rtl:k,toastId:I,deleteToast:P,isIn:K,isLoading:se,closeOnClick:ee,theme:le}=e,Ae=vr("Toastify__toast",`Toastify__toast-theme--${le}`,`Toastify__toast--${c}`,{"Toastify__toast--rtl":k},{"Toastify__toast--close-on-click":ee}),rt=zt(v)?v({rtl:k,position:g,type:c,defaultClassName:Ae}):vr(Ae,v),Fe=function(ae){let{theme:q,type:Ee,isLoading:te,icon:Se}=ae,Ce=null;const qe={theme:q,type:Ee};return Se===!1||(zt(Se)?Ce=Se({...qe,isLoading:te}):N.isValidElement(Se)?Ce=N.cloneElement(Se,qe):te?Ce=sc.spinner():(Rn=>Rn in sc)(Ee)&&(Ce=sc[Ee](qe))),Ce}(e),M=!!w||!l,V={closeToast:d,type:c,theme:le};let U=null;return a===!1||(U=zt(a)?a(V):N.isValidElement(a)?N.cloneElement(a,V):function(ae){let{closeToast:q,theme:Ee,ariaLabel:te="close"}=ae;return xe.createElement("button",{className:`Toastify__close-button Toastify__close-button--${Ee}`,type:"button",onClick:Se=>{Se.stopPropagation(),q(Se)},"aria-label":te},xe.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},xe.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(V)),xe.createElement(y,{isIn:K,done:P,position:g,preventExitTransition:n,nodeRef:r,playToast:o},xe.createElement("div",{id:I,onClick:u,"data-in":K,className:rt,...i,style:E,ref:r},xe.createElement("div",{...K&&{role:O},className:zt(h)?h({type:c}):vr("Toastify__toast-body",h),style:m},Fe!=null&&xe.createElement("div",{className:vr("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!se})},Fe),xe.createElement("div",null,s)),U,xe.createElement(CC,{...T&&!M?{key:`pb-${T}`}:{},rtl:k,theme:le,delay:l,isRunning:t,isIn:K,closeToast:d,hide:f,type:c,style:S,className:p,controlledProgress:M,progress:w||0})))},au=function(e,t){return t===void 0&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},IC=ou(au("bounce",!0)),PC=ou(au("slide",!0));ou(au("zoom"));ou(au("flip"));const AC={position:"top-right",transition:IC,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function LC(e){let t={...AC,...e};const n=e.stacked,[r,i]=N.useState(!0),o=N.useRef(null),{getToastToRender:a,isToastActive:s,count:l}=bC(t),{className:u,style:c,rtl:f,containerId:d}=t;function y(v){const E=vr("Toastify__toast-container",`Toastify__toast-container--${v}`,{"Toastify__toast-container--rtl":f});return zt(u)?u({position:v,rtl:f,defaultClassName:E}):vr(E,Gs(u))}function g(){n&&(i(!0),pe.play())}return RC(()=>{if(n){var v;const E=o.current.querySelectorAll('[data-in="true"]'),h=12,m=(v=t.position)==null?void 0:v.includes("top");let p=0,S=0;Array.from(E).reverse().forEach((T,O)=>{const w=T;w.classList.add("Toastify__toast--stacked"),O>0&&(w.dataset.collapsed=`${r}`),w.dataset.pos||(w.dataset.pos=m?"top":"bot");const k=p*(r?.2:1)+(r?0:h*O);w.style.setProperty("--y",`${m?k:-1*k}px`),w.style.setProperty("--g",`${h}`),w.style.setProperty("--s",""+(1-(r?S:0))),p+=w.offsetHeight,S+=.025})}},[r,l,n]),xe.createElement("div",{ref:o,className:"Toastify",id:d,onMouseEnter:()=>{n&&(i(!1),pe.pause())},onMouseLeave:g},a((v,E)=>{const h=E.length?{...c}:{...c,pointerEvents:"none"};return xe.createElement("div",{className:y(v),style:h,key:`container-${v}`},E.map(m=>{let{content:p,props:S}=m;return xe.createElement(DC,{...S,stacked:n,collapseAll:g,isIn:s(S.toastId,S.containerId),style:S.style,key:`toast-${S.key}`},p)}))}))}const q1=N.createContext(),H1=({children:e})=>{const[t,n]=N.useState([]),[r,i]=N.useState([]),[o,a]=N.useState(JSON.parse(localStorage.getItem("cartItems"))||[]),[s,l]=N.useState(null),u=(y={},g=!1,v=[])=>{var p;let E;if(g){if(y.attributes.filter(T=>!v.some(O=>O.attributeId===T.name)).length>0)return pe.error("Please select all attributes! ⚠️");E=v.map(T=>({id:T.id,attributeId:T.attributeId,value:T.value}))}else E=(p=y.attributes)==null?void 0:p.map(S=>({id:S.items[0].id,attributeId:S.items[0].attribute_id,value:S.items[0].value}));const h=[...o],m=h.findIndex(S=>S.product.id===y.id&&JSON.stringify(S.selectedAttributes)===JSON.stringify(E));if(m!==-1)h[m].quantity+=1;else{const S={id:new Date().valueOf(),product:y,selectedAttributes:E,quantity:1};h.unshift(S)}a(h),localStorage.setItem("cartItems",JSON.stringify(h)),pe.success("Item added to cart! 🛒")},c=(y,g,v)=>{const E=o.findIndex(p=>p.product.id===y.id&&JSON.stringify(p.selectedAttributes)===JSON.stringify(g));if(E===-1)return;const h=o.findIndex(p=>p.product.id===y.id&&JSON.stringify(p.selectedAttributes)===JSON.stringify(v)),m=[...o];if(h===-1){const p={...o[E],selectedAttributes:v};m[E]=p,pe.success("Cart item updated successfully!")}else E!==h&&(m[E].quantity+=m[h].quantity,m.splice(h,1),pe.success("Cart item quantities merged successfully!"));a(m),localStorage.setItem("cartItems",JSON.stringify(m))},f=(y,g)=>{const v=JSON.parse(localStorage.getItem("cartItems"))||[],E=v.findIndex(h=>h.id===y);E!==-1&&(v[E].quantity+=g,v[E].quantity<=0&&v.splice(E,1),localStorage.setItem("cartItems",JSON.stringify(v)),a(v),pe.success("Cart item updated successfully!"))},d=()=>{localStorage.removeItem("cartItems"),a([])};return R.jsx(q1.Provider,{value:{categoriesData:t,setCategoriesData:n,productsData:r,setProductsData:i,selectedCategory:s,setSelectedCategory:l,addToCart:u,cartItems:o,updateCartItemQuantity:f,updateCartItemAttribute:c,emptyCart:d},children:e})},Fr=()=>N.useContext(q1);H1.propTypes={children:ne.node.isRequired};On`
  query {
    categories {
      name
    }
  }
`;const bh=`
  id
  name
  inStock
  gallery
  description
  brand
  prices {
    amount
    currency {
      label
      symbol
    }
  }
  category
  attributes {
    id
    name
    type
    items {
      id
      attribute_id
      value
      displayValue
    }
  }
`,MC=On`
  query ($category: String) {
    products(category: $category) {
      ${bh}
    }
  }
`,FC=On`
  query ($id: String!) {
    product(id: $id) {
      ${bh}
    }
  }
`,jC=On`
  query ($category: String) {
    categories {
      name
    }
    products(category: $category) {
      ${bh}
    }
  }
`,zC=()=>{const{category:e}=p0(),{cartItems:t,setSelectedCategory:n,setProductsData:r,categoriesData:i}=Fr(),[o,a]=N.useState(!1),[s,l]=N.useState([]),u=()=>a(d=>!d),[c]=M1(MC,{onCompleted:d=>r(d.products)}),f=d=>{c({variables:{category:d}}),n(d)};return N.useEffect(()=>{document.body.style.overflowY=o?"hidden":"auto"},[o]),N.useEffect(()=>{var d;l(i.map(y=>y.name)),n(e??((d=i[0])==null?void 0:d.name))},[i]),R.jsxs("header",{className:"relative z-10 flex items-center justify-between",children:[R.jsx(W1,{categories:s,handleCategoryChange:f}),R.jsx("div",{className:"absolute inset-x-0 flex items-center justify-center mx-auto",children:R.jsx(Hl,{to:"/",onClick:()=>{var d;return f((d=i[0])==null?void 0:d.name)},children:R.jsx(yC,{})})}),R.jsxs("button",{className:"relative z-10 cursor-pointer",onClick:u,"data-testid":"cart-btn",children:[R.jsx(Th,{}),t.length>0&&R.jsx("div",{className:"absolute flex items-center justify-center w-5 h-5 -mt-1 -mr-1 text-sm text-white rounded-full -top-1 -right-2 bg-text","data-testid":"cart-item-amount",children:t.reduce((d,y)=>d+y.quantity,0)})]}),o&&R.jsxs(R.Fragment,{children:[R.jsx("div",{className:"absolute inset-x-0 z-50 h-screen bg-black opacity-25 top-full -right-20 -left-20",onClick:u,"data-testid":"cart-overlay"}),R.jsx(TE,{cartItems:t})]})]})};function W1({categories:e,handleCategoryChange:t}){const{selectedCategory:n}=Fr();return R.jsx("nav",{className:"z-10",children:R.jsx("ul",{className:"flex gap-6 uppercase",children:e.map(r=>{const i=r===n;return R.jsx("li",{children:R.jsx(Hl,{to:`/${r}`,className:`block pb-4 border-b-2 ${i?"nav-active":"border-transparent hover:text-primary"}`,"data-testid":i?"active-category-link":"category-link",onClick:()=>t(r),children:r})},r)})})})}W1.propTypes={categories:ne.array,handleCategoryChange:ne.func};const UC="/assets/searching-BVSosa7o.svg";function G1({product:e={}}){var n,r,i;const{addToCart:t}=Fr();return R.jsx("article",{children:R.jsxs("div",{className:"p-4 overflow-hidden transition-shadow duration-500 hover:shadow-xl group",children:[R.jsxs("div",{className:"relative mb-6",children:[R.jsx(Hl,{to:`/products/${e.id}`,"data-testid":`product-${e.name.replace(/\s+/g,"-").toLowerCase()}`,children:R.jsxs("div",{className:"relative",children:[R.jsx("img",{src:e.gallery[0]??UC,alt:e.name,loading:"lazy",className:"object-contain w-full min-h-64 max-h-96"}),!e.inStock&&R.jsx("div",{className:"absolute inset-0 flex items-center justify-center px-2 py-1 text-2xl uppercase bg-white bg-opacity-70 text-muted",children:"Out of Stock"})]})}),e.inStock&&R.jsx("button",{onClick:()=>t(e),className:"absolute bottom-0 p-2 transition-opacity duration-300 transform translate-y-1/2 rounded-full opacity-0 cta group-hover:opacity-100 right-4",children:R.jsx(Th,{color:"white",className:"w-5 h-5"})})]}),R.jsx("h3",{className:"text-lg font-light capitalize",children:e.name}),R.jsxs("div",{className:`${e.inStock?"":"text-muted "}text-lg`,children:[(r=(n=e.prices[0])==null?void 0:n.currency)==null?void 0:r.symbol," ",(i=e.prices[0])==null?void 0:i.amount]})]})})}G1.propTypes={product:ne.shape({id:ne.string.isRequired,name:ne.string.isRequired,inStock:ne.bool.isRequired,gallery:ne.arrayOf(ne.string),description:ne.string.isRequired,brand:ne.string.isRequired,prices:ne.arrayOf(ne.shape({amount:ne.string.isRequired,currency:ne.shape({label:ne.string.isRequired,symbol:ne.string.isRequired}).isRequired})).isRequired,category:ne.string.isRequired,attributes:ne.arrayOf(ne.shape({id:ne.string.isRequired,name:ne.string.isRequired,type:ne.string.isRequired,items:ne.arrayOf(ne.shape({id:ne.string.isRequired,value:ne.string.isRequired,displayValue:ne.string.isRequired})).isRequired})).isRequired}).isRequired};/*! @license DOMPurify 3.1.2 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.1.2/LICENSE */const{entries:Y1,setPrototypeOf:Sv,isFrozen:VC,getPrototypeOf:BC,getOwnPropertyDescriptor:QC}=Object;let{freeze:Tt,seal:tn,create:X1}=Object,{apply:Bf,construct:Qf}=typeof Reflect<"u"&&Reflect;Tt||(Tt=function(t){return t});tn||(tn=function(t){return t});Bf||(Bf=function(t,n,r){return t.apply(n,r)});Qf||(Qf=function(t,n){return new t(...n)});const _s=Bt(Array.prototype.forEach),_v=Bt(Array.prototype.pop),Mo=Bt(Array.prototype.push),Ys=Bt(String.prototype.toLowerCase),lc=Bt(String.prototype.toString),Tv=Bt(String.prototype.match),Fo=Bt(String.prototype.replace),$C=Bt(String.prototype.indexOf),qC=Bt(String.prototype.trim),ln=Bt(Object.prototype.hasOwnProperty),Ot=Bt(RegExp.prototype.test),jo=HC(TypeError);function Bt(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return Bf(e,t,r)}}function HC(e){return function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Qf(e,n)}}function ue(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Ys;Sv&&Sv(e,null);let r=t.length;for(;r--;){let i=t[r];if(typeof i=="string"){const o=n(i);o!==i&&(VC(t)||(t[r]=o),i=o)}e[i]=!0}return e}function WC(e){for(let t=0;t<e.length;t++)ln(e,t)||(e[t]=null);return e}function $r(e){const t=X1(null);for(const[n,r]of Y1(e))ln(e,n)&&(Array.isArray(r)?t[n]=WC(r):r&&typeof r=="object"&&r.constructor===Object?t[n]=$r(r):t[n]=r);return t}function Ts(e,t){for(;e!==null;){const r=QC(e,t);if(r){if(r.get)return Bt(r.get);if(typeof r.value=="function")return Bt(r.value)}e=BC(e)}function n(){return null}return n}const xv=Tt(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),uc=Tt(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),cc=Tt(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),GC=Tt(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),fc=Tt(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),YC=Tt(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),bv=Tt(["#text"]),kv=Tt(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),dc=Tt(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Cv=Tt(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),xs=Tt(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),XC=tn(/\{\{[\w\W]*|[\w\W]*\}\}/gm),KC=tn(/<%[\w\W]*|[\w\W]*%>/gm),JC=tn(/\${[\w\W]*}/gm),ZC=tn(/^data-[\-\w.\u00B7-\uFFFF]/),eO=tn(/^aria-[\-\w]+$/),K1=tn(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),tO=tn(/^(?:\w+script|data):/i),nO=tn(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J1=tn(/^html$/i),rO=tn(/^[a-z][.\w]*(-[.\w]+)+$/i);var Ov=Object.freeze({__proto__:null,MUSTACHE_EXPR:XC,ERB_EXPR:KC,TMPLIT_EXPR:JC,DATA_ATTR:ZC,ARIA_ATTR:eO,IS_ALLOWED_URI:K1,IS_SCRIPT_OR_DATA:tO,ATTR_WHITESPACE:nO,DOCTYPE_NAME:J1,CUSTOM_ELEMENT:rO});const iO=function(){return typeof window>"u"?null:window},oO=function(t,n){if(typeof t!="object"||typeof t.createPolicy!="function")return null;let r=null;const i="data-tt-policy-suffix";n&&n.hasAttribute(i)&&(r=n.getAttribute(i));const o="dompurify"+(r?"#"+r:"");try{return t.createPolicy(o,{createHTML(a){return a},createScriptURL(a){return a}})}catch{return console.warn("TrustedTypes policy "+o+" could not be created."),null}};function Z1(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:iO();const t=Q=>Z1(Q);if(t.version="3.1.2",t.removed=[],!e||!e.document||e.document.nodeType!==9)return t.isSupported=!1,t;let{document:n}=e;const r=n,i=r.currentScript,{DocumentFragment:o,HTMLTemplateElement:a,Node:s,Element:l,NodeFilter:u,NamedNodeMap:c=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:f,DOMParser:d,trustedTypes:y}=e,g=l.prototype,v=Ts(g,"cloneNode"),E=Ts(g,"nextSibling"),h=Ts(g,"childNodes"),m=Ts(g,"parentNode");if(typeof a=="function"){const Q=n.createElement("template");Q.content&&Q.content.ownerDocument&&(n=Q.content.ownerDocument)}let p,S="";const{implementation:T,createNodeIterator:O,createDocumentFragment:w,getElementsByTagName:k}=n,{importNode:I}=r;let P={};t.isSupported=typeof Y1=="function"&&typeof m=="function"&&T&&T.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:K,ERB_EXPR:se,TMPLIT_EXPR:ee,DATA_ATTR:le,ARIA_ATTR:Ae,IS_SCRIPT_OR_DATA:rt,ATTR_WHITESPACE:Fe,CUSTOM_ELEMENT:M}=Ov;let{IS_ALLOWED_URI:V}=Ov,U=null;const ae=ue({},[...xv,...uc,...cc,...fc,...bv]);let q=null;const Ee=ue({},[...kv,...dc,...Cv,...xs]);let te=Object.seal(X1(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Se=null,Ce=null,qe=!0,Rn=!0,Ga=!1,Ya=!0,Xn=!1,ht=!0,Lt=!1,mi=!1,mo=!1,Ct=!1,vi=!1,yi=!1,Xa=!0,Ka=!1;const pu="user-content-";let Dn=!0,nn=!1,In={},En=null;const rn=ue({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Kn=null;const vo=ue({},["audio","video","img","source","image","track"]);let Jn=null;const Ja=ue({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Mt="http://www.w3.org/1998/Math/MathML",Ur="http://www.w3.org/2000/svg",qt="http://www.w3.org/1999/xhtml";let Pn=qt,yo=!1,gi=null;const Ei=ue({},[Mt,Ur,qt],lc);let Zn=null;const go=["application/xhtml+xml","text/html"],mu="text/html";let He=null,er=null;const Eo=255,vu=n.createElement("form"),x=function(_){return _ instanceof RegExp||_ instanceof Function},C=function(){let _=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(er&&er===_)){if((!_||typeof _!="object")&&(_={}),_=$r(_),Zn=go.indexOf(_.PARSER_MEDIA_TYPE)===-1?mu:_.PARSER_MEDIA_TYPE,He=Zn==="application/xhtml+xml"?lc:Ys,U=ln(_,"ALLOWED_TAGS")?ue({},_.ALLOWED_TAGS,He):ae,q=ln(_,"ALLOWED_ATTR")?ue({},_.ALLOWED_ATTR,He):Ee,gi=ln(_,"ALLOWED_NAMESPACES")?ue({},_.ALLOWED_NAMESPACES,lc):Ei,Jn=ln(_,"ADD_URI_SAFE_ATTR")?ue($r(Ja),_.ADD_URI_SAFE_ATTR,He):Ja,Kn=ln(_,"ADD_DATA_URI_TAGS")?ue($r(vo),_.ADD_DATA_URI_TAGS,He):vo,En=ln(_,"FORBID_CONTENTS")?ue({},_.FORBID_CONTENTS,He):rn,Se=ln(_,"FORBID_TAGS")?ue({},_.FORBID_TAGS,He):{},Ce=ln(_,"FORBID_ATTR")?ue({},_.FORBID_ATTR,He):{},In=ln(_,"USE_PROFILES")?_.USE_PROFILES:!1,qe=_.ALLOW_ARIA_ATTR!==!1,Rn=_.ALLOW_DATA_ATTR!==!1,Ga=_.ALLOW_UNKNOWN_PROTOCOLS||!1,Ya=_.ALLOW_SELF_CLOSE_IN_ATTR!==!1,Xn=_.SAFE_FOR_TEMPLATES||!1,ht=_.SAFE_FOR_XML!==!1,Lt=_.WHOLE_DOCUMENT||!1,Ct=_.RETURN_DOM||!1,vi=_.RETURN_DOM_FRAGMENT||!1,yi=_.RETURN_TRUSTED_TYPE||!1,mo=_.FORCE_BODY||!1,Xa=_.SANITIZE_DOM!==!1,Ka=_.SANITIZE_NAMED_PROPS||!1,Dn=_.KEEP_CONTENT!==!1,nn=_.IN_PLACE||!1,V=_.ALLOWED_URI_REGEXP||K1,Pn=_.NAMESPACE||qt,te=_.CUSTOM_ELEMENT_HANDLING||{},_.CUSTOM_ELEMENT_HANDLING&&x(_.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(te.tagNameCheck=_.CUSTOM_ELEMENT_HANDLING.tagNameCheck),_.CUSTOM_ELEMENT_HANDLING&&x(_.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(te.attributeNameCheck=_.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),_.CUSTOM_ELEMENT_HANDLING&&typeof _.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(te.allowCustomizedBuiltInElements=_.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Xn&&(Rn=!1),vi&&(Ct=!0),In&&(U=ue({},bv),q=[],In.html===!0&&(ue(U,xv),ue(q,kv)),In.svg===!0&&(ue(U,uc),ue(q,dc),ue(q,xs)),In.svgFilters===!0&&(ue(U,cc),ue(q,dc),ue(q,xs)),In.mathMl===!0&&(ue(U,fc),ue(q,Cv),ue(q,xs))),_.ADD_TAGS&&(U===ae&&(U=$r(U)),ue(U,_.ADD_TAGS,He)),_.ADD_ATTR&&(q===Ee&&(q=$r(q)),ue(q,_.ADD_ATTR,He)),_.ADD_URI_SAFE_ATTR&&ue(Jn,_.ADD_URI_SAFE_ATTR,He),_.FORBID_CONTENTS&&(En===rn&&(En=$r(En)),ue(En,_.FORBID_CONTENTS,He)),Dn&&(U["#text"]=!0),Lt&&ue(U,["html","head","body"]),U.table&&(ue(U,["tbody"]),delete Se.tbody),_.TRUSTED_TYPES_POLICY){if(typeof _.TRUSTED_TYPES_POLICY.createHTML!="function")throw jo('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof _.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw jo('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');p=_.TRUSTED_TYPES_POLICY,S=p.createHTML("")}else p===void 0&&(p=oO(y,i)),p!==null&&typeof S=="string"&&(S=p.createHTML(""));Tt&&Tt(_),er=_}},D=ue({},["mi","mo","mn","ms","mtext"]),j=ue({},["foreignobject","annotation-xml"]),B=ue({},["title","style","font","a","script"]),oe=ue({},[...uc,...cc,...GC]),J=ue({},[...fc,...YC]),H=function(_){let L=m(_);(!L||!L.tagName)&&(L={namespaceURI:Pn,tagName:"template"});const W=Ys(_.tagName),z=Ys(L.tagName);return gi[_.namespaceURI]?_.namespaceURI===Ur?L.namespaceURI===qt?W==="svg":L.namespaceURI===Mt?W==="svg"&&(z==="annotation-xml"||D[z]):!!oe[W]:_.namespaceURI===Mt?L.namespaceURI===qt?W==="math":L.namespaceURI===Ur?W==="math"&&j[z]:!!J[W]:_.namespaceURI===qt?L.namespaceURI===Ur&&!j[z]||L.namespaceURI===Mt&&!D[z]?!1:!J[W]&&(B[W]||!oe[W]):!!(Zn==="application/xhtml+xml"&&gi[_.namespaceURI]):!1},Z=function(_){Mo(t.removed,{element:_});try{_.parentNode.removeChild(_)}catch{_.remove()}},re=function(_,L){try{Mo(t.removed,{attribute:L.getAttributeNode(_),from:L})}catch{Mo(t.removed,{attribute:null,from:L})}if(L.removeAttribute(_),_==="is"&&!q[_])if(Ct||vi)try{Z(L)}catch{}else try{L.setAttribute(_,"")}catch{}},Ge=function(_){let L=null,W=null;if(mo)_="<remove></remove>"+_;else{const Te=Tv(_,/^[\r\n\t ]+/);W=Te&&Te[0]}Zn==="application/xhtml+xml"&&Pn===qt&&(_='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+_+"</body></html>");const z=p?p.createHTML(_):_;if(Pn===qt)try{L=new d().parseFromString(z,Zn)}catch{}if(!L||!L.documentElement){L=T.createDocument(Pn,"template",null);try{L.documentElement.innerHTML=yo?S:z}catch{}}const ye=L.body||L.documentElement;return _&&W&&ye.insertBefore(n.createTextNode(W),ye.childNodes[0]||null),Pn===qt?k.call(L,Lt?"html":"body")[0]:Lt?L.documentElement:ye},Ye=function(_){return O.call(_.ownerDocument||_,_,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT|u.SHOW_PROCESSING_INSTRUCTION|u.SHOW_CDATA_SECTION,null)},ie=function(_){return _ instanceof f&&(typeof _.__depth<"u"&&typeof _.__depth!="number"||typeof _.__removalCount<"u"&&typeof _.__removalCount!="number"||typeof _.nodeName!="string"||typeof _.textContent!="string"||typeof _.removeChild!="function"||!(_.attributes instanceof c)||typeof _.removeAttribute!="function"||typeof _.setAttribute!="function"||typeof _.namespaceURI!="string"||typeof _.insertBefore!="function"||typeof _.hasChildNodes!="function")},Oe=function(_){return typeof s=="function"&&_ instanceof s},ke=function(_,L,W){P[_]&&_s(P[_],z=>{z.call(t,L,W,er)})},tr=function(_){let L=null;if(ke("beforeSanitizeElements",_,null),ie(_))return Z(_),!0;const W=He(_.nodeName);if(ke("uponSanitizeElement",_,{tagName:W,allowedTags:U}),_.hasChildNodes()&&!Oe(_.firstElementChild)&&Ot(/<[/\w]/g,_.innerHTML)&&Ot(/<[/\w]/g,_.textContent)||_.nodeType===7||ht&&_.nodeType===8&&Ot(/<[/\w]/g,_.data))return Z(_),!0;if(!U[W]||Se[W]){if(!Se[W]&&An(W)&&(te.tagNameCheck instanceof RegExp&&Ot(te.tagNameCheck,W)||te.tagNameCheck instanceof Function&&te.tagNameCheck(W)))return!1;if(Dn&&!En[W]){const z=m(_)||_.parentNode,ye=h(_)||_.childNodes;if(ye&&z){const Te=ye.length;for(let ge=Te-1;ge>=0;--ge){const Xe=v(ye[ge],!0);Xe.__removalCount=(_.__removalCount||0)+1,z.insertBefore(Xe,E(_))}}}return Z(_),!0}return _ instanceof l&&!H(_)||(W==="noscript"||W==="noembed"||W==="noframes")&&Ot(/<\/no(script|embed|frames)/i,_.innerHTML)?(Z(_),!0):(Xn&&_.nodeType===3&&(L=_.textContent,_s([K,se,ee],z=>{L=Fo(L,z," ")}),_.textContent!==L&&(Mo(t.removed,{element:_.cloneNode()}),_.textContent=L)),ke("afterSanitizeElements",_,null),!1)},on=function(_,L,W){if(Xa&&(L==="id"||L==="name")&&(W in n||W in vu))return!1;if(!(Rn&&!Ce[L]&&Ot(le,L))){if(!(qe&&Ot(Ae,L))){if(!q[L]||Ce[L]){if(!(An(_)&&(te.tagNameCheck instanceof RegExp&&Ot(te.tagNameCheck,_)||te.tagNameCheck instanceof Function&&te.tagNameCheck(_))&&(te.attributeNameCheck instanceof RegExp&&Ot(te.attributeNameCheck,L)||te.attributeNameCheck instanceof Function&&te.attributeNameCheck(L))||L==="is"&&te.allowCustomizedBuiltInElements&&(te.tagNameCheck instanceof RegExp&&Ot(te.tagNameCheck,W)||te.tagNameCheck instanceof Function&&te.tagNameCheck(W))))return!1}else if(!Jn[L]){if(!Ot(V,Fo(W,Fe,""))){if(!((L==="src"||L==="xlink:href"||L==="href")&&_!=="script"&&$C(W,"data:")===0&&Kn[_])){if(!(Ga&&!Ot(rt,Fo(W,Fe,"")))){if(W)return!1}}}}}}return!0},An=function(_){return _!=="annotation-xml"&&Tv(_,M)},Ln=function(_){ke("beforeSanitizeAttributes",_,null);const{attributes:L}=_;if(!L)return;const W={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:q};let z=L.length;for(;z--;){const ye=L[z],{name:Te,namespaceURI:ge,value:Xe}=ye,rr=He(Te);let pt=Te==="value"?Xe:qC(Xe);if(W.attrName=rr,W.attrValue=pt,W.keepAttr=!0,W.forceKeepAttr=void 0,ke("uponSanitizeAttribute",_,W),pt=W.attrValue,W.forceKeepAttr||(re(Te,_),!W.keepAttr))continue;if(!Ya&&Ot(/\/>/i,pt)){re(Te,_);continue}Xn&&_s([K,se,ee],Vh=>{pt=Fo(pt,Vh," ")});const Uh=He(_.nodeName);if(on(Uh,rr,pt)){if(Ka&&(rr==="id"||rr==="name")&&(re(Te,_),pt=pu+pt),p&&typeof y=="object"&&typeof y.getAttributeType=="function"&&!ge)switch(y.getAttributeType(Uh,rr)){case"TrustedHTML":{pt=p.createHTML(pt);break}case"TrustedScriptURL":{pt=p.createScriptURL(pt);break}}try{ge?_.setAttributeNS(ge,Te,pt):_.setAttribute(Te,pt),_v(t.removed)}catch{}}}ke("afterSanitizeAttributes",_,null)},nr=function Q(_){let L=null;const W=Ye(_);for(ke("beforeSanitizeShadowDOM",_,null);L=W.nextNode();){if(ke("uponSanitizeShadowNode",L,null),tr(L))continue;const z=m(L);L.nodeType===1&&(z&&z.__depth?L.__depth=(L.__removalCount||0)+z.__depth+1:L.__depth=1),L.__depth>=Eo&&Z(L),L.content instanceof o&&(L.content.__depth=L.__depth,Q(L.content)),Ln(L)}ke("afterSanitizeShadowDOM",_,null)};return t.sanitize=function(Q){let _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},L=null,W=null,z=null,ye=null;if(yo=!Q,yo&&(Q="<!-->"),typeof Q!="string"&&!Oe(Q))if(typeof Q.toString=="function"){if(Q=Q.toString(),typeof Q!="string")throw jo("dirty is not a string, aborting")}else throw jo("toString is not a function");if(!t.isSupported)return Q;if(mi||C(_),t.removed=[],typeof Q=="string"&&(nn=!1),nn){if(Q.nodeName){const Xe=He(Q.nodeName);if(!U[Xe]||Se[Xe])throw jo("root node is forbidden and cannot be sanitized in-place")}}else if(Q instanceof s)L=Ge("<!---->"),W=L.ownerDocument.importNode(Q,!0),W.nodeType===1&&W.nodeName==="BODY"||W.nodeName==="HTML"?L=W:L.appendChild(W);else{if(!Ct&&!Xn&&!Lt&&Q.indexOf("<")===-1)return p&&yi?p.createHTML(Q):Q;if(L=Ge(Q),!L)return Ct?null:yi?S:""}L&&mo&&Z(L.firstChild);const Te=Ye(nn?Q:L);for(;z=Te.nextNode();){if(tr(z))continue;const Xe=m(z);z.nodeType===1&&(Xe&&Xe.__depth?z.__depth=(z.__removalCount||0)+Xe.__depth+1:z.__depth=1),z.__depth>=Eo&&Z(z),z.content instanceof o&&(z.content.__depth=z.__depth,nr(z.content)),Ln(z)}if(nn)return Q;if(Ct){if(vi)for(ye=w.call(L.ownerDocument);L.firstChild;)ye.appendChild(L.firstChild);else ye=L;return(q.shadowroot||q.shadowrootmode)&&(ye=I.call(r,ye,!0)),ye}let ge=Lt?L.outerHTML:L.innerHTML;return Lt&&U["!doctype"]&&L.ownerDocument&&L.ownerDocument.doctype&&L.ownerDocument.doctype.name&&Ot(J1,L.ownerDocument.doctype.name)&&(ge="<!DOCTYPE "+L.ownerDocument.doctype.name+`>
`+ge),Xn&&_s([K,se,ee],Xe=>{ge=Fo(ge,Xe," ")}),p&&yi?p.createHTML(ge):ge},t.setConfig=function(){let Q=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};C(Q),mi=!0},t.clearConfig=function(){er=null,mi=!1},t.isValidAttribute=function(Q,_,L){er||C({});const W=He(Q),z=He(_);return on(W,z,L)},t.addHook=function(Q,_){typeof _=="function"&&(P[Q]=P[Q]||[],Mo(P[Q],_))},t.removeHook=function(Q){if(P[Q])return _v(P[Q])},t.removeHooks=function(Q){P[Q]&&(P[Q]=[])},t.removeAllHooks=function(){P={}},t}var aO=Z1(),eE={},kh={},Ch={};Object.defineProperty(Ch,"__esModule",{value:!0});var Nv="html",Rv="head",bs="body",sO=/<([a-zA-Z]+[0-9]?)/,Dv=/<head[^]*>/i,Iv=/<body[^]*>/i,Cl=function(e,t){throw new Error("This browser does not support `document.implementation.createHTMLDocument`")},$f=function(e,t){throw new Error("This browser does not support `DOMParser.prototype.parseFromString`")},Pv=typeof window=="object"&&window.DOMParser;if(typeof Pv=="function"){var lO=new Pv,uO="text/html";$f=function(e,t){return t&&(e="<".concat(t,">").concat(e,"</").concat(t,">")),lO.parseFromString(e,uO)},Cl=$f}if(typeof document=="object"&&document.implementation){var ks=document.implementation.createHTMLDocument();Cl=function(e,t){if(t){var n=ks.documentElement.querySelector(t);return n&&(n.innerHTML=e),ks}return ks.documentElement.innerHTML=e,ks}}var Cs=typeof document=="object"&&document.createElement("template"),qf;Cs&&Cs.content&&(qf=function(e){return Cs.innerHTML=e,Cs.content.childNodes});function cO(e){var t,n,r=e.match(sO),i=r&&r[1]?r[1].toLowerCase():"";switch(i){case Nv:{var o=$f(e);if(!Dv.test(e)){var a=o.querySelector(Rv);(t=a==null?void 0:a.parentNode)===null||t===void 0||t.removeChild(a)}if(!Iv.test(e)){var a=o.querySelector(bs);(n=a==null?void 0:a.parentNode)===null||n===void 0||n.removeChild(a)}return o.querySelectorAll(Nv)}case Rv:case bs:{var s=Cl(e).querySelectorAll(i);return Iv.test(e)&&Dv.test(e)?s[0].parentNode.childNodes:s}default:{if(qf)return qf(e);var a=Cl(e,bs).querySelector(bs);return a.childNodes}}}Ch.default=cO;var so={},Oh={},Nh={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Doctype=e.CDATA=e.Tag=e.Style=e.Script=e.Comment=e.Directive=e.Text=e.Root=e.isTag=e.ElementType=void 0;var t;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(t=e.ElementType||(e.ElementType={}));function n(r){return r.type===t.Tag||r.type===t.Script||r.type===t.Style}e.isTag=n,e.Root=t.Root,e.Text=t.Text,e.Directive=t.Directive,e.Comment=t.Comment,e.Script=t.Script,e.Style=t.Style,e.Tag=t.Tag,e.CDATA=t.CDATA,e.Doctype=t.Doctype})(Nh);var ce={},jr=et&&et.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),aa=et&&et.__assign||function(){return aa=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},aa.apply(this,arguments)};Object.defineProperty(ce,"__esModule",{value:!0});ce.cloneNode=ce.hasChildren=ce.isDocument=ce.isDirective=ce.isComment=ce.isText=ce.isCDATA=ce.isTag=ce.Element=ce.Document=ce.CDATA=ce.NodeWithChildren=ce.ProcessingInstruction=ce.Comment=ce.Text=ce.DataNode=ce.Node=void 0;var At=Nh,Rh=function(){function e(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent},set:function(t){this.parent=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){return this.prev},set:function(t){this.prev=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){return this.next},set:function(t){this.next=t},enumerable:!1,configurable:!0}),e.prototype.cloneNode=function(t){return t===void 0&&(t=!1),Dh(this,t)},e}();ce.Node=Rh;var su=function(e){jr(t,e);function t(n){var r=e.call(this)||this;return r.data=n,r}return Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.data},set:function(n){this.data=n},enumerable:!1,configurable:!0}),t}(Rh);ce.DataNode=su;var tE=function(e){jr(t,e);function t(){var n=e!==null&&e.apply(this,arguments)||this;return n.type=At.ElementType.Text,n}return Object.defineProperty(t.prototype,"nodeType",{get:function(){return 3},enumerable:!1,configurable:!0}),t}(su);ce.Text=tE;var nE=function(e){jr(t,e);function t(){var n=e!==null&&e.apply(this,arguments)||this;return n.type=At.ElementType.Comment,n}return Object.defineProperty(t.prototype,"nodeType",{get:function(){return 8},enumerable:!1,configurable:!0}),t}(su);ce.Comment=nE;var rE=function(e){jr(t,e);function t(n,r){var i=e.call(this,r)||this;return i.name=n,i.type=At.ElementType.Directive,i}return Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),t}(su);ce.ProcessingInstruction=rE;var lu=function(e){jr(t,e);function t(n){var r=e.call(this)||this;return r.children=n,r}return Object.defineProperty(t.prototype,"firstChild",{get:function(){var n;return(n=this.children[0])!==null&&n!==void 0?n:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},set:function(n){this.children=n},enumerable:!1,configurable:!0}),t}(Rh);ce.NodeWithChildren=lu;var iE=function(e){jr(t,e);function t(){var n=e!==null&&e.apply(this,arguments)||this;return n.type=At.ElementType.CDATA,n}return Object.defineProperty(t.prototype,"nodeType",{get:function(){return 4},enumerable:!1,configurable:!0}),t}(lu);ce.CDATA=iE;var oE=function(e){jr(t,e);function t(){var n=e!==null&&e.apply(this,arguments)||this;return n.type=At.ElementType.Root,n}return Object.defineProperty(t.prototype,"nodeType",{get:function(){return 9},enumerable:!1,configurable:!0}),t}(lu);ce.Document=oE;var aE=function(e){jr(t,e);function t(n,r,i,o){i===void 0&&(i=[]),o===void 0&&(o=n==="script"?At.ElementType.Script:n==="style"?At.ElementType.Style:At.ElementType.Tag);var a=e.call(this,i)||this;return a.name=n,a.attribs=r,a.type=o,a}return Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tagName",{get:function(){return this.name},set:function(n){this.name=n},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var n=this;return Object.keys(this.attribs).map(function(r){var i,o;return{name:r,value:n.attribs[r],namespace:(i=n["x-attribsNamespace"])===null||i===void 0?void 0:i[r],prefix:(o=n["x-attribsPrefix"])===null||o===void 0?void 0:o[r]}})},enumerable:!1,configurable:!0}),t}(lu);ce.Element=aE;function sE(e){return(0,At.isTag)(e)}ce.isTag=sE;function lE(e){return e.type===At.ElementType.CDATA}ce.isCDATA=lE;function uE(e){return e.type===At.ElementType.Text}ce.isText=uE;function cE(e){return e.type===At.ElementType.Comment}ce.isComment=cE;function fE(e){return e.type===At.ElementType.Directive}ce.isDirective=fE;function dE(e){return e.type===At.ElementType.Root}ce.isDocument=dE;function fO(e){return Object.prototype.hasOwnProperty.call(e,"children")}ce.hasChildren=fO;function Dh(e,t){t===void 0&&(t=!1);var n;if(uE(e))n=new tE(e.data);else if(cE(e))n=new nE(e.data);else if(sE(e)){var r=t?hc(e.children):[],i=new aE(e.name,aa({},e.attribs),r);r.forEach(function(l){return l.parent=i}),e.namespace!=null&&(i.namespace=e.namespace),e["x-attribsNamespace"]&&(i["x-attribsNamespace"]=aa({},e["x-attribsNamespace"])),e["x-attribsPrefix"]&&(i["x-attribsPrefix"]=aa({},e["x-attribsPrefix"])),n=i}else if(lE(e)){var r=t?hc(e.children):[],o=new iE(r);r.forEach(function(u){return u.parent=o}),n=o}else if(dE(e)){var r=t?hc(e.children):[],a=new oE(r);r.forEach(function(u){return u.parent=a}),e["x-mode"]&&(a["x-mode"]=e["x-mode"]),n=a}else if(fE(e)){var s=new rE(e.name,e.data);e["x-name"]!=null&&(s["x-name"]=e["x-name"],s["x-publicId"]=e["x-publicId"],s["x-systemId"]=e["x-systemId"]),n=s}else throw new Error("Not implemented yet: ".concat(e.type));return n.startIndex=e.startIndex,n.endIndex=e.endIndex,e.sourceCodeLocation!=null&&(n.sourceCodeLocation=e.sourceCodeLocation),n}ce.cloneNode=Dh;function hc(e){for(var t=e.map(function(r){return Dh(r,!0)}),n=1;n<t.length;n++)t[n].prev=t[n-1],t[n-1].next=t[n];return t}(function(e){var t=et&&et.__createBinding||(Object.create?function(s,l,u,c){c===void 0&&(c=u);var f=Object.getOwnPropertyDescriptor(l,u);(!f||("get"in f?!l.__esModule:f.writable||f.configurable))&&(f={enumerable:!0,get:function(){return l[u]}}),Object.defineProperty(s,c,f)}:function(s,l,u,c){c===void 0&&(c=u),s[c]=l[u]}),n=et&&et.__exportStar||function(s,l){for(var u in s)u!=="default"&&!Object.prototype.hasOwnProperty.call(l,u)&&t(l,s,u)};Object.defineProperty(e,"__esModule",{value:!0}),e.DomHandler=void 0;var r=Nh,i=ce;n(ce,e);var o={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},a=function(){function s(l,u,c){this.dom=[],this.root=new i.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,typeof u=="function"&&(c=u,u=o),typeof l=="object"&&(u=l,l=void 0),this.callback=l??null,this.options=u??o,this.elementCB=c??null}return s.prototype.onparserinit=function(l){this.parser=l},s.prototype.onreset=function(){this.dom=[],this.root=new i.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},s.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},s.prototype.onerror=function(l){this.handleCallback(l)},s.prototype.onclosetag=function(){this.lastNode=null;var l=this.tagStack.pop();this.options.withEndIndices&&(l.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(l)},s.prototype.onopentag=function(l,u){var c=this.options.xmlMode?r.ElementType.Tag:void 0,f=new i.Element(l,u,void 0,c);this.addNode(f),this.tagStack.push(f)},s.prototype.ontext=function(l){var u=this.lastNode;if(u&&u.type===r.ElementType.Text)u.data+=l,this.options.withEndIndices&&(u.endIndex=this.parser.endIndex);else{var c=new i.Text(l);this.addNode(c),this.lastNode=c}},s.prototype.oncomment=function(l){if(this.lastNode&&this.lastNode.type===r.ElementType.Comment){this.lastNode.data+=l;return}var u=new i.Comment(l);this.addNode(u),this.lastNode=u},s.prototype.oncommentend=function(){this.lastNode=null},s.prototype.oncdatastart=function(){var l=new i.Text(""),u=new i.CDATA([l]);this.addNode(u),l.parent=u,this.lastNode=l},s.prototype.oncdataend=function(){this.lastNode=null},s.prototype.onprocessinginstruction=function(l,u){var c=new i.ProcessingInstruction(l,u);this.addNode(c)},s.prototype.handleCallback=function(l){if(typeof this.callback=="function")this.callback(l,this.dom);else if(l)throw l},s.prototype.addNode=function(l){var u=this.tagStack[this.tagStack.length-1],c=u.children[u.children.length-1];this.options.withStartIndices&&(l.startIndex=this.parser.startIndex),this.options.withEndIndices&&(l.endIndex=this.parser.endIndex),u.children.push(l),c&&(l.prev=c,c.next=l),l.parent=u,this.lastNode=null},s}();e.DomHandler=a,e.default=a})(Oh);var hE={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.CASE_SENSITIVE_TAG_NAMES_MAP=e.CASE_SENSITIVE_TAG_NAMES=void 0,e.CASE_SENSITIVE_TAG_NAMES=["animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","linearGradient","radialGradient","textPath"],e.CASE_SENSITIVE_TAG_NAMES_MAP=e.CASE_SENSITIVE_TAG_NAMES.reduce(function(t,n){return t[n.toLowerCase()]=n,t},{})})(hE);Object.defineProperty(so,"__esModule",{value:!0});so.formatDOM=so.formatAttributes=void 0;var Os=Oh,dO=hE;function hO(e){return dO.CASE_SENSITIVE_TAG_NAMES_MAP[e]}function pE(e){for(var t={},n=0,r=e.length;n<r;n++){var i=e[n];t[i.name]=i.value}return t}so.formatAttributes=pE;function pO(e){e=e.toLowerCase();var t=hO(e);return t||e}function mE(e,t,n){t===void 0&&(t=null);for(var r=[],i,o=0,a=e.length;o<a;o++){var s=e[o];switch(s.nodeType){case 1:{var l=pO(s.nodeName);i=new Os.Element(l,pE(s.attributes)),i.children=mE(l==="template"?s.content.childNodes:s.childNodes,i);break}case 3:i=new Os.Text(s.nodeValue);break;case 8:i=new Os.Comment(s.nodeValue);break;default:continue}var u=r[o-1]||null;u&&(u.next=i),i.parent=t,i.prev=u,i.next=null,r.push(i)}return n&&(i=new Os.ProcessingInstruction(n.substring(0,n.indexOf(" ")).toLowerCase(),n),i.next=r[0]||null,i.parent=t,r.unshift(i),r[1]&&(r[1].prev=r[0])),r}so.formatDOM=mE;var mO=et&&et.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(kh,"__esModule",{value:!0});var vO=mO(Ch),yO=so,gO=/<(![a-zA-Z\s]+)>/;function EO(e){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];var t=e.match(gO),n=t?t[1]:void 0;return(0,yO.formatDOM)((0,vO.default)(e),null,n)}kh.default=EO;var uu={},gn={},cu={},wO=0;cu.SAME=wO;var SO=1;cu.CAMELCASE=SO;cu.possibleStandardNames={accept:0,acceptCharset:1,"accept-charset":"acceptCharset",accessKey:1,action:0,allowFullScreen:1,alt:0,as:0,async:0,autoCapitalize:1,autoComplete:1,autoCorrect:1,autoFocus:1,autoPlay:1,autoSave:1,capture:0,cellPadding:1,cellSpacing:1,challenge:0,charSet:1,checked:0,children:0,cite:0,class:"className",classID:1,className:1,cols:0,colSpan:1,content:0,contentEditable:1,contextMenu:1,controls:0,controlsList:1,coords:0,crossOrigin:1,dangerouslySetInnerHTML:1,data:0,dateTime:1,default:0,defaultChecked:1,defaultValue:1,defer:0,dir:0,disabled:0,disablePictureInPicture:1,disableRemotePlayback:1,download:0,draggable:0,encType:1,enterKeyHint:1,for:"htmlFor",form:0,formMethod:1,formAction:1,formEncType:1,formNoValidate:1,formTarget:1,frameBorder:1,headers:0,height:0,hidden:0,high:0,href:0,hrefLang:1,htmlFor:1,httpEquiv:1,"http-equiv":"httpEquiv",icon:0,id:0,innerHTML:1,inputMode:1,integrity:0,is:0,itemID:1,itemProp:1,itemRef:1,itemScope:1,itemType:1,keyParams:1,keyType:1,kind:0,label:0,lang:0,list:0,loop:0,low:0,manifest:0,marginWidth:1,marginHeight:1,max:0,maxLength:1,media:0,mediaGroup:1,method:0,min:0,minLength:1,multiple:0,muted:0,name:0,noModule:1,nonce:0,noValidate:1,open:0,optimum:0,pattern:0,placeholder:0,playsInline:1,poster:0,preload:0,profile:0,radioGroup:1,readOnly:1,referrerPolicy:1,rel:0,required:0,reversed:0,role:0,rows:0,rowSpan:1,sandbox:0,scope:0,scoped:0,scrolling:0,seamless:0,selected:0,shape:0,size:0,sizes:0,span:0,spellCheck:1,src:0,srcDoc:1,srcLang:1,srcSet:1,start:0,step:0,style:0,summary:0,tabIndex:1,target:0,title:0,type:0,useMap:1,value:0,width:0,wmode:0,wrap:0,about:0,accentHeight:1,"accent-height":"accentHeight",accumulate:0,additive:0,alignmentBaseline:1,"alignment-baseline":"alignmentBaseline",allowReorder:1,alphabetic:0,amplitude:0,arabicForm:1,"arabic-form":"arabicForm",ascent:0,attributeName:1,attributeType:1,autoReverse:1,azimuth:0,baseFrequency:1,baselineShift:1,"baseline-shift":"baselineShift",baseProfile:1,bbox:0,begin:0,bias:0,by:0,calcMode:1,capHeight:1,"cap-height":"capHeight",clip:0,clipPath:1,"clip-path":"clipPath",clipPathUnits:1,clipRule:1,"clip-rule":"clipRule",color:0,colorInterpolation:1,"color-interpolation":"colorInterpolation",colorInterpolationFilters:1,"color-interpolation-filters":"colorInterpolationFilters",colorProfile:1,"color-profile":"colorProfile",colorRendering:1,"color-rendering":"colorRendering",contentScriptType:1,contentStyleType:1,cursor:0,cx:0,cy:0,d:0,datatype:0,decelerate:0,descent:0,diffuseConstant:1,direction:0,display:0,divisor:0,dominantBaseline:1,"dominant-baseline":"dominantBaseline",dur:0,dx:0,dy:0,edgeMode:1,elevation:0,enableBackground:1,"enable-background":"enableBackground",end:0,exponent:0,externalResourcesRequired:1,fill:0,fillOpacity:1,"fill-opacity":"fillOpacity",fillRule:1,"fill-rule":"fillRule",filter:0,filterRes:1,filterUnits:1,floodOpacity:1,"flood-opacity":"floodOpacity",floodColor:1,"flood-color":"floodColor",focusable:0,fontFamily:1,"font-family":"fontFamily",fontSize:1,"font-size":"fontSize",fontSizeAdjust:1,"font-size-adjust":"fontSizeAdjust",fontStretch:1,"font-stretch":"fontStretch",fontStyle:1,"font-style":"fontStyle",fontVariant:1,"font-variant":"fontVariant",fontWeight:1,"font-weight":"fontWeight",format:0,from:0,fx:0,fy:0,g1:0,g2:0,glyphName:1,"glyph-name":"glyphName",glyphOrientationHorizontal:1,"glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphOrientationVertical:1,"glyph-orientation-vertical":"glyphOrientationVertical",glyphRef:1,gradientTransform:1,gradientUnits:1,hanging:0,horizAdvX:1,"horiz-adv-x":"horizAdvX",horizOriginX:1,"horiz-origin-x":"horizOriginX",ideographic:0,imageRendering:1,"image-rendering":"imageRendering",in2:0,in:0,inlist:0,intercept:0,k1:0,k2:0,k3:0,k4:0,k:0,kernelMatrix:1,kernelUnitLength:1,kerning:0,keyPoints:1,keySplines:1,keyTimes:1,lengthAdjust:1,letterSpacing:1,"letter-spacing":"letterSpacing",lightingColor:1,"lighting-color":"lightingColor",limitingConeAngle:1,local:0,markerEnd:1,"marker-end":"markerEnd",markerHeight:1,markerMid:1,"marker-mid":"markerMid",markerStart:1,"marker-start":"markerStart",markerUnits:1,markerWidth:1,mask:0,maskContentUnits:1,maskUnits:1,mathematical:0,mode:0,numOctaves:1,offset:0,opacity:0,operator:0,order:0,orient:0,orientation:0,origin:0,overflow:0,overlinePosition:1,"overline-position":"overlinePosition",overlineThickness:1,"overline-thickness":"overlineThickness",paintOrder:1,"paint-order":"paintOrder",panose1:0,"panose-1":"panose1",pathLength:1,patternContentUnits:1,patternTransform:1,patternUnits:1,pointerEvents:1,"pointer-events":"pointerEvents",points:0,pointsAtX:1,pointsAtY:1,pointsAtZ:1,prefix:0,preserveAlpha:1,preserveAspectRatio:1,primitiveUnits:1,property:0,r:0,radius:0,refX:1,refY:1,renderingIntent:1,"rendering-intent":"renderingIntent",repeatCount:1,repeatDur:1,requiredExtensions:1,requiredFeatures:1,resource:0,restart:0,result:0,results:0,rotate:0,rx:0,ry:0,scale:0,security:0,seed:0,shapeRendering:1,"shape-rendering":"shapeRendering",slope:0,spacing:0,specularConstant:1,specularExponent:1,speed:0,spreadMethod:1,startOffset:1,stdDeviation:1,stemh:0,stemv:0,stitchTiles:1,stopColor:1,"stop-color":"stopColor",stopOpacity:1,"stop-opacity":"stopOpacity",strikethroughPosition:1,"strikethrough-position":"strikethroughPosition",strikethroughThickness:1,"strikethrough-thickness":"strikethroughThickness",string:0,stroke:0,strokeDasharray:1,"stroke-dasharray":"strokeDasharray",strokeDashoffset:1,"stroke-dashoffset":"strokeDashoffset",strokeLinecap:1,"stroke-linecap":"strokeLinecap",strokeLinejoin:1,"stroke-linejoin":"strokeLinejoin",strokeMiterlimit:1,"stroke-miterlimit":"strokeMiterlimit",strokeWidth:1,"stroke-width":"strokeWidth",strokeOpacity:1,"stroke-opacity":"strokeOpacity",suppressContentEditableWarning:1,suppressHydrationWarning:1,surfaceScale:1,systemLanguage:1,tableValues:1,targetX:1,targetY:1,textAnchor:1,"text-anchor":"textAnchor",textDecoration:1,"text-decoration":"textDecoration",textLength:1,textRendering:1,"text-rendering":"textRendering",to:0,transform:0,typeof:0,u1:0,u2:0,underlinePosition:1,"underline-position":"underlinePosition",underlineThickness:1,"underline-thickness":"underlineThickness",unicode:0,unicodeBidi:1,"unicode-bidi":"unicodeBidi",unicodeRange:1,"unicode-range":"unicodeRange",unitsPerEm:1,"units-per-em":"unitsPerEm",unselectable:0,vAlphabetic:1,"v-alphabetic":"vAlphabetic",values:0,vectorEffect:1,"vector-effect":"vectorEffect",version:0,vertAdvY:1,"vert-adv-y":"vertAdvY",vertOriginX:1,"vert-origin-x":"vertOriginX",vertOriginY:1,"vert-origin-y":"vertOriginY",vHanging:1,"v-hanging":"vHanging",vIdeographic:1,"v-ideographic":"vIdeographic",viewBox:1,viewTarget:1,visibility:0,vMathematical:1,"v-mathematical":"vMathematical",vocab:0,widths:0,wordSpacing:1,"word-spacing":"wordSpacing",writingMode:1,"writing-mode":"writingMode",x1:0,x2:0,x:0,xChannelSelector:1,xHeight:1,"x-height":"xHeight",xlinkActuate:1,"xlink:actuate":"xlinkActuate",xlinkArcrole:1,"xlink:arcrole":"xlinkArcrole",xlinkHref:1,"xlink:href":"xlinkHref",xlinkRole:1,"xlink:role":"xlinkRole",xlinkShow:1,"xlink:show":"xlinkShow",xlinkTitle:1,"xlink:title":"xlinkTitle",xlinkType:1,"xlink:type":"xlinkType",xmlBase:1,"xml:base":"xmlBase",xmlLang:1,"xml:lang":"xmlLang",xmlns:0,"xml:space":"xmlSpace",xmlnsXlink:1,"xmlns:xlink":"xmlnsXlink",xmlSpace:1,y1:0,y2:0,y:0,yChannelSelector:1,z:0,zoomAndPan:1};const vE=0,zr=1,fu=2,du=3,Ih=4,yE=5,gE=6;function _O(e){return dt.hasOwnProperty(e)?dt[e]:null}function kt(e,t,n,r,i,o,a){this.acceptsBooleans=t===fu||t===du||t===Ih,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}const dt={},TO=["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"];TO.forEach(e=>{dt[e]=new kt(e,vE,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(([e,t])=>{dt[e]=new kt(e,zr,!1,t,null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(e=>{dt[e]=new kt(e,fu,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(e=>{dt[e]=new kt(e,fu,!1,e,null,!1,!1)});["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach(e=>{dt[e]=new kt(e,du,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(e=>{dt[e]=new kt(e,du,!0,e,null,!1,!1)});["capture","download"].forEach(e=>{dt[e]=new kt(e,Ih,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(e=>{dt[e]=new kt(e,gE,!1,e,null,!1,!1)});["rowSpan","start"].forEach(e=>{dt[e]=new kt(e,yE,!1,e.toLowerCase(),null,!1,!1)});const Ph=/[\-\:]([a-z])/g,Ah=e=>e[1].toUpperCase();["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach(e=>{const t=e.replace(Ph,Ah);dt[t]=new kt(t,zr,!1,e,null,!1,!1)});["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach(e=>{const t=e.replace(Ph,Ah);dt[t]=new kt(t,zr,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(e=>{const t=e.replace(Ph,Ah);dt[t]=new kt(t,zr,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(e=>{dt[e]=new kt(e,zr,!1,e.toLowerCase(),null,!1,!1)});const xO="xlinkHref";dt[xO]=new kt("xlinkHref",zr,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(e=>{dt[e]=new kt(e,zr,!1,e.toLowerCase(),null,!0,!0)});const{CAMELCASE:bO,SAME:kO,possibleStandardNames:Av}=cu,CO=":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",OO=CO+"\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040",NO=RegExp.prototype.test.bind(new RegExp("^(data|aria)-["+OO+"]*$")),RO=Object.keys(Av).reduce((e,t)=>{const n=Av[t];return n===kO?e[t]=t:n===bO?e[t.toLowerCase()]=t:e[t]=n,e},{});gn.BOOLEAN=du;gn.BOOLEANISH_STRING=fu;gn.NUMERIC=yE;gn.OVERLOADED_BOOLEAN=Ih;gn.POSITIVE_NUMERIC=gE;gn.RESERVED=vE;gn.STRING=zr;gn.getPropertyInfo=_O;gn.isCustomAttribute=NO;gn.possibleStandardNames=RO;var Lh={},Mh={},Lv=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,DO=/\n/g,IO=/^\s*/,PO=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,AO=/^:\s*/,LO=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,MO=/^[;\s]*/,FO=/^\s+|\s+$/g,jO=`
`,Mv="/",Fv="*",Hr="",zO="comment",UO="declaration",VO=function(e,t){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,r=1;function i(g){var v=g.match(DO);v&&(n+=v.length);var E=g.lastIndexOf(jO);r=~E?g.length-E:r+g.length}function o(){var g={line:n,column:r};return function(v){return v.position=new a(g),u(),v}}function a(g){this.start=g,this.end={line:n,column:r},this.source=t.source}a.prototype.content=e;function s(g){var v=new Error(t.source+":"+n+":"+r+": "+g);if(v.reason=g,v.filename=t.source,v.line=n,v.column=r,v.source=e,!t.silent)throw v}function l(g){var v=g.exec(e);if(v){var E=v[0];return i(E),e=e.slice(E.length),v}}function u(){l(IO)}function c(g){var v;for(g=g||[];v=f();)v!==!1&&g.push(v);return g}function f(){var g=o();if(!(Mv!=e.charAt(0)||Fv!=e.charAt(1))){for(var v=2;Hr!=e.charAt(v)&&(Fv!=e.charAt(v)||Mv!=e.charAt(v+1));)++v;if(v+=2,Hr===e.charAt(v-1))return s("End of comment missing");var E=e.slice(2,v-2);return r+=2,i(E),e=e.slice(v),r+=2,g({type:zO,comment:E})}}function d(){var g=o(),v=l(PO);if(v){if(f(),!l(AO))return s("property missing ':'");var E=l(LO),h=g({type:UO,property:jv(v[0].replace(Lv,Hr)),value:E?jv(E[0].replace(Lv,Hr)):Hr});return l(MO),h}}function y(){var g=[];c(g);for(var v;v=d();)v!==!1&&(g.push(v),c(g));return g}return u(),y()};function jv(e){return e?e.replace(FO,Hr):Hr}var BO=et&&et.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Mh,"__esModule",{value:!0});var QO=BO(VO);function $O(e,t){var n=null;if(!e||typeof e!="string")return n;var r=(0,QO.default)(e),i=typeof t=="function";return r.forEach(function(o){if(o.type==="declaration"){var a=o.property,s=o.value;i?t(a,s,o):s&&(n=n||{},n[a]=s)}}),n}Mh.default=$O;var hu={};Object.defineProperty(hu,"__esModule",{value:!0});hu.camelCase=void 0;var qO=/^--[a-zA-Z0-9-]+$/,HO=/-([a-z])/g,WO=/^[^-]+$/,GO=/^-(webkit|moz|ms|o|khtml)-/,YO=/^-(ms)-/,XO=function(e){return!e||WO.test(e)||qO.test(e)},KO=function(e,t){return t.toUpperCase()},zv=function(e,t){return"".concat(t,"-")},JO=function(e,t){return t===void 0&&(t={}),XO(e)?e:(e=e.toLowerCase(),t.reactCompat?e=e.replace(YO,zv):e=e.replace(GO,zv),e.replace(HO,KO))};hu.camelCase=JO;var ZO=et&&et.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},eN=ZO(Mh),tN=hu;function Hf(e,t){var n={};return!e||typeof e!="string"||(0,eN.default)(e,function(r,i){r&&i&&(n[(0,tN.camelCase)(r,t)]=i)}),n}Hf.default=Hf;var nN=Hf;(function(e){var t=et&&et.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(e,"__esModule",{value:!0}),e.returnFirstArg=e.canTextBeChildOfNode=e.ELEMENTS_WITH_NO_TEXT_CHILDREN=e.PRESERVE_CUSTOM_ATTRIBUTES=e.setStyleProp=e.isCustomComponent=void 0;var n=N,r=t(nN),i=new Set(["annotation-xml","color-profile","font-face","font-face-src","font-face-uri","font-face-format","font-face-name","missing-glyph"]);function o(c,f){return c.includes("-")?!i.has(c):!!(f&&typeof f.is=="string")}e.isCustomComponent=o;var a={reactCompat:!0};function s(c,f){if(typeof c=="string"){if(!c.trim()){f.style={};return}try{f.style=(0,r.default)(c,a)}catch{f.style={}}}}e.setStyleProp=s,e.PRESERVE_CUSTOM_ATTRIBUTES=Number(n.version.split(".")[0])>=16,e.ELEMENTS_WITH_NO_TEXT_CHILDREN=new Set(["tr","tbody","thead","tfoot","colgroup","table","head","html","frameset"]);var l=function(c){return!e.ELEMENTS_WITH_NO_TEXT_CHILDREN.has(c.name)};e.canTextBeChildOfNode=l;var u=function(c){return c};e.returnFirstArg=u})(Lh);Object.defineProperty(uu,"__esModule",{value:!0});var $o=gn,Uv=Lh,rN=["checked","value"],iN=["input","select","textarea"],oN={reset:!0,submit:!0};function aN(e,t){e===void 0&&(e={});var n={},r=!!(e.type&&oN[e.type]);for(var i in e){var o=e[i];if((0,$o.isCustomAttribute)(i)){n[i]=o;continue}var a=i.toLowerCase(),s=Vv(a);if(s){var l=(0,$o.getPropertyInfo)(s);switch(rN.includes(s)&&iN.includes(t)&&!r&&(s=Vv("default"+a)),n[s]=o,l&&l.type){case $o.BOOLEAN:n[s]=!0;break;case $o.OVERLOADED_BOOLEAN:o===""&&(n[s]=!0);break}continue}Uv.PRESERVE_CUSTOM_ATTRIBUTES&&(n[i]=o)}return(0,Uv.setStyleProp)(e.style,n),n}uu.default=aN;function Vv(e){return $o.possibleStandardNames[e]}var Fh={},sN=et&&et.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Fh,"__esModule",{value:!0});var pc=N,lN=sN(uu),sa=Lh,uN={cloneElement:pc.cloneElement,createElement:pc.createElement,isValidElement:pc.isValidElement};function EE(e,t){t===void 0&&(t={});for(var n=[],r=typeof t.replace=="function",i=t.transform||sa.returnFirstArg,o=t.library||uN,a=o.cloneElement,s=o.createElement,l=o.isValidElement,u=e.length,c=0;c<u;c++){var f=e[c];if(r){var d=t.replace(f,c);if(l(d)){u>1&&(d=a(d,{key:d.key||c})),n.push(i(d,f,c));continue}}if(f.type==="text"){var y=!f.data.trim().length;if(y&&f.parent&&!(0,sa.canTextBeChildOfNode)(f.parent)||t.trim&&y)continue;n.push(i(f.data,f,c));continue}var g=f,v={};cN(g)?((0,sa.setStyleProp)(g.attribs.style,g.attribs),v=g.attribs):g.attribs&&(v=(0,lN.default)(g.attribs,g.name));var E=void 0;switch(f.type){case"script":case"style":f.children[0]&&(v.dangerouslySetInnerHTML={__html:f.children[0].data});break;case"tag":f.name==="textarea"&&f.children[0]?v.defaultValue=f.children[0].data:f.children&&f.children.length&&(E=EE(f.children,t));break;default:continue}u>1&&(v.key=c),n.push(i(s(f.name,v,E),f,c))}return n.length===1?n[0]:n}Fh.default=EE;function cN(e){return sa.PRESERVE_CUSTOM_ATTRIBUTES&&e.type==="tag"&&(0,sa.isCustomComponent)(e.name,e.attribs)}(function(e){var t=et&&et.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(e,"__esModule",{value:!0}),e.htmlToDOM=e.domToReact=e.attributesToProps=e.Text=e.ProcessingInstruction=e.Element=e.Comment=void 0;var n=t(kh);e.htmlToDOM=n.default;var r=t(uu);e.attributesToProps=r.default;var i=t(Fh);e.domToReact=i.default;var o=Oh;Object.defineProperty(e,"Comment",{enumerable:!0,get:function(){return o.Comment}}),Object.defineProperty(e,"Element",{enumerable:!0,get:function(){return o.Element}}),Object.defineProperty(e,"ProcessingInstruction",{enumerable:!0,get:function(){return o.ProcessingInstruction}}),Object.defineProperty(e,"Text",{enumerable:!0,get:function(){return o.Text}});var a={lowerCaseAttributeNames:!1};function s(l,u){if(typeof l!="string")throw new TypeError("First argument must be a string");return l?(0,i.default)((0,n.default)(l,(u==null?void 0:u.htmlparser2)||a),u):[]}e.default=s})(eE);const Bv=Ma(eE),fN=Bv.default||Bv,jh=({product:e,className:t,isModalView:n=!1,itemSelectedAttributes:r=[]})=>{var f,d,y,g;const{addToCart:i,updateCartItemAttribute:o}=Fr(),[a,s]=N.useState(r),l=e.prices&&e.prices.length>0?`${e.prices[0].currency.symbol} ${(parseFloat((f=e.prices[0])==null?void 0:f.amount)*(e.quantity??1)).toFixed(2)}`:null,u=v=>{const E=a.findIndex(m=>m.attributeId===v.attribute_id),h=[...a];E!==-1?h[E]={id:v.id,attributeId:v.attribute_id,value:v.value}:h.push({id:v.id,attributeId:v.attribute_id,value:v.value}),s(h),n&&o(e,a,h)},c=v=>a.some(E=>v.attribute_id===E.attributeId&&v.value===E.value);return R.jsxs("div",{className:`${t}${e.inStock?"":" opacity-70"}`,children:[R.jsx("h2",{className:n?"capitalize font-light text-lg":"heading-h1",children:e.name}),n&&R.jsx("div",{className:"my-2 font-bold",children:l}),(d=e.attributes)==null?void 0:d.map(v=>R.jsxs("div",{className:"mt-4","data-testid":`${n?"cart-item":"product"}-attribute-${v.name.replace(/\s+/g,"-")}`,children:[R.jsxs("h3",{className:`${n?"font-sm":"font-bold uppercase"} capitalize mb-1`,children:[v.name,":"]}),R.jsx("div",{className:`${n?"gap-x-2":"gap-x-3"} flex flex-wrap gap-y-2`,children:v.items.map(E=>{var h,m;return((h=v.type)==null?void 0:h.toLowerCase())==="swatch"&&((m=v.name)==null?void 0:m.toLowerCase())==="color"?R.jsx("button",{type:"button",className:`relative ${n?"w-5 h-5":"w-8 h-8"} ${c(E)?"border-primary":"border-white"} border ${e.inStock?"hover:border-primary":""} transition-colors`,style:{backgroundColor:E.value},title:E.displayValue,onClick:()=>u(E),disabled:!e.inStock,"data-testid":`${n?"cart-item":"product"}-attribute-${v.name.replace(/\s+/g,"-")}-${E.displayValue.replace(/\s+/g,"-")}${c(E)?"-selected":""}`,children:R.jsx("div",{className:"absolute inset-0 border border-gray-200"})},E.id):R.jsx("button",{type:"button",className:`${n?"min:w-6 min:h-6 text-sm":"min:w-20 min:h-10"} ${c(E)?"bg-text text-white":"bg-white"} px-1 flex items-center justify-center transition-colors border ${e.inStock?"hover:bg-gray-800 hover:text-white":""} border-gray-800`,disabled:!e.inStock,onClick:()=>u(E),"data-testid":`${n?"cart-item":"product"}-attribute-${v.name.replace(/\s+/g,"-")}-${E.displayValue.replace(/\s+/g,"-")}${c(E)?"-selected":""}`,children:E.displayValue},E.id)})})]},v.id)),!n&&R.jsxs(R.Fragment,{children:[R.jsx("h3",{className:"mt-4 mb-1 font-bold uppercase font-roboto",children:"Price:"}),R.jsx("div",{className:"heading-h2",children:e.prices&&e.prices.length>0&&`${(y=e.prices[0])==null?void 0:y.currency.symbol} ${(g=e.prices[0])==null?void 0:g.amount}`})]}),!n&&e.inStock&&R.jsx("button",{type:"button",className:"w-full mb-8 btn-cta",onClick:()=>i(e,!0,a),disabled:e.attributes.length!==a.length,"data-testid":"add-to-cart",children:"Add to Cart"}),!n&&R.jsx("div",{className:"text-sm font-roboto","data-testid":"product-description",children:fN(aO.sanitize(e.description))})]})};jh.propTypes={product:ne.object.isRequired,className:ne.string,isModalView:ne.bool,itemSelectedAttributes:ne.array};class wE extends N.Component{constructor(t){super(t),this.state={currentIndex:0,mainImageHeight:null},this.handleNext=this.handleNext.bind(this),this.handlePrev=this.handlePrev.bind(this),this.handleMainImageLoad=this.handleMainImageLoad.bind(this)}handleNext(){this.setState(t=>({currentIndex:(t.currentIndex+1)%this.props.images.length}))}handlePrev(){this.setState(t=>({currentIndex:(t.currentIndex-1+this.props.images.length)%this.props.images.length}))}handleMainImageLoad(t){const{clientHeight:n}=t.target,i=window.innerHeight*.6,o=Math.min(n,i);this.setState({mainImageHeight:o})}render(){const{images:t=[],alt:n="Product"}=this.props,{currentIndex:r,mainImageHeight:i}=this.state;return R.jsx("section",{className:"mb-6 md:w-2/3 md:mb-0","data-testid":"product-gallery",children:!!(t!=null&&t.length)&&R.jsxs("div",{className:"relative flex",children:[R.jsx("div",{className:"w-1/5 max-h-screen overflow-y-auto",style:{maxHeight:i},children:t.map((o,a)=>R.jsx("img",{src:o,alt:n,className:`w-full ${a===r?"opacity-50":"opacity-100"} cursor-pointer`,onClick:()=>this.setState({currentIndex:a})},a))}),R.jsxs("div",{className:"relative w-4/5",children:[R.jsx("img",{src:t[r],alt:n,className:"object-contain w-full h-auto",onLoad:this.handleMainImageLoad,style:{maxHeight:i||"100vh"}}),R.jsx("button",{className:"absolute p-2 text-gray-300 transition-colors duration-300 transform -translate-y-1/2 bg-text top-1/2 left-4 hover:text-white",onClick:this.handlePrev,children:R.jsx(zf,{direction:"left"})}),R.jsx("button",{className:"absolute p-2 text-gray-300 transition-colors duration-300 transform -translate-y-1/2 bg-text top-1/2 right-4 hover:text-white",onClick:this.handleNext,children:R.jsx(zf,{})})]})]})})}}wE.propTypes={images:ne.array,alt:ne.string};class Wf extends N.Component{render(){const{text:t,onClick:n,data:r="",type:i="button"}=this.props;return R.jsx("button",{type:i,className:"flex items-center justify-center w-6 h-6 transition-colors border border-text hover:bg-text hover:text-white",onClick:n,"data-testid":r,children:t})}}Wf.propTypes={text:ne.string.isRequired,type:ne.string,onClick:ne.func,data:ne.string};function SE({item:e={}}){var r;const{updateCartItemQuantity:t}=Fr(),n=(r=e.product.gallery)!=null&&r.length?e.product.gallery[0]:"";return R.jsxs("div",{className:"flex justify-between",children:[R.jsx(jh,{className:"w-3/6",isModalView:!0,product:e.product,itemSelectedAttributes:e.selectedAttributes}),R.jsxs("div",{className:"flex flex-col items-center justify-between w-1/6",children:[R.jsx(Wf,{text:"+",onClick:()=>t(e.id,1),data:"cart-item-amount-increase"}),R.jsx("span",{children:e.quantity}),R.jsx(Wf,{text:"-",onClick:()=>t(e.id,-1),data:"cart-item-amount-decrease"})]}),R.jsx("div",{className:"w-2/6",children:R.jsx("img",{src:n,alt:e.product.name,className:"object-contain w-full h-full"})})]})}SE.propTypes={item:ne.object};const dN=On`
  mutation PlaceOrder($orderInput: OrderInput!) {
    placeOrder(OrderInput: $orderInput)
  }
`;function _E({className:e}){const[t,{loading:n}]=vC(dN),{emptyCart:r}=Fr(),i=async()=>{const o=JSON.parse(localStorage.getItem("cartItems"))||[];if(!o.length)return pe.error("Cart is empty! 🛒");const a={items:o.map(s=>({productId:s.product.id,quantity:s.quantity,attributeValues:s.selectedAttributes.map(l=>({id:l.id,value:l.value}))}))};try{const{data:s}=await t({variables:{orderInput:a}});r(),pe.success(s.placeOrder)}catch(s){if(s.graphQLErrors&&s.graphQLErrors.length>0){const l=s.graphQLErrors[0].message;return pe.error(`Error placing order: ${l}`)}if(s.networkError&&s.networkError.result&&s.networkError.result.error){const l=s.networkError.result.error;return pe.error(`Error placing order: ${l}`)}pe.error("Error placing order. Please try again later.")}};return R.jsxs("button",{type:"button",className:`btn-cta flex items-center justify-center disabled:opacity-70${e?" "+e:""}`,onClick:i,disabled:n,children:[n&&R.jsx(xh,{className:"w-4 h-4 mr-2"}),"Place Order"]})}_E.propTypes={className:ne.string};function TE({cartItems:e=[]}){var r;const t=e.reduce((i,o)=>{var a,s;return i+parseFloat((s=(a=o.product)==null?void 0:a.prices[0])==null?void 0:s.amount)*o.quantity},0).toFixed(2),n=e.reduce((i,o)=>i+o.quantity,0);return R.jsxs("section",{className:"absolute z-50 bg-white shadow-lg -right-3.5 top-full w-80 py-6 px-4 max-h-[calc(100vh-4rem)] overflow-y-auto",children:[R.jsxs("h2",{className:"mb-6",children:[R.jsx("span",{className:"font-bold",children:"My Bag"}),!!n&&`, ${n} item${n===1?"":"s"}`]}),n===0?R.jsx("p",{className:"mt-2 text-gray-500",children:"Your bag is empty."}):R.jsxs(R.Fragment,{children:[R.jsx("div",{className:"py-4 space-y-8 overflow-y-auto max-h-80",children:e.map(i=>R.jsx(SE,{item:i},i.id))}),R.jsxs("div",{className:"pt-4 mt-4",children:[R.jsxs("div",{className:"flex items-center justify-between",children:[R.jsx("h3",{className:"font-semibold font-roboto",children:"Total"}),R.jsx("div",{className:"font-bold","data-testid":"cart-total",children:`${(r=e[0])==null?void 0:r.product.prices[0].currency.symbol} ${t}`})]}),R.jsx(_E,{className:"w-full mt-8"})]})]})]})}TE.propTypes={cartItems:ne.arrayOf(ne.object)};class xE extends N.Component{componentDidMount(){document.body.classList.add("overflow-hidden")}componentWillUnmount(){document.body.classList.remove("overflow-hidden")}render(){return R.jsxs("div",{role:"status",className:"fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full overflow-hidden bg-white",children:[R.jsx(xh,{}),R.jsx("span",{className:"sr-only",children:"Loading..."})]})}}const hN="/assets/error-DVtz2VXm.svg";let zh=class extends N.Component{render(){const{statusCode:t=500,message:n}=this.props;return R.jsxs("main",{className:"flex flex-col items-center justify-center mt-6 max:h-screen",children:[R.jsx("img",{src:hN,alt:"error",className:"w-64 mb-6"}),n?R.jsx("h1",{className:"heading-h1",children:n}):R.jsx("h1",{className:"heading-h1",children:t===404?"Page not found":t===400?"Bad request":"Something went wrong"}),R.jsx(Hl,{to:"/",className:"btn-cta",children:"Back home"})]})}};zh.propTypes={message:ne.string,statusCode:ne.number};class pN extends N.Component{render(){return R.jsxs(R.Fragment,{children:[R.jsx(zC,{}),R.jsx(dT,{})]})}}function mN(e){function t(n){const r=g0();return R.jsx(e,{...n,error:r})}return t}class bE extends N.Component{render(){const{error:t}=this.props;return R.jsx(zh,{statusCode:t==null?void 0:t.status})}}bE.propTypes={error:ne.object};const kE=mN(bE);function Qv(){const{selectedCategory:e,productsData:t}=Fr();return R.jsxs("main",{className:"mt-14",children:[R.jsx("h1",{className:"heading-h1 !mb-12 !uppercase",children:e}),!!t.length&&R.jsx("section",{className:"grid grid-cols-auto-fill-350 gap-x-4 gap-y-8",children:t.map(n=>R.jsx(G1,{product:n},n.id))})]})}function vN(){const{id:e}=p0(),{data:t,loading:n,error:r}=dC(FC,{variables:{id:e}});if(r)return r.networkError?R.jsx(zh,{statusCode:r.networkError.statusCode,message:"Product not found"}):R.jsx(kE,{});if(n)return R.jsx(xE,{});const{product:i}=t;return R.jsxs("main",{className:"flex flex-col items-start mt-14 md:flex-row","data-testid":`product-${i.name.replace(/\s+/g,"-").toLowerCase()}`,children:[R.jsx(wE,{images:i.gallery,alt:i.name}),R.jsx(jh,{className:"md:w-1/3 md:pl-4",product:i})]})}const yN=wT([{path:"/",element:R.jsx(pN,{}),errorElement:R.jsx(kE,{}),children:[{index:!0,element:R.jsx(Qv,{})},{path:"/:category",element:R.jsx(Qv,{})},{path:"products/:id",element:R.jsx(vN,{})}]}]);class gN extends N.Component{render(){return R.jsxs(H1,{children:[R.jsx(EN,{}),R.jsx(NT,{router:yN})]})}}function EN(){const{setCategoriesData:e,setProductsData:t}=Fr(),[n,{loading:r,error:i}]=M1(jC,{onCompleted:o=>{e(o.categories),t(o.products)}});return N.useEffect(()=>{const o=new URLSearchParams(location.search).get("category");n({variables:{category:o}})},[n]),i?R.jsx("p",{className:"my-8 font-semibold text-center text-red-500",children:"Something went wrong"}):r?R.jsx(xE,{}):null}const wN="/graphql",SN=new O1({uri:wN,cache:new x1});mc.createRoot(document.getElementById("root")).render(R.jsx(xe.StrictMode,{children:R.jsxs(oC,{client:SN,children:[R.jsx(gN,{}),R.jsx(LC,{position:"bottom-left",transition:PC,autoClose:2e3,hideProgressBar:!0})]})}))});export default _N();
