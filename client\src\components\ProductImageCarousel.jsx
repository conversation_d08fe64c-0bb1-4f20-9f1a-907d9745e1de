//importing necessary libraries and components
import { Component } from 'react';
import PropTypes from 'prop-types';
import { Arrow } from './';

//creating a class component for the ProductImageCarousel
class ProductImageCarousel extends Component {
  //creating a constructor for the component
  constructor(props) {
    super(props);
    //initializing state with currentIndex and mainImageHeight
    this.state = {
      currentIndex: 0,//initializing currentIndex to 0
      mainImageHeight: null,//initializing mainImageHeight to null
    };

    //binding functions to the component instance
    this.handleNext = this.handleNext.bind(this);
    this.handlePrev = this.handlePrev.bind(this);
    this.handleMainImageLoad = this.handleMainImageLoad.bind(this);
  }

  //creating functions to handle navigation
  handleNext() {
    this.setState((prevState) => ({
      currentIndex: (prevState.currentIndex + 1) % this.props.images.length,//using modulo to ensure the index wraps around when it reaches the end of the array.
    }));
  }

  handlePrev() {
    this.setState((prevState) => ({
      currentIndex:
        (prevState.currentIndex - 1 + this.props.images.length) %
        this.props.images.length,//using modulo to ensure the index wraps around when it reaches the end of the array.
    }));
  }

  //creating a function to handle image loading
  handleMainImageLoad(e) {
    const { clientHeight } = e.target;
    const maxHeightRatio = 0.6;
    const maxHeight = window.innerHeight * maxHeightRatio;
    const mainImageHeight = Math.min(clientHeight, maxHeight);//calculate maximum height for image based on screen size and ratio and select smaller one between calculated and actual height.

    this.setState({ mainImageHeight });//update state with new height value
  }

  //creating a render function to display the component
  render() {
    const { images = [], alt = 'Product' } = this.props;
    const { currentIndex, mainImageHeight } = this.state;

    return (
      <section className="mb-6 md:w-2/3 md:mb-0" data-testid="product-gallery">
        {!!images?.length && (
          <div className="relative flex">
            <div
              className="w-1/5 max-h-screen overflow-y-auto"
              style={{ maxHeight: mainImageHeight }}
            >
              {images.map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={alt}
                  className={`w-full ${
                    index === currentIndex ? 'opacity-50' : 'opacity-100'
                  } cursor-pointer`}
                  onClick={() => this.setState({ currentIndex: index })}
                />
              ))}
            </div>
            <div className="relative w-4/5">
              <img
                src={images[currentIndex]}
                alt={alt}
                className="object-contain w-full h-auto"
                onLoad={this.handleMainImageLoad}
                style={{ maxHeight: mainImageHeight || '100vh' }}
              />

              <button
                className="absolute p-2 text-gray-300 transition-colors duration-300 transform -translate-y-1/2 bg-text top-1/2 left-4 hover:text-white"
                onClick={this.handlePrev}
              >
                <Arrow direction="left" />
              </button>
              <button
                className="absolute p-2 text-gray-300 transition-colors duration-300 transform -translate-y-1/2 bg-text top-1/2 right-4 hover:text-white"
                onClick={this.handleNext}
              >
                <Arrow />
              </button>
            </div>
          </div>
        )}
      </section>
    );
  }
}

ProductImageCarousel.propTypes = {
  images: PropTypes.array,
  alt: PropTypes.string,
};

export default ProductImageCarousel;
